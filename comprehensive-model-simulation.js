const fs = require('fs');
const path = require('path');

// Load environment and services
console.log('🔧 Loading environment and services...');
require('dotenv').config();

let apiService;
try {
    apiService = require('./src/services/apiService');
    console.log('✅ API Service loaded successfully');
} catch (error) {
    console.error('❌ Failed to load API service:', error.message);
    console.error('Full error:', error);
    process.exit(1);
}

// Comprehensive Model Performance Simulation
async function runComprehensiveModelSimulation() {
    console.log('🚀 Starting Comprehensive Model Performance Simulation...');
    console.log('📊 This will test all 16 models with multiple simulations each');
    console.log('⏱️  Each test includes 5-second delays to avoid rate limits');
    console.log('🎯 Focus: No thinking tags + Best performance\n');
    
    const testModels = [
        'deepseek/deepseek-r1:free',
        'tngtech/deepseek-r1t-chimera:free', 
        'qwen/qwen3-32b:free',
        'mistralai/mistral-nemo:free',
        'google/gemma-3-27b-it:free',
        'meta-llama/llama-4-maverick:free',
        'qwen/qwq-32b:free',
        'qwen/qwen3-235b-a22b:free',
        'microsoft/mai-ds-r1:free',
        'moonshotai/kimi-dev-72b:free',
        'thudm/glm-z1-32b:free',
        'deepseek/deepseek-r1-distill-llama-70b:free',
        'mistralai/mistral-7b-instruct:free',
        'nvidia/llama-3.3-nemotron-super-49b-v1:free',
        'google/gemini-2.0-flash-exp:free',
        'mistralai/devstral-small:free'
    ];

    const testContent = `
    The human heart is a vital organ that pumps blood throughout the body. It consists of four chambers: two atria (upper chambers) and two ventricles (lower chambers). The right atrium receives deoxygenated blood from the body, which then flows to the right ventricle. From the right ventricle, blood is pumped to the lungs for oxygenation. Once oxygenated, blood returns to the left atrium and then flows to the left ventricle, which pumps it back to the body.
    `;

    const simulationResults = [];
    const DELAY_BETWEEN_TESTS = 5000; // 5 seconds delay
    const SIMULATIONS_PER_MODEL = 3; // Run 3 simulations per model

    for (let modelIndex = 0; modelIndex < testModels.length; modelIndex++) {
        const modelId = testModels[modelIndex];
        console.log(`\n📊 Testing Model ${modelIndex + 1}/${testModels.length}: ${modelId}`);
        
        const modelResults = {
            modelId,
            simulations: [],
            averageResponseTime: 0,
            successRate: 0,
            hasThinkingTags: false,
            jsonParseSuccess: 0,
            qualityScore: 0,
            overallRating: 'Unknown'
        };

        for (let sim = 1; sim <= SIMULATIONS_PER_MODEL; sim++) {
            console.log(`  🔄 Simulation ${sim}/${SIMULATIONS_PER_MODEL} for ${modelId}`);
            
            try {
                const startTime = Date.now();
                
                // Use the actual API service to generate questions
                const response = await apiService.generateTrueFalseQuestions(
                    testContent,
                    3, // Generate 3 questions
                    'desktop-user',
                    modelId
                );
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                // Analyze the response
                const analysis = analyzeModelResponse(response, modelId);
                
                const simulationResult = {
                    simulationNumber: sim,
                    responseTime,
                    success: analysis.success,
                    hasThinking: analysis.hasThinking,
                    jsonParsed: analysis.jsonParsed,
                    questionCount: analysis.questionCount,
                    qualityScore: analysis.qualityScore,
                    errors: analysis.errors,
                    rawResponsePreview: JSON.stringify(response).substring(0, 200) + '...'
                };
                
                modelResults.simulations.push(simulationResult);
                
                console.log(`    ✅ Response Time: ${responseTime}ms`);
                console.log(`    📝 Questions Generated: ${analysis.questionCount}`);
                console.log(`    🧠 Has Thinking Tags: ${analysis.hasThinking ? '❌ YES' : '✅ NO'}`);
                console.log(`    📋 JSON Parsed: ${analysis.jsonParsed ? '✅ YES' : '❌ NO'}`);
                console.log(`    ⭐ Quality Score: ${analysis.qualityScore}/10`);
                
            } catch (error) {
                console.log(`    ❌ Simulation ${sim} failed: ${error.message}`);
                modelResults.simulations.push({
                    simulationNumber: sim,
                    responseTime: 0,
                    success: false,
                    hasThinking: false,
                    jsonParsed: false,
                    questionCount: 0,
                    qualityScore: 0,
                    errors: [error.message],
                    rawResponsePreview: 'ERROR'
                });
            }
            
            // Delay between simulations
            if (sim < SIMULATIONS_PER_MODEL) {
                console.log(`    ⏳ Waiting ${DELAY_BETWEEN_TESTS/1000}s before next simulation...`);
                await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_TESTS));
            }
        }
        
        // Calculate model statistics
        calculateModelStatistics(modelResults);
        simulationResults.push(modelResults);
        
        // Delay between models
        if (modelIndex < testModels.length - 1) {
            console.log(`\n⏳ Waiting ${DELAY_BETWEEN_TESTS/1000}s before testing next model...`);
            await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_TESTS));
        }
    }
    
    // Generate comprehensive report
    generateSimulationReport(simulationResults);
    
    return simulationResults;
}

// Analyze model response for thinking tags and performance
function analyzeModelResponse(response, modelId) {
    const analysis = {
        success: false,
        hasThinking: false,
        jsonParsed: false,
        questionCount: 0,
        qualityScore: 0,
        errors: []
    };

    try {
        // Check if response is successful
        if (response && response.questions && Array.isArray(response.questions)) {
            analysis.success = true;
            analysis.jsonParsed = true;
            analysis.questionCount = response.questions.length;
            
            // Check for thinking tags in the raw response
            const responseStr = JSON.stringify(response);
            const thinkingPatterns = [
                'ΓùüthinkΓû╖',
                '<think>',
                '</think>',
                'thinking:',
                'reasoning:',
                'analysis:'
            ];
            
            analysis.hasThinking = thinkingPatterns.some(pattern => 
                responseStr.toLowerCase().includes(pattern.toLowerCase())
            );
            
            // Calculate quality score based on multiple factors
            let qualityScore = 0;
            
            // Base score for successful generation
            if (analysis.questionCount >= 3) qualityScore += 3;
            
            // Bonus for no thinking tags
            if (!analysis.hasThinking) qualityScore += 3;
            
            // Bonus for proper JSON structure
            if (analysis.jsonParsed) qualityScore += 2;
            
            // Check question quality
            const hasExplanations = response.questions.some(q => 
                q.answer === false && q.explanation && q.explanation.length > 10
            );
            if (hasExplanations) qualityScore += 2;
            
            analysis.qualityScore = qualityScore;
            
        } else {
            analysis.errors.push('Invalid response structure');
        }
        
    } catch (error) {
        analysis.errors.push(`Analysis error: ${error.message}`);
    }
    
    return analysis;
}

// Calculate statistics for a model across all simulations
function calculateModelStatistics(modelResults) {
    const simulations = modelResults.simulations;
    const successfulSims = simulations.filter(s => s.success);
    
    // Average response time (only successful)
    if (successfulSims.length > 0) {
        modelResults.averageResponseTime = Math.round(
            successfulSims.reduce((sum, s) => sum + s.responseTime, 0) / successfulSims.length
        );
    }
    
    // Success rate
    modelResults.successRate = Math.round((successfulSims.length / simulations.length) * 100);
    
    // Check if any simulation had thinking tags
    modelResults.hasThinkingTags = simulations.some(s => s.hasThinking);
    
    // JSON parse success rate
    const jsonSuccessful = simulations.filter(s => s.jsonParsed).length;
    modelResults.jsonParseSuccess = Math.round((jsonSuccessful / simulations.length) * 100);
    
    // Average quality score
    if (successfulSims.length > 0) {
        modelResults.qualityScore = Math.round(
            successfulSims.reduce((sum, s) => sum + s.qualityScore, 0) / successfulSims.length * 10
        ) / 10;
    }
    
    // Overall rating
    if (modelResults.successRate >= 90 && !modelResults.hasThinkingTags && modelResults.jsonParseSuccess >= 90) {
        modelResults.overallRating = '🏆 EXCELLENT';
    } else if (modelResults.successRate >= 70 && !modelResults.hasThinkingTags) {
        modelResults.overallRating = '⭐ GOOD';
    } else if (modelResults.successRate >= 50) {
        modelResults.overallRating = '⚠️ AVERAGE';
    } else {
        modelResults.overallRating = '❌ POOR';
    }
}

// Generate comprehensive simulation report
function generateSimulationReport(results) {
    console.log('\n' + '='.repeat(80));
    console.log('📊 COMPREHENSIVE MODEL SIMULATION REPORT');
    console.log('='.repeat(80));
    
    // Sort by overall performance
    const sortedResults = results.sort((a, b) => {
        // Prioritize models without thinking tags
        if (a.hasThinkingTags !== b.hasThinkingTags) {
            return a.hasThinkingTags ? 1 : -1;
        }
        // Then by success rate
        if (a.successRate !== b.successRate) {
            return b.successRate - a.successRate;
        }
        // Then by response time (faster is better)
        return a.averageResponseTime - b.averageResponseTime;
    });
    
    console.log('\n🏆 TOP PERFORMERS (No Thinking Tags + High Performance):');
    console.log('-'.repeat(60));
    
    const topPerformers = sortedResults.filter(r => !r.hasThinkingTags && r.successRate >= 80);
    
    topPerformers.forEach((result, index) => {
        console.log(`${index + 1}. ${result.modelId}`);
        console.log(`   Rating: ${result.overallRating}`);
        console.log(`   Success Rate: ${result.successRate}%`);
        console.log(`   Avg Response Time: ${result.averageResponseTime}ms`);
        console.log(`   JSON Parse Success: ${result.jsonParseSuccess}%`);
        console.log(`   Quality Score: ${result.qualityScore}/10`);
        console.log(`   Has Thinking Tags: ${result.hasThinkingTags ? '❌ YES' : '✅ NO'}`);
        console.log('');
    });
    
    console.log('\n⚠️ MODELS WITH THINKING TAGS (Avoid These):');
    console.log('-'.repeat(60));
    
    const thinkingModels = sortedResults.filter(r => r.hasThinkingTags);
    thinkingModels.forEach(result => {
        console.log(`❌ ${result.modelId} - ${result.overallRating}`);
    });
    
    console.log('\n❌ FAILED/UNRELIABLE MODELS:');
    console.log('-'.repeat(60));
    
    const failedModels = sortedResults.filter(r => r.successRate < 50);
    failedModels.forEach(result => {
        console.log(`❌ ${result.modelId} - Success Rate: ${result.successRate}%`);
    });
    
    // Save detailed report to file
    const reportData = {
        timestamp: new Date().toISOString(),
        summary: {
            totalModels: results.length,
            topPerformers: topPerformers.length,
            modelsWithThinking: thinkingModels.length,
            failedModels: failedModels.length
        },
        detailedResults: sortedResults
    };
    
    fs.writeFileSync('simulation-report.json', JSON.stringify(reportData, null, 2));
    console.log('\n💾 Detailed report saved to: simulation-report.json');
    
    console.log('\n🎯 FINAL RECOMMENDATIONS:');
    console.log('-'.repeat(60));
    if (topPerformers.length > 0) {
        console.log(`🥇 Best Overall: ${topPerformers[0].modelId}`);
        console.log(`🥈 Second Choice: ${topPerformers[1]?.modelId || 'N/A'}`);
        console.log(`🥉 Third Choice: ${topPerformers[2]?.modelId || 'N/A'}`);
    } else {
        console.log('❌ No models met the criteria for top performance');
    }
    
    console.log('\n' + '='.repeat(80));
}

// Run the simulation
if (require.main === module) {
    runComprehensiveModelSimulation()
        .then(() => {
            console.log('✅ Simulation completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Simulation failed:', error);
            process.exit(1);
        });
}

module.exports = { runComprehensiveModelSimulation };
