// Test script for radiation physics contradiction detection
const apiService = require('./src/services/apiService');

// Test with problematic radiation physics questions
const radiationPhysicsIssues = [
  {
    question: "The principle of radiation protection does not include the concept of shielding.",
    answer: true, // Should be FALSE
    explanation: "The text explicitly mentions shielding as one of the key principles of radiation protection, alongside time and distance."
  },
  {
    question: "The effective dose is calculated by summing the weighted absorbed doses to all tissues and organs.",
    answer: true, // Should be FALSE
    explanation: "The text mentions that effective dose is the sum of weighted equivalent doses (HT), not absorbed doses, to all tissues and organs."
  },
  {
    question: "The unit Becquerel (Bq) is larger than the unit Curie (Ci).",
    answer: true, // Should be FALSE
    explanation: "The text states that one Curie equals 2.2 x 10¹² disintegrations per minute, making it a larger unit compared to the Becquerel, which equals 60 dpm."
  },
  {
    question: "Absorbed dose is not measured in units of gray (Gy) or rad.",
    answer: false, // Should stay FALSE (correct)
    explanation: "False. The text specifies that absorbed dose is measured in gray (Gy) and sometimes in rad, especially in the USA."
  }
];

console.log('🔍 Testing Radiation Physics Contradiction Detection');
console.log('==================================================');

console.log('\n❌ Problematic Questions Analysis:');
radiationPhysicsIssues.forEach((q, index) => {
  console.log(`${index + 1}. ${q.question}`);
  console.log(`   Current Answer: ${q.answer}`);
  console.log(`   Key Issue: ${q.explanation.substring(0, 80)}...`);
  
  // Analyze what should trigger detection
  const hasDoesNotInclude = q.question.toLowerCase().includes('does not include');
  const hasIsLarger = q.question.toLowerCase().includes('is larger');
  const hasAbsorbedDoses = q.question.toLowerCase().includes('absorbed doses');
  
  const explanationHasKeyPrinciple = q.explanation.toLowerCase().includes('key principle');
  const explanationHasNotAbsorbed = q.explanation.toLowerCase().includes('not absorbed doses');
  const explanationHasLargerUnit = q.explanation.toLowerCase().includes('larger unit');
  
  console.log(`   Triggers: doesNotInclude=${hasDoesNotInclude}, isLarger=${hasIsLarger}, absorbedDoses=${hasAbsorbedDoses}`);
  console.log(`   Explanation flags: keyPrinciple=${explanationHasKeyPrinciple}, notAbsorbed=${explanationHasNotAbsorbed}, largerUnit=${explanationHasLargerUnit}`);
  console.log('');
});

console.log('🔧 Running Radiation Physics Logic Validation...');

// Test each question individually
const results = [];
radiationPhysicsIssues.forEach((q, index) => {
  console.log(`\n🔍 Testing Question ${index + 1}:`);
  const result = apiService.validateTrueFalseLogic({ ...q }, index + 1);
  const wasChanged = result.answer !== q.answer;
  results.push(result);
  console.log(`   Original: ${q.answer} → Result: ${result.answer} ${wasChanged ? '(CHANGED ✅)' : '(SAME ❌)'}`);
});

console.log('\n🎯 Running Full System Test...');
const balancedResult = apiService.forceBalanceTrueFalseQuestions(radiationPhysicsIssues, 4);

console.log('\n✅ Final Results Summary:');
const corrections = results.filter((r, i) => r.answer !== radiationPhysicsIssues[i].answer).length;

console.log('📊 Radiation Physics Contradiction Detection Results:');
console.log(`   - Total questions tested: ${radiationPhysicsIssues.length}`);
console.log(`   - Questions corrected: ${corrections}/${radiationPhysicsIssues.length}`);

console.log('\n🔍 Individual Results:');
results.forEach((r, i) => {
  const originalAnswer = radiationPhysicsIssues[i].answer;
  const expectedAnswer = i === 3 ? false : false; // Q1-Q3 should be False, Q4 should stay False
  const isCorrect = r.answer === expectedAnswer;
  
  console.log(`   Q${i + 1}: ${isCorrect ? 'CORRECT ✅' : 'INCORRECT ❌'} (${originalAnswer} → ${r.answer})`);
});

const allCorrect = results.every((r, i) => r.answer === false);

if (allCorrect) {
  console.log('\n🎉 PERFECT SUCCESS: All radiation physics contradictions detected and fixed!');
  console.log('The comprehensive logic validation system now works perfectly for:');
  console.log('✅ "Does not include X" vs "X is key principle" patterns');
  console.log('✅ "Sum of absorbed doses" vs "not absorbed doses" patterns');
  console.log('✅ "X is larger than Y" vs "Y is larger unit" patterns');
  console.log('✅ Complete radiation physics education accuracy achieved!');
} else {
  console.log('\n⚠️ ISSUES FOUND: Some radiation physics contradictions still not caught.');
  console.log('Additional patterns may be needed for physics terminology.');
}
