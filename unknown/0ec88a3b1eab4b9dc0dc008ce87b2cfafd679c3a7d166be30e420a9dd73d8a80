// Test script for massive Microsoft Word contradiction detection
const apiService = require('./src/services/apiService');

// Test with all problematic Microsoft Word questions that have wrong answers
const massiveWordContradictions = [
  {
    question: "The Zoom Control in Word 2010 only allows zooming in, not zooming out.",
    answer: true, // Should be FALSE
    explanation: "The text explains that the Zoom Control lets you zoom in or out using a slider or buttons."
  },
  {
    question: "The Status Bar in Word 2010 displays the total number of words but not the number of pages.",
    answer: true, // Should be FALSE
    explanation: "The text states that the Status Bar contains both the total number of pages and words in the document."
  },
  {
    question: "The Backstage view in Word 2010 is used to format text and paragraphs.",
    answer: true, // Should be FALSE
    explanation: "The text explains that the Backstage view is for file-related operations, not text formatting."
  },
  {
    question: "Word 2010 documents can only be saved with the .doc file extension.",
    answer: true, // Should be FALSE
    explanation: "The text mentions that Word documents can have either .doc or .docx extensions."
  },
  {
    question: "The Maximize/Restore button in Word 2010 minimizes the program to the taskbar.",
    answer: true, // Should be FALSE
    explanation: "The text states that the Minimize button sends the program to the taskbar, not the Maximize/Restore button."
  },
  {
    question: "To create a table in Word 2010, you must first convert existing text into a table.",
    answer: true, // Should be FALSE
    explanation: "The text explains that you can create a blank table or convert existing text into a table."
  },
  {
    question: "Headers in Word 2010 appear at the bottom of every page, while footers appear at the top.",
    answer: true, // Should be FALSE
    explanation: "The text states that headers appear at the top of every page and footers at the bottom."
  },
  {
    question: "The Draft view in Word 2010 displays pictures and text arrangement as they will appear when printed.",
    answer: true, // Should be FALSE
    explanation: "The text explains that Draft view does not show pictures or text arrangement as they will appear when printed."
  },
  {
    question: "To save a document in Word 2010, you must use the Save option from the File tab only.",
    answer: true, // Should be FALSE
    explanation: "The text mentions multiple ways to save a document, including using the floppy icon or keyboard shortcuts."
  }
];

console.log('🔍 Testing Massive Microsoft Word Contradiction Detection');
console.log('======================================================');

console.log('\n❌ All Problematic Questions Analysis:');
massiveWordContradictions.forEach((q, index) => {
  console.log(`${index + 1}. ${q.question.substring(0, 70)}...`);
  console.log(`   Current Answer: ${q.answer}`);
  console.log(`   Key Issue: ${q.explanation.substring(0, 80)}...`);
  console.log('');
});

console.log('🔧 Running Comprehensive Logic Validation...');

// Test each question individually
const results = [];
massiveWordContradictions.forEach((q, index) => {
  console.log(`\n🔍 Testing Question ${index + 1}:`);
  const result = apiService.validateTrueFalseLogic({ ...q }, index + 1);
  const wasChanged = result.answer !== q.answer;
  results.push(result);
  console.log(`   Original: ${q.answer} → Result: ${result.answer} ${wasChanged ? '(CHANGED ✅)' : '(SAME ❌)'}`);
});

console.log('\n🎯 Running Full System Test...');
const balancedResult = apiService.forceBalanceTrueFalseQuestions(massiveWordContradictions, 9);

console.log('\n✅ Final Results Summary:');
const corrections = results.filter((r, i) => r.answer !== massiveWordContradictions[i].answer).length;
const allCorrect = results.every(r => r.answer === false);

console.log('📊 Massive Microsoft Word Contradiction Detection Results:');
console.log(`   - Total questions tested: ${massiveWordContradictions.length}`);
console.log(`   - Questions corrected: ${corrections}/${massiveWordContradictions.length}`);
console.log(`   - All should be False: ${allCorrect ? 'YES ✅' : 'NO ❌'}`);

console.log('\n🔍 Individual Results:');
results.forEach((r, i) => {
  const wasFixed = r.answer === false;
  console.log(`   Q${i + 1}: ${wasFixed ? 'FIXED ✅' : 'NOT FIXED ❌'} (${massiveWordContradictions[i].answer} → ${r.answer})`);
});

if (allCorrect) {
  console.log('\n🎉 PERFECT SUCCESS: All Microsoft Word contradictions detected and fixed!');
  console.log('The comprehensive logic validation system is working flawlessly across:');
  console.log('✅ "Only X" vs "X or Y" patterns');
  console.log('✅ "Not Y" vs "both X and Y" patterns');
  console.log('✅ "Used for X" vs "not for X" patterns');
  console.log('✅ "Only extension X" vs "either X or Y" patterns');
  console.log('✅ "Button X does Y" vs "not button X" patterns');
  console.log('✅ "Must do X" vs "can do X or Y" patterns');
  console.log('✅ "X at bottom, Y at top" vs "X at top, Y at bottom" patterns');
  console.log('✅ "Shows X" vs "does not show X" patterns');
  console.log('✅ "Must use only X" vs "multiple ways" patterns');
  console.log('✅ Complete technology education accuracy achieved!');
} else {
  console.log('\n⚠️ ISSUES FOUND: Some contradictions still not caught.');
  console.log('The system needs further enhancement for these specific patterns.');
}
