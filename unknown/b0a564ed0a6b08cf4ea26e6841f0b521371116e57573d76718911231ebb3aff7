// Test script for ACE inhibitor/ARB contradiction detection
const apiService = require('./src/services/apiService');

// Test with problematic ACE/ARB questions that have wrong answers
const problematicACEARBQuestions = [
  {
    question: "ARBs do not block bradykinin breakdown.",
    answer: false, // This should be TRUE
    explanation: "False. The text mentions that ARBs do not block bradykinin breakdown, unlike ACE inhibitors."
  },
  {
    question: "ACE inhibitors are used in patients with a history of ARB-induced angioedema.",
    answer: true, // This should be FALSE
    explanation: "The text states that an ARB can generally be used in patients with a history of ACE inhibitor-induced angioedema, not the other way around."
  },
  {
    question: "ACE inhibitors are used to treat chronic hypertension in pregnancy.",
    answer: true, // This should be FALSE
    explanation: "The text states that ACE inhibitors are contraindicated in pregnancy."
  },
  {
    question: "ARBs are used to treat acute hypertension in pregnancy.",
    answer: true, // This should be FALSE
    explanation: "The text states that IV hydralazine or IV labetalol is used to treat acute hypertension in pregnancy, not ARBs."
  },
  {
    question: "ACE inhibitors block the conversion of angiotensin I to angiotensin II.",
    answer: true, // This should stay TRUE (correct)
    explanation: "The text states that ACE inhibitors block the conversion of angiotensin I to angiotensin II."
  }
];

console.log('🔍 Testing ACE Inhibitor/ARB Contradiction Detection');
console.log('==================================================');

console.log('\n❌ Problematic Questions Analysis:');
problematicACEARBQuestions.forEach((q, index) => {
  console.log(`${index + 1}. ${q.question}`);
  console.log(`   Current Answer: ${q.answer}`);
  console.log(`   Explanation: ${q.explanation.substring(0, 100)}...`);
  
  // Analyze what should trigger detection
  const hasDoNot = q.question.toLowerCase().includes('do not');
  const hasUsedIn = q.question.toLowerCase().includes('used in') || q.question.toLowerCase().includes('used to treat');
  const hasContraindicated = q.explanation.toLowerCase().includes('contraindicated');
  const hasNotTheOtherWay = q.explanation.toLowerCase().includes('not the other way around');
  const hasNotARBs = q.explanation.toLowerCase().includes('not arbs');
  
  console.log(`   Triggers: doNot=${hasDoNot}, usedIn=${hasUsedIn}`);
  console.log(`   Explanation flags: contraindicated=${hasContraindicated}, notOtherWay=${hasNotTheOtherWay}, notARBs=${hasNotARBs}`);
  console.log('');
});

console.log('🔧 Running ACE/ARB Contradiction Detection...');

// Test each question individually to see detection
const results = [];
problematicACEARBQuestions.forEach((q, index) => {
  console.log(`\n🔍 Testing Question ${index + 1}:`);
  const result = apiService.validateTrueFalseLogic({ ...q }, index + 1);
  const wasChanged = result.answer !== q.answer;
  results.push(result);
  console.log(`   Original: ${q.answer} → Result: ${result.answer} ${wasChanged ? '(CHANGED ✅)' : '(SAME ❌)'}`);
});

console.log('\n🎯 Running Full System Test...');
const balancedResult = apiService.forceBalanceTrueFalseQuestions(problematicACEARBQuestions, 5);

console.log('\n✅ Final Results:');
balancedResult.forEach((q, index) => {
  const wasChanged = q.answer !== problematicACEARBQuestions[index].answer;
  const originalAnswer = problematicACEARBQuestions[index].answer;
  
  // Expected answers: Q1=True, Q2=False, Q3=False, Q4=False, Q5=True
  const expectedAnswers = ['True', 'False', 'False', 'False', 'True'];
  const isCorrect = q.answer === expectedAnswers[index];
  
  console.log(`${index + 1}. ${q.question.substring(0, 60)}...`);
  console.log(`   Answer: ${q.answer} ${wasChanged ? `(${originalAnswer} → ${q.answer})` : ''} ${isCorrect ? '✅' : '❌'}`);
  console.log('');
});

const corrections = results.filter((r, i) => r.answer !== problematicACEARBQuestions[i].answer).length;
const expectedCorrections = [true, false, false, false, true]; // What each should be
const allCorrect = results.every((r, i) => (expectedCorrections[i] && r.answer === true) || (!expectedCorrections[i] && r.answer === false));

console.log('📊 ACE/ARB Contradiction Detection Results:');
console.log(`   - Questions corrected: ${corrections}/5`);
console.log(`   - Q1 (do not block/explanation confirms): ${results[0].answer === true ? 'FIXED ✅' : 'NOT FIXED ❌'}`);
console.log(`   - Q2 (wrong direction/not other way): ${results[1].answer === false ? 'FIXED ✅' : 'NOT FIXED ❌'}`);
console.log(`   - Q3 (used in pregnancy/contraindicated): ${results[2].answer === false ? 'FIXED ✅' : 'NOT FIXED ❌'}`);
console.log(`   - Q4 (used for acute/not ARBs): ${results[3].answer === false ? 'FIXED ✅' : 'NOT FIXED ❌'}`);
console.log(`   - Q5 (correct statement): ${results[4].answer === true ? 'KEPT TRUE ✅' : 'CHANGED ❌'}`);
console.log(`   - All questions correct: ${allCorrect ? 'YES ✅' : 'NO ❌'}`);

if (allCorrect) {
  console.log('\n🎉 SUCCESS: ACE/ARB contradiction detection is working perfectly!');
  console.log('The system now catches all types of pharmacological contradictions.');
} else {
  console.log('\n⚠️ PARTIAL SUCCESS: Some ACE/ARB contradictions still not caught.');
  console.log('Need to enhance detection patterns for specific medical terms.');
}
