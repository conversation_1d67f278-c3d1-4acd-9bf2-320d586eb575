const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class SavedQuizzesService {
    constructor() {
        this.savedQuizzesFile = path.join(__dirname, '..', 'data', 'saved_quizzes.json');
        this.ensureDataDirectory();
        this.ensureSavedQuizzesFile();
    }

    ensureDataDirectory() {
        const dataDir = path.dirname(this.savedQuizzesFile);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
            logger.info('Created data directory for saved quizzes');
        }
    }

    ensureSavedQuizzesFile() {
        if (!fs.existsSync(this.savedQuizzesFile)) {
            fs.writeFileSync(this.savedQuizzesFile, JSON.stringify([], null, 2), 'utf8');
            logger.info('Created saved quizzes file');
        }
    }

    loadSavedQuizzes() {
        try {
            const data = fs.readFileSync(this.savedQuizzesFile, 'utf8');
            const quizzes = JSON.parse(data);
            return Array.isArray(quizzes) ? quizzes : [];
        } catch (error) {
            logger.error('Error loading saved quizzes:', error);
            return [];
        }
    }

    saveSavedQuizzes(quizzes) {
        try {
            fs.writeFileSync(this.savedQuizzesFile, JSON.stringify(quizzes, null, 2), 'utf8');
            return true;
        } catch (error) {
            logger.error('Error saving quizzes to file:', error);
            return false;
        }
    }

    addSavedQuiz(quiz) {
        try {
            // Validate quiz data
            if (!quiz.id || !quiz.title || !quiz.questions || !Array.isArray(quiz.questions)) {
                throw new Error('Invalid quiz data: missing required fields');
            }

            if (quiz.questions.length === 0) {
                throw new Error('Quiz must have at least one question');
            }

            // Load existing quizzes
            const quizzes = this.loadSavedQuizzes();

            // Check if quiz with same ID already exists
            const existingIndex = quizzes.findIndex(q => q.id === quiz.id);
            if (existingIndex !== -1) {
                // Update existing quiz
                quizzes[existingIndex] = {
                    ...quiz,
                    updatedAt: new Date().toISOString()
                };
                logger.info(`Updated existing saved quiz: ${quiz.id}`);
            } else {
                // Add new quiz with proper date formatting
                const currentDate = new Date();
                const newQuiz = {
                    ...quiz,
                    createdAt: quiz.createdAt || currentDate.toISOString(),
                    updatedAt: currentDate.toISOString(),
                    created_at: quiz.createdAt || currentDate.toISOString() // Backward compatibility
                };
                quizzes.unshift(newQuiz); // Add to beginning of array
                logger.info(`Added new saved quiz: ${quiz.id}`);
            }

            // Save back to file
            const saved = this.saveSavedQuizzes(quizzes);
            if (saved) {
                logger.success(`Successfully saved quiz: ${quiz.title}`);
                return { success: true, quiz: quiz };
            } else {
                throw new Error('Failed to write to file');
            }
        } catch (error) {
            logger.error('Error adding saved quiz:', error);
            return { success: false, error: error.message };
        }
    }

    getSavedQuizzes() {
        try {
            const quizzes = this.loadSavedQuizzes();
            
            // Sort by creation date (newest first)
            quizzes.sort((a, b) => {
                const dateA = new Date(a.createdAt || a.updatedAt || 0);
                const dateB = new Date(b.createdAt || b.updatedAt || 0);
                return dateB - dateA;
            });

            logger.info(`Retrieved ${quizzes.length} saved quizzes`);
            return { success: true, savedQuizzes: quizzes };
        } catch (error) {
            logger.error('Error getting saved quizzes:', error);
            return { success: false, error: error.message, savedQuizzes: [] };
        }
    }

    getSavedQuizById(quizId) {
        try {
            const quizzes = this.loadSavedQuizzes();
            const quiz = quizzes.find(q => q.id === quizId);
            
            if (quiz) {
                logger.info(`Found saved quiz: ${quizId}`);
                return { success: true, quiz: quiz };
            } else {
                return { success: false, error: 'Quiz not found' };
            }
        } catch (error) {
            logger.error('Error getting saved quiz by ID:', error);
            return { success: false, error: error.message };
        }
    }

    deleteSavedQuiz(quizId) {
        try {
            const quizzes = this.loadSavedQuizzes();
            const initialLength = quizzes.length;
            
            // Filter out the quiz to delete
            const filteredQuizzes = quizzes.filter(q => q.id !== quizId);
            
            if (filteredQuizzes.length === initialLength) {
                return { success: false, error: 'Quiz not found' };
            }

            // Save the updated list
            const saved = this.saveSavedQuizzes(filteredQuizzes);
            if (saved) {
                logger.success(`Deleted saved quiz: ${quizId}`);
                return { success: true };
            } else {
                throw new Error('Failed to write to file');
            }
        } catch (error) {
            logger.error('Error deleting saved quiz:', error);
            return { success: false, error: error.message };
        }
    }

    clearAllSavedQuizzes() {
        try {
            const saved = this.saveSavedQuizzes([]);
            if (saved) {
                logger.success('Cleared all saved quizzes');
                return { success: true };
            } else {
                throw new Error('Failed to write to file');
            }
        } catch (error) {
            logger.error('Error clearing saved quizzes:', error);
            return { success: false, error: error.message };
        }
    }

    getQuizStats() {
        try {
            const quizzes = this.loadSavedQuizzes();

            const stats = {
                totalQuizzes: quizzes.length,
                totalQuestions: quizzes.reduce((sum, quiz) => sum + (quiz.questions?.length || 0), 0),
                quizTypes: {},
                oldestQuiz: null,
                newestQuiz: null
            };

            // Count quiz types
            quizzes.forEach(quiz => {
                const type = quiz.questionType || quiz.question_type || 'Unknown';
                stats.quizTypes[type] = (stats.quizTypes[type] || 0) + 1;
            });

            // Find oldest and newest
            if (quizzes.length > 0) {
                const sortedByDate = [...quizzes].sort((a, b) => {
                    const dateA = new Date(a.createdAt || 0);
                    const dateB = new Date(b.createdAt || 0);
                    return dateA - dateB;
                });

                stats.oldestQuiz = sortedByDate[0];
                stats.newestQuiz = sortedByDate[sortedByDate.length - 1];
            }

            return { success: true, stats: stats };
        } catch (error) {
            logger.error('Error getting quiz stats:', error);
            return { success: false, error: error.message };
        }
    }

    fixIslamicDatesInTitles() {
        try {
            const quizzes = this.loadSavedQuizzes();
            let updatedCount = 0;

            const updatedQuizzes = quizzes.map(quiz => {
                // Check if title contains Islamic date patterns
                const islamicDatePattern = /[٠-٩]+\/[٠-٩]+\/[٠-٩]+\s*ه|[٠-٩]+‏\/[٠-٩]+‏\/[٠-٩]+\s*ه/;

                if (quiz.title && islamicDatePattern.test(quiz.title)) {
                    // Extract the date from createdAt and format it properly
                    const createdDate = new Date(quiz.createdAt || quiz.created_at);
                    let formattedDate;

                    if (isNaN(createdDate.getTime())) {
                        // If date is invalid, use current date
                        const now = new Date();
                        const month = (now.getMonth() + 1).toString().padStart(2, '0');
                        const day = now.getDate().toString().padStart(2, '0');
                        const year = now.getFullYear();
                        formattedDate = `${month}/${day}/${year}`;
                    } else {
                        // Format the existing date properly
                        const month = (createdDate.getMonth() + 1).toString().padStart(2, '0');
                        const day = createdDate.getDate().toString().padStart(2, '0');
                        const year = createdDate.getFullYear();
                        formattedDate = `${month}/${day}/${year}`;
                    }

                    // Determine if title is in Arabic or English
                    const isArabic = quiz.title.includes('اختبار محفوظ');
                    const questionType = quiz.questionType || quiz.question_type || 'Quiz';

                    // Generate new title with proper Gregorian date
                    const newTitle = isArabic
                        ? `اختبار محفوظ - ${questionType} - ${formattedDate}`
                        : `Saved Quiz - ${questionType} - ${formattedDate}`;

                    logger.info(`Fixing Islamic date in quiz title: "${quiz.title}" -> "${newTitle}"`);
                    updatedCount++;

                    return {
                        ...quiz,
                        title: newTitle,
                        updatedAt: new Date().toISOString()
                    };
                }

                return quiz;
            });

            if (updatedCount > 0) {
                const saved = this.saveSavedQuizzes(updatedQuizzes);
                if (saved) {
                    logger.success(`Fixed Islamic dates in ${updatedCount} quiz titles`);
                    return { success: true, updatedCount: updatedCount };
                } else {
                    throw new Error('Failed to save updated quizzes');
                }
            } else {
                logger.info('No Islamic dates found in quiz titles');
                return { success: true, updatedCount: 0 };
            }
        } catch (error) {
            logger.error('Error fixing Islamic dates in titles:', error);
            return { success: false, error: error.message };
        }
    }
}

module.exports = new SavedQuizzesService();
