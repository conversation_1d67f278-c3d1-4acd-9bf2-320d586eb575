[{"id": "deepseek/deepseek-r1:free", "name": "DeepSeek-R1", "description": "Reasoning model with thinking tags", "added": "2025-06-20T00:32:22.665Z", "custom": true}, {"id": "tngtech/deepseek-r1t-chimera:free", "name": "DeepSeek-R1T-Chimera", "description": "Enhanced reasoning model", "added": "2025-06-30T16:30:00.000Z", "custom": true}, {"id": "qwen/qwen3-32b:free", "name": "Qwen3-32B", "description": "Large context model", "added": "2025-06-30T16:30:01.000Z", "custom": true}, {"id": "mistralai/mistral-nemo:free", "name": "Mistral-Nemo", "description": "Instruction-following model", "added": "2025-06-30T16:30:02.000Z", "custom": true}, {"id": "google/gemma-3-27b-it:free", "name": "Gemma-3-27B", "description": "Google's instruction-tuned model", "added": "2025-06-30T16:30:03.000Z", "custom": true}, {"id": "meta-llama/llama-4-maverick:free", "name": "Llama-4-<PERSON><PERSON><PERSON>", "description": "Meta's latest model", "added": "2025-06-20T01:01:26.520Z", "custom": true}, {"id": "qwen/qwq-32b:free", "name": "QwQ-32B", "description": "Reasoning model", "added": "2025-06-20T01:01:04.761Z", "custom": true}, {"id": "qwen/qwen3-235b-a22b:free", "name": "Qwen3-235B", "description": "Ultra-large model", "added": "2025-06-30T16:30:04.000Z", "custom": true}, {"id": "microsoft/mai-ds-r1:free", "name": "Microsoft-MAI-DS-R1", "description": "Microsoft's reasoning model", "added": "2025-06-30T16:30:05.000Z", "custom": true}, {"id": "moonshotai/kimi-dev-72b:free", "name": "<PERSON><PERSON><PERSON><PERSON>-72B", "description": "Reasoning model with thinking tags", "added": "2025-06-20T01:12:43.602Z", "custom": true}, {"id": "thudm/glm-z1-32b:free", "name": "GLM-Z1-32B", "description": "<PERSON>sing<PERSON>'s model", "added": "2025-06-30T16:30:06.000Z", "custom": true}, {"id": "deepseek/deepseek-r1-distill-llama-70b:free", "name": "DeepSeek-R1-<PERSON>still-70B", "description": "Distilled reasoning model", "added": "2025-06-30T16:30:07.000Z", "custom": true}, {"id": "mistralai/mistral-7b-instruct:free", "name": "Mistral-7B-Instruct", "description": "Compact instruction model", "added": "2025-06-30T16:30:08.000Z", "custom": true}, {"id": "nvidia/llama-3.3-nemotron-super-49b-v1:free", "name": "Nemotron-Super-49B", "description": "NVIDIA's enhanced model", "added": "2025-06-30T16:30:09.000Z", "custom": true}, {"id": "google/gemini-2.0-flash-exp:free", "name": "Gemini-2.0-<PERSON>", "description": "Google's fast model", "added": "2025-06-20T00:59:42.865Z", "custom": true}, {"id": "mistralai/devstral-small:free", "name": "Mistral-Devstral-Small", "description": "Proven good for structured output", "added": "2025-06-20T01:00:08.104Z", "custom": true}]