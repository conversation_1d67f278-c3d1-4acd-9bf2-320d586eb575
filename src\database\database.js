// Database operations
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const config = require('../config');

// Global database instance
let db = null;

// In-memory cache for frequently accessed data
const memoryCache = {
  cache: new Map(),  // For question cache
  config: new Map(),  // For config settings
  stats: new Map(),   // For usage statistics
  rateLimits: new Map(), // User rate limits
  lastCleanup: Date.now()
};

/**
 * Initialize database and create tables
 * @returns {object} Database connection
 */
function initDatabase() {
  try {
    // Make sure the data directory exists
    const dataDir = path.join(__dirname, '../../data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    const dbPath = path.join(dataDir, 'bot.db');
    console.log(`Connecting to SQLite database at: ${dbPath}`);

    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err.message);
        throw err;
      }
      console.log('Connected to the SQLite database.');
      
      // Call migrate database to ensure all tables are properly set up
      migrateDatabase();
      
      // Initialize memory cache with data from database
      initializeMemoryCache();
    });

    // Set up cache cleanup interval
    setInterval(cleanupMemoryCache, 1000 * 60 * 60); // Clean every hour

    return db;
  } catch (error) {
    console.error('Database initialization error:', error);
    throw error;
  }
}

/**
 * Initialize memory cache with data from database
 */
function initializeMemoryCache() {
  console.log('Initializing memory cache...');
  
  // Load config into memory
  db.all('SELECT key, value FROM config', [], (err, rows) => {
    if (err) {
      console.error('Error loading config into memory cache:', err.message);
      return;
    }
    
    rows.forEach(row => {
      memoryCache.config.set(row.key, row.value);
    });
    console.log(`Loaded ${rows.length} config items into memory cache`);
  });
  
  // Check the structure of the rate_limits table first
  db.all("PRAGMA table_info(rate_limits)", (err, columns) => {
    if (err) {
      console.error('Error checking rate_limits table structure:', err.message);
      return;
    }
    
    // Check if the table has the expected columns
    const hasCountColumn = columns.some(col => col.name === 'count');
    const hasResetAtColumn = columns.some(col => col.name === 'reset_at');
    
    if (hasCountColumn && hasResetAtColumn) {
      // The table has the expected structure, proceed with loading
      // Load recent rate limits into memory (those that haven't expired)
      const now = Date.now();
      db.all('SELECT user_id, count, reset_at FROM rate_limits WHERE reset_at > ?', [now], (err, rows) => {
        if (err) {
          console.error('Error loading rate limits into memory cache:', err.message);
          return;
        }
        
        rows.forEach(row => {
          memoryCache.rateLimits.set(row.user_id, {
            count: row.count,
            resetAt: row.reset_at
          });
        });
        console.log(`Loaded ${rows.length} active rate limits into memory cache`);
      });
    } else {
      // Recreate the table with the correct structure
      console.log('Rate limits table has incorrect structure, recreating...');
      db.run('DROP TABLE IF EXISTS rate_limits', function(err) {
        if (err) {
          console.error('Error dropping rate_limits table:', err.message);
          return;
        }
        
        db.run(`CREATE TABLE IF NOT EXISTS rate_limits (
          user_id TEXT PRIMARY KEY,
          count INTEGER DEFAULT 0,
          reset_at INTEGER
        )`, function(err) {
          if (err) {
            console.error('Error recreating rate_limits table:', err.message);
          } else {
            console.log('Successfully recreated rate_limits table with correct structure');
          }
        });
      });
    }
  });
  
  // Load most recent cache entries to memory (up to limit from config)
  const cacheLimit = config.maxCacheSize || 500;
  db.all('SELECT id, data, created_at FROM cache ORDER BY created_at DESC LIMIT ?', [cacheLimit], (err, rows) => {
    if (err) {
      console.error('Error loading cache into memory:', err.message);
      return;
    }
    
    rows.forEach(row => {
      memoryCache.cache.set(row.id, {
        data: row.data,
        createdAt: row.created_at
      });
    });
    console.log(`Loaded ${rows.length} cache entries into memory cache`);
  });
}

/**
 * Clean up expired items from memory cache
 */
function cleanupMemoryCache() {
  console.log('Cleaning up memory cache...');
  const now = Date.now();
  
  // Cleanup expired rate limits
  let rateLimitsRemoved = 0;
  for (const [userId, data] of memoryCache.rateLimits.entries()) {
    if (data.resetAt < now) {
      memoryCache.rateLimits.delete(userId);
      rateLimitsRemoved++;
    }
  }
  
  // Trim cache if it's too large
  const cacheLimit = config.maxCacheSize || 500;
  let cacheRemoved = 0;
  
  if (memoryCache.cache.size > cacheLimit) {
    // Sort by creation time and remove oldest
    const sortedEntries = [...memoryCache.cache.entries()]
      .sort((a, b) => a[1].createdAt - b[1].createdAt);
    
    const toRemove = sortedEntries.slice(0, memoryCache.cache.size - cacheLimit);
    toRemove.forEach(([key]) => {
      memoryCache.cache.delete(key);
      cacheRemoved++;
    });
  }
  
  console.log(`Memory cache cleanup: removed ${rateLimitsRemoved} rate limits and ${cacheRemoved} cache entries`);
  memoryCache.lastCleanup = now;
}

/**
 * Create necessary database tables
 */
function createTables() {
  return new Promise((resolve, reject) => {
    const tables = [
      `CREATE TABLE IF NOT EXISTS config (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )`,
      `CREATE TABLE IF NOT EXISTS cache (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL,
        created_at INTEGER NOT NULL
      )`,
      `CREATE TABLE IF NOT EXISTS rate_limits (
        user_id TEXT PRIMARY KEY,
        count INTEGER NOT NULL,
        reset_at INTEGER NOT NULL
      )`,
      `CREATE TABLE IF NOT EXISTS feedback (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT NOT NULL,
        timestamp INTEGER NOT NULL
      )`,
      `CREATE TABLE IF NOT EXISTS usage_stats (
        stat_key TEXT PRIMARY KEY,
        value INTEGER DEFAULT 0
      )`,
      `CREATE TABLE IF NOT EXISTS quiz_attempts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        type TEXT NOT NULL,
        score INTEGER NOT NULL,
        total INTEGER NOT NULL,
        timestamp INTEGER NOT NULL
      )`,
      `CREATE TABLE IF NOT EXISTS quiz_answers (
        id TEXT PRIMARY KEY,
        attempt_id TEXT NOT NULL,
        question_text TEXT NOT NULL,
        user_answer TEXT NOT NULL,
        correct_answer TEXT NOT NULL,
        is_correct INTEGER NOT NULL,
        FOREIGN KEY(attempt_id) REFERENCES quiz_attempts(id)
      )`,
      `CREATE TABLE IF NOT EXISTS api_keys (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        last_used INTEGER
      )`,
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY,
        telegram_id TEXT UNIQUE,
        username TEXT,
        first_name TEXT,
        last_name TEXT,
        language TEXT,
        join_date INTEGER,
        last_activity INTEGER
      )`,
      `CREATE TABLE IF NOT EXISTS api_usage (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        api_key TEXT,
        user_id TEXT,
        request_type TEXT,
        tokens_used INTEGER,
        timestamp INTEGER
      )`,
      `CREATE TABLE IF NOT EXISTS quiz_attempts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT,
        quiz_type TEXT,
        score INTEGER,
        total_questions INTEGER,
        percentage INTEGER,
        timestamp INTEGER
      )`,
      `CREATE TABLE IF NOT EXISTS feedback (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT,
        username TEXT,
        rating TEXT,
        suggestion TEXT,
        quiz_type TEXT,
        score INTEGER,
        timestamp INTEGER,
        is_read INTEGER DEFAULT 0
      )`
    ];

    // Execute each CREATE TABLE statement
    db.serialize(() => {
      db.run('BEGIN TRANSACTION');
      
      let hasError = false;
      for (const sql of tables) {
        db.run(sql, (err) => {
          if (err && !hasError) {
            hasError = true;
            db.run('ROLLBACK');
            reject(err);
          }
        });
      }
      
      db.run('COMMIT', (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  });
}

/**
 * Run database migrations to update schema
 */
function runDatabaseMigrations() {
  return new Promise((resolve, reject) => {
    console.log('Running database migrations...');
    
    // Check if config table has entries and add defaults if empty
    db.get('SELECT COUNT(*) as count FROM config', (err, row) => {
      if (err) {
        console.error('Error checking config table:', err.message);
        reject(err);
        return;
      }
      
      if (row.count === 0) {
        console.log('Initializing config table with default values...');
        const defaults = [
          { key: 'current_api_key_index', value: '0' },
          { key: 'last_cleanup', value: Date.now().toString() }
        ];
        
        db.run('BEGIN TRANSACTION');
        
        for (const item of defaults) {
          db.run('INSERT INTO config (key, value) VALUES (?, ?)', 
            [item.key, item.value], 
            function(err) {
              if (err) {
                console.error('Error inserting config:', err.message);
                db.run('ROLLBACK');
                reject(err);
                return;
              }
            }
          );
        }
        
        db.run('COMMIT', (err) => {
          if (err) {
            console.error('Error committing config transaction:', err.message);
            reject(err);
          } else {
            console.log('Config table initialized with defaults');
            resolve();
          }
        });
      } else {
        console.log('Config table already has data, skipping initialization');
        resolve();
      }
    });
  });
}

/**
 * Get cache hit rate 
 * @returns {Promise<object>} Hit rate statistics
 */
async function getCacheHitRate() {
  return new Promise((resolve, reject) => {
    try {
      // Get total counts from stat_key
      db.all("SELECT stat_key, value FROM usage_stats WHERE stat_key IN ('cache_hit', 'cache_miss')", [], (err, rows) => {
        if (err) {
          console.error('Error getting cache hit rates:', err);
          resolve({ hitRate: 0, totalLookups: 0 });
          return;
        }
        
        let hits = 0;
        let totalLookups = 0;
        
        rows.forEach(row => {
          if (row.stat_key === 'cache_hit') {
            hits = row.value || 0;
          } else if (row.stat_key === 'cache_miss') {
            totalLookups += row.value || 0;
          }
        });
        
        // Add hits to the total lookups
        totalLookups += hits;
        
        const hitRate = totalLookups > 0 ? hits / totalLookups : 0;
        resolve({ hitRate, totalLookups });
      });
    } catch (error) {
      console.error('Error calculating cache hit rate:', error);
      resolve({ hitRate: 0, totalLookups: 0 });
    }
  });
}

/**
 * Clean up old cache entries
 */
function cleanupOldCache() {
  const maxAgeMs = 72 * 60 * 60 * 1000; // 72 hours
  const cutoffTime = Date.now() - maxAgeMs;
  
  db.run('DELETE FROM cache WHERE created_at < ?', [cutoffTime], function(err) {
    if (err) {
      console.error('Error cleaning up cache:', err);
    } else {
      console.log(`Cache cleanup: removed ${this.changes} old entries`);
    }
  });
}

/**
 * Check if user has exceeded rate limit
 * @param {string|number} userId - User ID to check
 * @returns {Promise<object>} Rate limit status
 */
function checkRateLimit(userId) {
  return new Promise((resolve, reject) => {
    const now = Date.now();
    const resetTime = now + (24 * 60 * 60 * 1000); // 24 hours from now
    const maxRequests = 20; // Maximum requests per day
    
    // Check if user exists in rate limit table
    db.get('SELECT * FROM rate_limits WHERE user_id = ?', [userId], (err, row) => {
      if (err) {
        console.error('Error checking rate limit:', err);
        resolve({ allowed: true, remaining: maxRequests - 1 });
        return;
      }
      
      if (!row) {
        // Create new rate limit entry
        db.run('INSERT INTO rate_limits (user_id, count, reset_at) VALUES (?, 1, ?)', 
          [userId, resetTime], (err) => {
            if (err) console.error('Error creating rate limit entry:', err);
          });
        resolve({ allowed: true, remaining: maxRequests - 1 });
        return;
      }
      
      // Check if reset time has passed
      if (now > row.reset_at) {
        // Reset count and update reset time
        db.run('UPDATE rate_limits SET count = 1, reset_at = ? WHERE user_id = ?', 
          [resetTime, userId], (err) => {
            if (err) console.error('Error resetting rate limit:', err);
          });
        resolve({ allowed: true, remaining: maxRequests - 1 });
        return;
      }
      
      // Check if limit exceeded
      if (row.count >= maxRequests) {
        const remainingTime = Math.ceil((row.reset_at - now) / (60 * 60 * 1000)); // hours
        resolve({ allowed: false, remainingTime });
        return;
      }
      
      // Increment count
      db.run('UPDATE rate_limits SET count = count + 1 WHERE user_id = ?', [userId], (err) => {
        if (err) console.error('Error updating rate limit:', err);
      });
      
      resolve({ allowed: true, remaining: maxRequests - row.count - 1 });
    });
  });
}

/**
 * Returns the database instance
 * @returns {object} SQLite database instance
 */
function getDb() {
  return db;
}

// Add or update database tables
function migrateDatabase() {
  if (!db) {
    console.error('Database not initialized for migration');
    return;
  }
  
  // Create tables if they don't exist
  db.serialize(() => {
    // Config table
    db.run(`CREATE TABLE IF NOT EXISTS config (
      key TEXT PRIMARY KEY,
      value TEXT,
      updated_at INTEGER
    )`);
    
    // API keys table
    db.run(`CREATE TABLE IF NOT EXISTS api_keys (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      key TEXT NOT NULL,
      source TEXT,
      is_active INTEGER DEFAULT 1,
      last_used INTEGER,
      created_at INTEGER
    )`);
    
    // Cache table with hash_key column
    db.run(`CREATE TABLE IF NOT EXISTS cache (
      id TEXT PRIMARY KEY,
      data TEXT NOT NULL,
      created_at INTEGER NOT NULL,
      content_text TEXT,
      filtered_text TEXT,
      result TEXT
    )`);
    
    // Feedback table
    db.run(`CREATE TABLE IF NOT EXISTS feedback (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      username TEXT,
      rating TEXT NOT NULL,
      suggestion TEXT,
      quiz_type TEXT,
      score INTEGER,
      timestamp INTEGER NOT NULL,
      is_read INTEGER DEFAULT 0
    )`, (err) => {
      if (err) {
        console.error('Error creating feedback table:', err);
      } else {
        console.log('Feedback table created or already exists');
      }
    });
    
    // Quiz attempts table
    db.run(`CREATE TABLE IF NOT EXISTS quiz_attempts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      username TEXT,
      quiz_type TEXT NOT NULL,
      topic TEXT,
      score INTEGER NOT NULL,
      total_questions INTEGER NOT NULL,
      timestamp INTEGER NOT NULL
    )`, (err) => {
      if (err) {
        console.error('Error creating quiz_attempts table:', err);
      } else {
        console.log('Quiz attempts table created or already exists');
      }
    });
    
    // Create rate_limits table
    db.run(`CREATE TABLE IF NOT EXISTS rate_limits (
      user_id TEXT PRIMARY KEY,
      count INTEGER DEFAULT 0,
      reset_at INTEGER
    )`);
    
    // Create file_uploads table to track user file uploads per day
    db.run(`CREATE TABLE IF NOT EXISTS file_uploads (
      user_id TEXT PRIMARY KEY,
      count INTEGER DEFAULT 0,
      reset_at INTEGER
    )`, (err) => {
      if (err) {
        console.error('Error creating file_uploads table:', err);
      } else {
        console.log('File uploads table created or already exists');
      }
    });
    
    // Usage stats table with stat_key column
    db.run(`CREATE TABLE IF NOT EXISTS usage_stats (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      stat_key TEXT NOT NULL,
      value INTEGER DEFAULT 0,
      timestamp INTEGER DEFAULT (strftime('%s','now'))
    )`);
    
    // Check if content_text column exists and add it if it doesn't
    db.all("PRAGMA table_info(cache)", (err, rows) => {
      if (err) {
        console.error('Error checking cache table columns:', err);
      } else {
        // Add content_text column if it doesn't exist
        if (!rows.some(row => row.name === 'content_text')) {
          db.run("ALTER TABLE cache ADD COLUMN content_text TEXT", (err) => {
            if (err) {
              console.error('Error adding content_text column to cache table:', err);
            } else {
              console.log('Added content_text column to cache table');
            }
          });
        }
        
        // Add filtered_text column if it doesn't exist
        if (!rows.some(row => row.name === 'filtered_text')) {
          db.run("ALTER TABLE cache ADD COLUMN filtered_text TEXT", (err) => {
            if (err) {
              console.error('Error adding filtered_text column to cache table:', err);
            } else {
              console.log('Added filtered_text column to cache table');
            }
          });
        }
        
        // Add result column if it doesn't exist
        if (!rows.some(row => row.name === 'result')) {
          db.run("ALTER TABLE cache ADD COLUMN result TEXT", (err) => {
            if (err) {
              console.error('Error adding result column to cache table:', err);
            } else {
              console.log('Added result column to cache table');
            }
          });
        }
      }
    });
    
    // Check if usage_stats table has the correct columns
    db.all("PRAGMA table_info(usage_stats)", (err, rows) => {
      if (err) {
        console.error('Error checking usage_stats table columns:', err);
      } else {
        // Check if event_type column exists but stat_key doesn't
        if (rows.some(row => row.name === 'event_type') && !rows.some(row => row.name === 'stat_key')) {
          // Drop and recreate the table if it has the wrong schema
          db.run("DROP TABLE IF EXISTS usage_stats", (err) => {
            if (err) {
              console.error('Error dropping usage_stats table:', err);
            } else {
              db.run(`CREATE TABLE IF NOT EXISTS usage_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stat_key TEXT NOT NULL,
                value INTEGER DEFAULT 0,
                timestamp INTEGER DEFAULT (strftime('%s','now'))
              )`, (err) => {
                if (err) {
                  console.error('Error recreating usage_stats table:', err);
                } else {
                  console.log('Recreated usage_stats table with correct schema');
                }
              });
            }
          });
        }
      }
    });
    
    // Check if config table is empty and initialize if needed
    db.get('SELECT COUNT(*) as count FROM config', (err, row) => {
      if (err) {
        console.error('Error checking config table:', err);
        return;
      }
      
      if (row.count === 0) {
        console.log('Initializing config table with default values...');
        
        // Default settings
        const defaults = [
          { key: 'current_api_key_index', value: '0' },
          { key: 'prompt_version', value: '1.0' },
          { key: 'rate_limit_reset', value: Date.now().toString() },
          { key: 'rate_limit_remaining', value: '60' }  // Default quota
        ];
        
        // Insert defaults
        const stmt = db.prepare('INSERT INTO config (key, value, updated_at) VALUES (?, ?, ?)');
        defaults.forEach(item => {
          stmt.run(item.key, item.value, Date.now());
        });
        stmt.finalize();
        
        console.log('Config table initialized with defaults');
      } else {
        console.log('Config table already has data, skipping initialization');
      }
    });
    
    // Create model_stats table if it doesn't exist
    db.run(`CREATE TABLE IF NOT EXISTS model_stats (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      model TEXT NOT NULL,
      success INTEGER NOT NULL,
      duration INTEGER,
      status TEXT,
      timestamp INTEGER NOT NULL
    )`, (err) => {
      if (err) {
        console.error('Error creating model_stats table:', err);
      } else {
        console.log('Model stats table created or already exists');
      }
    });

    // Create quiz_sessions table for storing completed quiz results
    db.run(`CREATE TABLE IF NOT EXISTS quiz_sessions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      timestamp TEXT NOT NULL,
      question_type TEXT NOT NULL,
      score_correct INTEGER NOT NULL,
      score_total INTEGER NOT NULL,
      duration INTEGER NOT NULL,
      answers TEXT NOT NULL,
      questions TEXT NOT NULL
    )`, (err) => {
      if (err) {
        console.error('Error creating quiz_sessions table:', err);
      } else {
        console.log('Quiz sessions table created or already exists');
      }
    });

    // Create saved_quizzes table for storing quiz questions for later use
    db.run(`CREATE TABLE IF NOT EXISTS saved_quizzes (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      question_type TEXT NOT NULL,
      questions TEXT NOT NULL,
      created_at TEXT NOT NULL,
      source TEXT
    )`, (err) => {
      if (err) {
        console.error('Error creating saved_quizzes table:', err);
      } else {
        console.log('Saved quizzes table created or already exists');
      }
    });
  });
}

/**
 * Check if user has exceeded file upload limit
 * @param {string|number} userId - User ID to check
 * @returns {Promise<object>} Upload limit status
 */
function checkFileUploadLimit(userId) {
  return new Promise((resolve, reject) => {
    const now = Date.now();
    const config = require('../config');
    const maxUploads = config.fileUploadsPerDay || 5;
    
    // Check if user exists in file_uploads table
    db.get('SELECT * FROM file_uploads WHERE user_id = ?', [userId], (err, row) => {
      if (err) {
        console.error('Error checking file upload limit:', err);
        resolve({ allowed: true, remaining: maxUploads - 1 });
        return;
      }
      
      if (!row) {
        // First upload of the day, create new entry
        const resetTime = now + (24 * 60 * 60 * 1000); // 24 hours from now
        db.run('INSERT INTO file_uploads (user_id, count, reset_at) VALUES (?, 1, ?)', 
          [userId, resetTime], (err) => {
            if (err) console.error('Error creating file upload entry:', err);
          });
        resolve({ allowed: true, remaining: maxUploads - 1 });
        return;
      }
      
      // Check if reset time has passed
      if (now > row.reset_at) {
        // Reset count and update reset time
        const resetTime = now + (24 * 60 * 60 * 1000);
        db.run('UPDATE file_uploads SET count = 1, reset_at = ? WHERE user_id = ?', 
          [resetTime, userId], (err) => {
            if (err) console.error('Error resetting file upload limit:', err);
          });
        resolve({ allowed: true, remaining: maxUploads - 1 });
        return;
      }
      
      // Check if limit exceeded
      if (row.count >= maxUploads) {
        const remainingTime = Math.ceil((row.reset_at - now) / (60 * 60 * 1000)); // hours
        resolve({ 
          allowed: false, 
          remainingTime,
          resetAt: new Date(row.reset_at).toISOString()
        });
        return;
      }
      
      // Increment count
      db.run('UPDATE file_uploads SET count = count + 1 WHERE user_id = ?', [userId], (err) => {
        if (err) console.error('Error updating file upload count:', err);
      });
      
      // Fix: Calculate remaining uploads correctly - should be max minus current count
      // The current count is row.count + 1 because we just incremented it
      resolve({ allowed: true, remaining: maxUploads - (row.count + 1) });
    });
  });
}

/**
 * Update bot configuration setting
 * @param {string} key - Configuration key
 * @param {string} value - Configuration value
 * @returns {Promise<boolean>} Success status
 */
function updateConfig(key, value) {
  return new Promise((resolve, reject) => {
    const now = Date.now();
    
    db.run(
      'INSERT OR REPLACE INTO config (key, value, updated_at) VALUES (?, ?, ?)',
      [key, value, now],
      (err) => {
        if (err) {
          console.error(`Error updating config for ${key}:`, err);
          resolve(false);
          return;
        }
        
        resolve(true);
      }
    );
  });
}

/**
 * Get bot configuration setting
 * @param {string} key - Configuration key
 * @returns {Promise<string|null>} Configuration value
 */
function getConfig(key) {
  return new Promise((resolve, reject) => {
    db.get('SELECT value FROM config WHERE key = ?', [key], (err, row) => {
      if (err) {
        console.error(`Error getting config for ${key}:`, err);
        resolve(null);
        return;
      }
      
      if (!row) {
        resolve(null);
        return;
      }
      
      resolve(row.value);
    });
  });
}

// Modify the cacheData function to use in-memory cache
function cacheData(id, data) {
  return new Promise((resolve, reject) => {
    const now = Date.now();
    const jsonData = JSON.stringify(data);
    
    // Store in memory cache first
    memoryCache.cache.set(id, {
      data: jsonData,
      createdAt: now
    });
    
    // Then persist to database
    db.run(
      'INSERT OR REPLACE INTO cache (id, data, created_at) VALUES (?, ?, ?)',
      [id, jsonData, now],
      function(err) {
        if (err) {
          console.error('Error caching data:', err.message);
          reject(err);
        } else {
          resolve({ id, timestamp: now });
        }
      }
    );
  });
}

// Modify getCachedData to use in-memory cache first
function getCachedData(id) {
  return new Promise((resolve, reject) => {
    // Check memory cache first
    if (memoryCache.cache.has(id)) {
      try {
        const cachedItem = memoryCache.cache.get(id);
        const parsedData = JSON.parse(cachedItem.data);
        console.log(`Memory cache hit for ${id}`);
        resolve(parsedData);
        return;
      } catch (e) {
        console.error('Error parsing cached data from memory:', e);
        // Fall through to database lookup on parse error
      }
    }
    
    // Not in memory or parse error, check database
    db.get(
      'SELECT data FROM cache WHERE id = ?',
      [id],
      (err, row) => {
        if (err) {
          console.error('Error fetching cached data:', err.message);
          reject(err);
        } else if (row) {
          try {
            const parsedData = JSON.parse(row.data);
            
            // Add to memory cache for next time
            memoryCache.cache.set(id, {
              data: row.data,
              createdAt: Date.now() // We don't know the exact time, use now
            });
            
            resolve(parsedData);
          } catch (parseError) {
            console.error('Error parsing cached data:', parseError);
            reject(parseError);
          }
        } else {
          resolve(null); // No data found
        }
      }
    );
  });
}

// Check rate limits with memory cache optimization
function checkRateLimit(userId, actionType = 'file_upload', limit = 5, resetHours = 24) {
  return new Promise((resolve, reject) => {
    const now = Date.now();
    
    // Check memory cache first
    if (memoryCache.rateLimits.has(userId)) {
      const userLimit = memoryCache.rateLimits.get(userId);
      
      // If reset time has passed, reset the counter
      if (userLimit.resetAt < now) {
        memoryCache.rateLimits.delete(userId);
      } else {
        // Rate limit still active
        const remaining = Math.max(0, limit - userLimit.count);
        const resetTime = userLimit.resetAt;
        const remainingTime = Math.max(0, (resetTime - now) / 1000 / 60 / 60); // hours
        
        return resolve({
          allowed: remaining > 0,
          remaining: remaining,
          resetAt: new Date(resetTime),
          remainingTime: remainingTime
        });
      }
    }
    
    // Not in memory cache or expired, check database
    db.get(
      'SELECT count, reset_at FROM rate_limits WHERE user_id = ?',
      [userId],
      (err, row) => {
        if (err) {
          console.error('Error checking rate limit:', err.message);
          reject(err);
          return;
        }
        
        // If no record or reset time has passed, create/reset counter
        if (!row || row.reset_at < now) {
          const resetAt = now + (resetHours * 60 * 60 * 1000);
          
          // Add to memory cache
          memoryCache.rateLimits.set(userId, {
            count: 1,
            resetAt: resetAt
          });
          
          // Update in database
          db.run(
            'INSERT OR REPLACE INTO rate_limits (user_id, count, reset_at) VALUES (?, ?, ?)',
            [userId, 1, resetAt],
            function(err) {
              if (err) {
                console.error('Error updating rate limit:', err.message);
                reject(err);
                return;
              }
              
              resolve({
                allowed: true,
                remaining: limit - 1,
                resetAt: new Date(resetAt),
                remainingTime: resetHours
              });
            }
          );
        } else {
          // Existing rate limit
          const count = row.count;
          const resetAt = row.reset_at;
          const remaining = Math.max(0, limit - count);
          const remainingTime = Math.max(0, (resetAt - now) / 1000 / 60 / 60); // hours
          
          if (remaining > 0) {
            // Increment usage count
            const newCount = count + 1;
            
            // Update memory cache
            memoryCache.rateLimits.set(userId, {
              count: newCount,
              resetAt: resetAt
            });
            
            // Update database
            db.run(
              'UPDATE rate_limits SET count = ? WHERE user_id = ?',
              [newCount, userId],
              function(err) {
                if (err) {
                  console.error('Error updating rate limit count:', err.message);
                  reject(err);
                  return;
                }
                
                resolve({
                  allowed: true,
                  remaining: limit - newCount,
                  resetAt: new Date(resetAt),
                  remainingTime: remainingTime
                });
              }
            );
          } else {
            // Rate limit exceeded
            resolve({
              allowed: false,
              remaining: 0,
              resetAt: new Date(resetAt),
              remainingTime: remainingTime
            });
          }
        }
      }
    );
  });
}

module.exports = {
  initDatabase,
  runDatabaseMigrations,
  getCacheHitRate,
  cleanupOldCache,
  checkRateLimit,
  db: getDb,
  migrateDatabase,
  checkFileUploadLimit,
  updateConfig,
  getConfig,
  cacheData,
  getCachedData
}; 