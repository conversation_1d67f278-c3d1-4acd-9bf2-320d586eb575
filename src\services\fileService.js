const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { extractWithPython } = require('../utils/pythonExtractor');

/**
 * Downloads a file from Telegram using the bot instance and file ID
 * @param {Object} bot - Telegram bot instance
 * @param {string} fileId - Telegram file ID to download
 * @returns {Promise<string>} - Path to the downloaded file
 */
async function downloadFile(bot, fileId) {
  try {
    console.log(`Downloading file with ID: ${fileId}`);
    const fileLink = await bot.telegram.getFileLink(fileId);
    
    // Get file extension from the URL
    const fileUrl = fileLink.href || fileLink.toString();
    const fileExtension = path.extname(fileUrl).toLowerCase();
    
    // Create a temporary file path with the correct extension
    const tempDir = require('os').tmpdir();
    const timestamp = Date.now();
    const tempFilePath = path.join(tempDir, `temp_${timestamp}${fileExtension}`);
    
    // Download the file
    const response = await axios({
      method: 'get',
      url: fileUrl,
      responseType: 'arraybuffer'
    });
    
    // Write the file to disk
    fs.writeFileSync(tempFilePath, Buffer.from(response.data));
    console.log(`Created temporary file at ${tempFilePath}`);
    
    return tempFilePath;
  } catch (error) {
    console.error('Error downloading file:', error.message);
    throw new Error(`Failed to download file: ${error.message}`);
  }
}

/**
 * Extracts text from a document file using Python extraction
 * @param {string} filePath - Path to the document file
 * @returns {Promise<{text: string, pageCount: number, questionCount: number}>} - Extracted text and metadata
 */
async function extractTextFromDocument(filePath) {
  try {
    // Get file extension to determine document type
    const fileExtension = path.extname(filePath).toLowerCase();
    
    // Log which extraction method we're using
    console.log(`Using Python extraction for ${fileExtension} file`);
    
    // Call Python extraction with the file path
    const result = await extractWithPython(filePath);
    
    // Validate the result
    if (!result || !result.text || result.text.trim().length === 0) {
      throw new Error(`Failed to extract text from ${fileExtension} document: Empty result`);
    }
    
    return result;
  } catch (error) {
    console.error(`Document extraction error: ${error.message}`);
    throw new Error(`Failed to extract text from document: ${error.message}`);
  }
}

module.exports = {
  downloadFile,
  extractTextFromDocument
}; 