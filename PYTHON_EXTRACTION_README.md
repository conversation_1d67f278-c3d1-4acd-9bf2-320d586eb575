# Python Extraction Setup Guide

This guide explains how to set up the Python-based text extraction functionality for the Telegram MCQ/TF Bot on Windows.

## Requirements

- Python 3.8 or later
- Tesseract OCR
- Required Python packages

## Step 1: Install Python

1. Download Python 3.8+ from the [official Python website](https://www.python.org/downloads/)
2. Run the installer
3. **Important:** Check the box that says "Add Python to PATH" during installation
4. Complete the installation

## Step 2: Install Tesseract OCR

Tesseract is required for extracting text from images and scanned documents:

1. Download the Tesseract installer from [UB-Mannheim's GitHub repository](https://github.com/UB-Mannheim/tesseract/wiki)
2. Run the installer
3. Select additional languages if needed (Arabic is recommended for this bot)
4. Note the installation path (default is usually `C:\Program Files\Tesseract-OCR`)
5. Add Tesseract to your PATH environment variable:
   - Right-click on "This PC" or "My Computer" and select "Properties"
   - Click on "Advanced system settings"
   - Click the "Environment Variables" button
   - Under "System variables", find the "Path" variable, select it and click "Edit"
   - Click "New" and add the Tesseract installation path (e.g., `C:\Program Files\Tesseract-OCR`)
   - Click "OK" to close all dialogs

## Step 3: Install Required Python Packages

Open Command Prompt and run:

```
pip install pytesseract pillow pdf2image PyPDF2 opencv-python numpy matplotlib reportlab
```

## Step 4: Install Poppler (for PDF support)

For PDF processing, you need Poppler:

1. Download Poppler for Windows from [this link](https://github.com/oschwartz10612/poppler-windows/releases/)
2. Extract the ZIP file to a location like `C:\poppler`
3. Add the `bin` directory to your PATH environment variable:
   - Following the same steps as for Tesseract, add `C:\poppler\bin` to the PATH

## Step 5: Configure the Bot

Add the following to your `.env` file if Tesseract or Poppler are not in your PATH:

```
TESSERACT_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe
POPPLER_PATH=C:\poppler\bin
```

## Testing the Extraction

To test if the extraction is working correctly:

1. Navigate to your project directory
2. Run the following command:

```
python simple_extraction_service_fixed.py path/to/test/file.pdf
```

You should see JSON output with the extracted text and metadata.

## Troubleshooting

### Common Issues

1. **"Tesseract not found"** error:
   - Verify Tesseract is correctly installed
   - Check if the Tesseract path is in your PATH environment variable
   - Add the TESSERACT_PATH to your .env file

2. **PDF conversion errors**:
   - Make sure Poppler is correctly installed
   - Verify the Poppler bin directory is in your PATH
   - Add POPPLER_PATH to your .env file

3. **Missing DLLs**:
   - Install the Visual C++ Redistributable for Visual Studio 2015-2019

4. **Memory errors with large documents**:
   - Try increasing your system's virtual memory
   - Process smaller documents or split large ones

### Performance Tips

1. For better OCR results, adjust the DPI setting in the Python script:
   - Open `simple_extraction_service_fixed.py`
   - Find the line with `dpi=300` and increase it to 400 or 500 for better quality
   - Note that higher DPI settings will use more memory and processing time

2. For faster processing (but potentially lower quality):
   - Decrease the DPI setting to 200
   - Add `oem=1` to the Tesseract config options in the script

## Additional Resources

- [Tesseract Documentation](https://tesseract-ocr.github.io/tessdoc/)
- [PyTesseract Documentation](https://github.com/madmaze/pytesseract)
- [PDF2Image Documentation](https://github.com/Belval/pdf2image) 