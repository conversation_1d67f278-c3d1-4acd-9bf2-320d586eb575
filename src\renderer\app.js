// Main application logic for the renderer process
console.log('🚀 app.js file is loading...');

class QuestionGeneratorApp {
    constructor() {
        console.log('🚀 QuestionGeneratorApp constructor called - JavaScript is loading!');
        this.currentScreen = 'homeScreen';
        this.selectedQuestionType = null;
        this.selectedInputMethod = null;
        this.currentQuestions = [];
        this.currentQuiz = null;
        this.quizState = {
            currentQuestionIndex: 0,
            answers: [],
            score: { correct: 0, total: 0 },
            startTime: null,
            endTime: null
        };

        // Initialize model preference (will be loaded from settings)
        this.savedModelPreference = null;

        // Global AI Settings state
        this.isGlobalAiSidebarOpen = false;

        // Pagination properties
        this.savedQuizzesPage = 1;
        this.savedQuizzesPerPage = 6;
        this.allSavedQuizzes = [];
        this.historyPage = 1;
        this.historyPerPage = 6;
        this.allHistorySessions = [];

        // PDF Editor state
        this.selectedImages = [];
        this.selectedPdfs = [];
        this.currentPdfFile = null;
        this.currentFeature = 'imageToPdf';
        this.pdfSettings = {
            quality: 'medium',
            pageSize: 'a4',
            margin: 'small'
        };
        this.textToPdfSettings = {
            pageSize: 'a4',
            margins: 'medium',
            lineSpacing: '1.15',
            fontFamily: 'Arial',
            fontSize: 12
        };
        this.mergePdfSettings = {
            quality: 'balanced',
            maintainBookmarks: true,
            addTableOfContents: false,
            addPageNumbers: false
        };
        this.splitPdfSettings = {
            method: 'pages',
            pageRanges: '',
            everyNPages: 1,
            extractPages: ''
        };
        this.lockPdfSettings = {
            userPassword: '',
            ownerPassword: '',
            allowPrinting: true,
            allowCopying: true,
            allowModifying: false
        };
        this.deletePagesSettings = {
            pagesToDelete: ''
        };

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupMenuListeners();
        this.setupDragAndDrop();
        this.setupPdfEditorListeners();
        this.setupGlobalAiSettings();
        this.initializeLanguage();
        this.showScreen('homeScreen');
    }

    initializeLanguage() {
        // Initialize the language system
        if (window.initializeLanguage) {
            window.initializeLanguage();
        }
    }

    setupEventListeners() {
        // Home screen service launchers
        document.getElementById('quizGeneratorService').addEventListener('click', () => {
            this.showScreen('quizGeneratorScreen');
        });

        document.getElementById('textToolsService').addEventListener('click', () => {
            console.log('🎯 Text Tools Service clicked!');
            console.log('🎯 Attempting to show textToolsScreen...');
            this.showScreen('textToolsScreen');
            console.log('🎯 showScreen call completed');
        });

        // Quiz Generator Screen - Question type selection
        document.getElementById('mcqBtn').addEventListener('click', () => {
            this.selectQuestionType('MCQ');
        });

        document.getElementById('tfBtn').addEventListener('click', () => {
            this.selectQuestionType('TF');
        });

        // Input method selection
        document.getElementById('textInputBtn').addEventListener('click', () => {
            this.selectInputMethod('text');
        });

        document.getElementById('fileUploadBtn').addEventListener('click', async () => {
            // Directly trigger file selection dialog instead of navigating to content screen
            await this.generateFromFile();
        });

        document.getElementById('imageUploadBtn').addEventListener('click', async () => {
            // Directly trigger image selection dialog instead of navigating to content screen
            await this.generateFromImage();
        });

        // Text Tools Screen Navigation
        document.getElementById('backToHomeFromTextTools').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // Quiz Generator Navigation Tabs
        document.getElementById('generateTab').addEventListener('click', () => {
            this.setActiveQuizTab('generate');
        });

        document.getElementById('historyTabNav').addEventListener('click', () => {
            this.setActiveQuizTab('history');
            this.showHistoryScreen();
        });

        document.getElementById('statisticsTabNav').addEventListener('click', () => {
            this.setActiveQuizTab('statistics');
            this.showStatisticsScreen();
        });

        // History Screen Navigation
        document.getElementById('generateTabHistory').addEventListener('click', () => {
            this.showScreen('quizGeneratorScreen');
        });

        document.getElementById('historyTabHistory').addEventListener('click', () => {
            // Already on history screen, just ensure active state
            this.setActiveQuizTabOnScreen('history', 'historyScreen');
        });

        document.getElementById('statisticsTabHistory').addEventListener('click', () => {
            this.showStatisticsScreen();
        });

        // Statistics Screen Navigation
        document.getElementById('generateTabStats').addEventListener('click', () => {
            this.showScreen('quizGeneratorScreen');
        });

        document.getElementById('historyTabStats').addEventListener('click', () => {
            this.showHistoryScreen();
        });

        document.getElementById('statisticsTabStats').addEventListener('click', () => {
            // Already on statistics screen, just ensure active state
            this.setActiveQuizTabOnScreen('statistics', 'statisticsScreen');
        });

        // Mind Map Input Methods
        document.getElementById('mindMapTextInputBtn').addEventListener('click', () => {
            this.selectMindMapInputMethod('text');
        });

        document.getElementById('mindMapFileUploadBtn').addEventListener('click', () => {
            this.selectMindMapInputMethod('file');
        });

        document.getElementById('mindMapImageUploadBtn').addEventListener('click', () => {
            this.selectMindMapInputMethod('image');
        });

        // Mind Map Generation Handlers
        document.getElementById('generateMindMapFromText').addEventListener('click', () => {
            this.generateMindMapFromText();
        });

        // Add a test button for debugging (temporary)
        const testBtn = document.createElement('button');
        testBtn.textContent = 'Test Mind Map Display';
        testBtn.className = 'btn btn-secondary btn-sm';
        testBtn.style.margin = '10px';
        testBtn.onclick = () => this.testMindMapDisplay();
        document.querySelector('.mind-map-input-section')?.appendChild(testBtn);

        // Add a persistent test button
        const persistentTestBtn = document.createElement('button');
        persistentTestBtn.textContent = 'Persistent Test';
        persistentTestBtn.className = 'btn btn-info btn-sm';
        persistentTestBtn.style.margin = '10px';
        persistentTestBtn.onclick = () => this.createPersistentTest();
        document.querySelector('.mind-map-input-section')?.appendChild(persistentTestBtn);

        // Add a button to manually hide processing screen (debugging)
        const hideBtn = document.createElement('button');
        hideBtn.textContent = 'Hide Processing Screen';
        hideBtn.className = 'btn btn-warning btn-sm';
        hideBtn.style.margin = '10px';
        hideBtn.onclick = () => {
            console.log('Manually hiding processing screen...');
            this.hideProcessingScreen();
            this.showScreen('textToolsScreen');
        };
        document.querySelector('.mind-map-input-section')?.appendChild(hideBtn);

        // Add a button to force mind map visibility (debugging)
        const forceBtn = document.createElement('button');
        forceBtn.textContent = 'Force Show Mind Map';
        forceBtn.className = 'btn btn-success btn-sm';
        forceBtn.style.margin = '10px';
        forceBtn.onclick = () => {
            console.log('Forcing mind map visibility...');
            const container = document.getElementById('mindMapContainer');
            if (container) {
                container.style.border = '3px solid lime';
                container.style.backgroundColor = '#000';
                container.style.minHeight = '600px';
                container.style.display = 'block';
                container.style.visibility = 'visible';
                container.style.opacity = '1';
                container.style.zIndex = '9999';
                container.scrollIntoView({ behavior: 'smooth' });
                console.log('Mind map container forced visible');
                console.log('Container content:', container.innerHTML.substring(0, 200));
            }
        };
        document.querySelector('.mind-map-input-section')?.appendChild(forceBtn);

        document.getElementById('generateMindMapFromFile').addEventListener('click', () => {
            this.generateMindMapFromFile();
        });

        document.getElementById('generateMindMapFromImage').addEventListener('click', () => {
            this.generateMindMapFromImage();
        });

        // Mind Map File Input Handlers
        document.getElementById('mindMapFileInput').addEventListener('change', async (e) => {
            if (e.target.files[0]) {
                await this.generateMindMapFromFile();
            }
        });

        document.getElementById('mindMapImageInput').addEventListener('change', async (e) => {
            if (e.target.files[0]) {
                await this.generateMindMapFromImage();
            }
        });

        // Mind Map File Removal
        document.getElementById('removeMindMapFile').addEventListener('click', () => {
            this.removeMindMapFile();
        });

        document.getElementById('removeMindMapImage').addEventListener('click', () => {
            this.removeMindMapImage();
        });

        // Mind Map Controls
        document.getElementById('zoomInBtn').addEventListener('click', () => {
            this.zoomMindMap(1.2);
        });

        document.getElementById('zoomOutBtn').addEventListener('click', () => {
            this.zoomMindMap(0.8);
        });

        document.getElementById('resetZoomBtn').addEventListener('click', () => {
            this.resetMindMapZoom();
        });

        document.getElementById('exportMindMapBtn').addEventListener('click', () => {
            this.exportMindMap();
        });

        // Content Screen Navigation
        document.getElementById('backToQuizGenerator').addEventListener('click', () => {
            this.showScreen('quizGeneratorScreen');
        });

        document.getElementById('backToHome').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // Text Content Generation
        document.getElementById('generateFromText').addEventListener('click', () => {
            this.generateFromText();
        });



        // Home language toggle
        document.getElementById('homeLanguageToggle').addEventListener('click', () => {
            if (window.toggleLanguage) {
                window.toggleLanguage();
                this.updateHomeLanguageToggle();
            }
        });

        // Image to PDF functionality is now handled by PDF Editor





        // Quiz actions
        document.getElementById('startQuizBtn').addEventListener('click', () => {
            this.startInteractiveQuiz();
        });

        document.getElementById('showAnswersBtn').addEventListener('click', () => {
            this.showQuestionsWithAnswers();
        });

        document.getElementById('exportQuestionsBtn').addEventListener('click', () => {
            this.exportQuestionsToPDF();
        });

        document.getElementById('submitAnswer').addEventListener('click', (e) => {
            console.log('Submit button clicked');
            e.preventDefault(); // Prevent any default behavior
            this.submitQuizAnswer();
        });

        document.getElementById('nextQuestion').addEventListener('click', () => {
            this.nextQuizQuestion();
        });

        document.getElementById('finishQuiz').addEventListener('click', () => {
            this.finishQuiz();
        });

        // Results actions
        document.getElementById('newQuizBtn').addEventListener('click', () => {
            this.showScreen('quizGeneratorScreen');
        });

        document.getElementById('reviewAnswersBtn').addEventListener('click', () => {
            this.reviewAnswers();
        });

        document.getElementById('saveQuizBtn').addEventListener('click', () => {
            this.saveQuizForLater();
        });





        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });

        document.getElementById('statsBtn').addEventListener('click', () => {
            this.showStatistics();
        });

        // Back to main menu buttons - removed, using floating home button instead

        // History and Statistics buttons
        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistoryScreen();
        });

        document.getElementById('statsBtn').addEventListener('click', () => {
            this.showStatisticsScreen();
        });

        // Header back button
        document.getElementById('headerBackBtn').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // Floating back button
        document.getElementById('floatingBackBtn').addEventListener('click', () => {
            // Check if we're in a quiz and need confirmation
            const currentScreen = document.querySelector('.screen.active')?.id;
            if (currentScreen === 'quizScreen') {
                this.confirmBackToMain('Are you sure you want to exit the quiz? Your progress will be lost.');
            } else {
                this.showScreen('homeScreen');
            }
        });

        // History screen navigation
        document.getElementById('backToMainFromHistory').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // Manual refresh buttons
        document.getElementById('refreshHistoryBtn').addEventListener('click', async () => {
            await this.refreshHistoryData();
        });

        document.getElementById('refreshStatsBtn').addEventListener('click', async () => {
            await this.refreshStatisticsData();
        });

        // Main menu buttons removed - using floating home button instead

        // Statistics screen navigation
        document.getElementById('backToMainFromStats').addEventListener('click', () => {
            this.showScreen('homeScreen');
        });

        // History actions
        document.getElementById('clearHistoryBtn').addEventListener('click', () => {
            this.clearHistory();
        });

        document.getElementById('refreshHistoryBtn').addEventListener('click', () => {
            this.loadHistory();
        });

        document.getElementById('refreshSavedQuizzesBtn').addEventListener('click', () => {
            this.loadSavedQuizzes();
        });

        document.getElementById('clearAllSavedQuizzesBtn').addEventListener('click', () => {
            this.clearAllSavedQuizzes();
        });

        // Pagination event listeners
        document.getElementById('savedQuizzesPrevBtn').addEventListener('click', () => {
            this.changeSavedQuizzesPage(-1);
        });

        document.getElementById('savedQuizzesNextBtn').addEventListener('click', () => {
            this.changeSavedQuizzesPage(1);
        });

        document.getElementById('historyPrevBtn').addEventListener('click', () => {
            this.changeHistoryPage(-1);
        });

        document.getElementById('historyNextBtn').addEventListener('click', () => {
            this.changeHistoryPage(1);
        });

        // History filters
        document.getElementById('historyTypeFilter').addEventListener('change', () => {
            this.filterHistory();
        });

        document.getElementById('historyDateFilter').addEventListener('change', () => {
            this.filterHistory();
        });

        // Debug: Add test save functionality (remove this later)
        if (document.getElementById('testSaveBtn')) {
            document.getElementById('testSaveBtn').addEventListener('click', async () => {
                console.log('Testing save functionality...');
                const testQuiz = {
                    id: 'test-' + Date.now(),
                    title: 'Test Quiz',
                    questionType: 'MCQ',
                    questions: [
                        { question: 'Test question?', answer: 'A', options: ['A', 'B', 'C', 'D'] }
                    ],
                    createdAt: new Date().toISOString(),
                    source: 'Test Source'
                };

                const result = await window.electronAPI.saveSavedQuiz(testQuiz);
                console.log('Test save result:', result);

                if (result.success) {
                    this.showNotification('Test quiz saved!', 'success');
                    await this.loadSavedQuizzes();
                } else {
                    this.showNotification('Test save failed: ' + result.error, 'error');
                }
            });
        }

        // Statistics actions
        document.getElementById('exportStatsBtn').addEventListener('click', () => {
            this.exportStatistics();
        });



        // Load settings immediately when event listeners are set up
        setTimeout(() => {
            this.initializeModelManagement();
        }, 100);
    }

    setupMenuListeners() {
        // Listen for menu events from main process
        window.electronAPI.onNewQuiz(() => {
            this.showScreen('quizGeneratorScreen');
        });

        window.electronAPI.onGenerateMCQ(() => {
            this.selectQuestionType('MCQ');
            this.selectInputMethod('text');
        });

        window.electronAPI.onGenerateTF(() => {
            this.selectQuestionType('TF');
            this.selectInputMethod('text');
        });

        window.electronAPI.onStartQuiz(() => {
            if (this.currentQuestions.length > 0) {
                this.startInteractiveQuiz();
            } else {
                this.showNotification('No questions available. Please generate questions first.', 'warning');
            }
        });



        window.electronAPI.onStatistics(() => {
            this.showStatistics();
        });

        window.electronAPI.onHistory(() => {
            this.showHistory();
        });

        window.electronAPI.onAbout(() => {
            this.showAbout();
        });

        window.electronAPI.onHelp(() => {
            this.showHelp();
        });

        window.electronAPI.onFileSelected((event, filePath) => {
            this.handleExternalFile(filePath);
        });
    }

    setupDragAndDrop() {
        // File drop zone - these may not exist if content screen was removed
        const fileDropZone = document.getElementById('fileDropZone');
        const imageDropZone = document.getElementById('imageDropZone');

        // Only set up drag and drop if the elements exist
        [fileDropZone, imageDropZone].filter(zone => zone).forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('dragover');
            });

            zone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                zone.classList.remove('dragover');
            });

            zone.addEventListener('drop', async (e) => {
                e.preventDefault();
                zone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    // In Electron, we can access the file path directly from dropped files
                    if (file.path) {
                        if (zone === fileDropZone) {
                            await this.processFile({ path: file.path });
                        } else {
                            // Check if it's an image
                            const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                            const isImage = imageExtensions.some(ext =>
                                file.path.toLowerCase().endsWith(ext)
                            );

                            if (isImage) {
                                await this.processFile({ path: file.path });
                            } else {
                                this.showNotification('Please drop an image file', 'warning');
                            }
                        }
                    } else {
                        this.showNotification('Unable to access file path', 'error');
                    }
                }
            });

            zone.addEventListener('click', async () => {
                if (zone === fileDropZone) {
                    await this.generateFromFile();
                } else {
                    await this.generateFromImage();
                }
            });
        });

        // Mind Map drop zones
        const mindMapFileDropZone = document.getElementById('mindMapFileDropZone');
        const mindMapImageDropZone = document.getElementById('mindMapImageDropZone');

        [mindMapFileDropZone, mindMapImageDropZone].forEach(zone => {
            if (!zone) return; // Skip if element doesn't exist

            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('dragover');
            });

            zone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                zone.classList.remove('dragover');
            });

            zone.addEventListener('drop', async (e) => {
                e.preventDefault();
                zone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    // In Electron, we can access the file path directly from dropped files
                    if (file.path) {
                        if (zone === mindMapFileDropZone) {
                            await this.processMindMapFile({ path: file.path });
                        } else {
                            // Check if it's an image
                            const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                            const isImage = imageExtensions.some(ext =>
                                file.path.toLowerCase().endsWith(ext)
                            );

                            if (isImage) {
                                await this.processMindMapFile({ path: file.path });
                            } else {
                                this.showNotification('Please drop an image file', 'warning');
                            }
                        }
                    } else {
                        this.showNotification('Unable to access file path', 'error');
                    }
                }
            });

            zone.addEventListener('click', async () => {
                if (zone === mindMapFileDropZone) {
                    await this.generateMindMapFromFile();
                } else {
                    await this.generateMindMapFromImage();
                }
            });
        });
    }

    setupQuestionCountListeners() {
        // File questions count input
        const fileQuestionsInput = document.getElementById('fileQuestionsCount');
        const imageQuestionsInput = document.getElementById('imageQuestionsCount');

        // Load saved values on startup - try multiple times to ensure it works
        this.loadQuestionCountSettings();
        setTimeout(() => {
            this.loadQuestionCountSettings();
        }, 500);
        setTimeout(() => {
            this.loadQuestionCountSettings();
        }, 1000);

        // Save when values change
        fileQuestionsInput.addEventListener('change', () => {
            this.saveQuestionCountSetting('questionsPerPage', parseInt(fileQuestionsInput.value));
        });

        imageQuestionsInput.addEventListener('change', () => {
            this.saveQuestionCountSetting('imageQuestionsCount', parseInt(imageQuestionsInput.value));
        });

        // Validate input ranges
        fileQuestionsInput.addEventListener('input', () => {
            const value = parseInt(fileQuestionsInput.value);
            if (value < 1) fileQuestionsInput.value = 1;
            if (value > 10) fileQuestionsInput.value = 10;
        });

        imageQuestionsInput.addEventListener('input', () => {
            const value = parseInt(imageQuestionsInput.value);
            if (value < 1) imageQuestionsInput.value = 1;
            if (value > 15) imageQuestionsInput.value = 15;
        });
    }

    async loadQuestionCountSettings() {
        try {
            if (!window.electronAPI || !window.electronAPI.getSettings) {
                console.warn('electronAPI not ready yet, will retry...');
                return;
            }

            const result = await window.electronAPI.getSettings();
            if (result.success && result.settings) {
                const fileInput = document.getElementById('fileQuestionsCount');
                const imageInput = document.getElementById('imageQuestionsCount');

                if (fileInput) {
                    fileInput.value = result.settings.questionsPerPage || 5;
                    console.log(`Loaded file questions per page: ${fileInput.value}`);
                }
                if (imageInput) {
                    imageInput.value = result.settings.imageQuestionsCount || 5;
                    console.log(`Loaded image questions count: ${imageInput.value}`);
                }

                // Update app settings
                this.appSettings = result.settings;
                console.log('Settings loaded successfully:', result.settings);
            } else {
                console.warn('Failed to load settings:', result);
            }
        } catch (error) {
            console.warn('Could not load question count settings:', error);
        }
    }

    async saveQuestionCountSetting(key, value) {
        try {
            const settings = { [key]: value };
            const result = await window.electronAPI.saveSettings(settings);
            if (result.success) {
                // Update app settings
                if (!this.appSettings) this.appSettings = {};
                this.appSettings[key] = value;

                this.showNotification(`${key === 'questionsPerPage' ? 'Questions per page' : 'Image questions'} updated to ${value}`, 'success');
            } else {
                this.showNotification('Failed to save setting', 'error');
            }
        } catch (error) {
            console.error('Error saving question count setting:', error);
            this.showNotification('Error saving setting', 'error');
        }
    }

    selectQuestionType(type) {
        this.selectedQuestionType = type;

        // Update UI
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.classList.remove('selected');
        });

        if (type === 'MCQ') {
            document.getElementById('mcqBtn').classList.add('selected');
        } else {
            document.getElementById('tfBtn').classList.add('selected');
        }

        this.showNotification(`Selected ${type} questions`, 'success');
    }

    selectInputMethod(method) {
        this.selectedInputMethod = method;

        if (!this.selectedQuestionType) {
            this.showNotification('Please select a question type first', 'warning');
            return;
        }

        if (method === 'text') {
            this.showScreen('contentScreen');

            // Update screen title
            const title = document.getElementById('contentScreenTitle');
            if (window.t) {
                // Use translations if available
                const questionType = this.selectedQuestionType === 'MCQ' ? window.t('multipleChoice') : window.t('trueFalse');
                title.textContent = `${window.t('addContent')} ${questionType}`;
            } else {
                title.textContent = `Add Content for ${this.selectedQuestionType} Questions`;
            }

            // Show text input area and focus
            document.getElementById('textInputArea').classList.add('active');
            document.getElementById('textContent').focus();
        }
    }

    selectMindMapInputMethod(method) {
        this.selectedMindMapInputMethod = method;

        // Show appropriate input area
        document.querySelectorAll('#textToolsScreen .input-area').forEach(area => {
            area.classList.remove('active');
        });

        switch (method) {
            case 'text':
                document.getElementById('mindMapTextInputArea').classList.add('active');
                document.getElementById('mindMapTextContent').focus();
                break;
            case 'file':
                document.getElementById('mindMapFileUploadArea').classList.add('active');
                break;
            case 'image':
                document.getElementById('mindMapImageUploadArea').classList.add('active');
                break;
        }

        // Update input method buttons
        document.querySelectorAll('#textToolsScreen .input-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const activeBtn = method === 'text' ? 'mindMapTextInputBtn' :
                         method === 'file' ? 'mindMapFileUploadBtn' : 'mindMapImageUploadBtn';
        document.getElementById(activeBtn).classList.add('active');
    }

    // Method to set active quiz generator tab
    setActiveQuizTab(tabName) {
        // Remove active class from all quiz nav buttons
        const quizNavButtons = document.querySelectorAll('.quiz-generator-nav .nav-btn');
        quizNavButtons.forEach(btn => btn.classList.remove('active'));

        // Add active class to selected tab
        const activeTab = document.getElementById(tabName + 'Tab');
        if (activeTab) {
            activeTab.classList.add('active');
        }
    }

    // Method to set active quiz generator tab on specific screen
    setActiveQuizTabOnScreen(tabName, screenId) {
        // Remove active class from all quiz nav buttons on the specific screen
        const screen = document.getElementById(screenId);
        if (screen) {
            const quizNavButtons = screen.querySelectorAll('.quiz-generator-nav .nav-btn');
            quizNavButtons.forEach(btn => btn.classList.remove('active'));

            // Add active class to selected tab on the specific screen
            const activeTab = screen.querySelector(`#${tabName}Tab${screenId === 'historyScreen' ? 'History' : 'Stats'}`);
            if (activeTab) {
                activeTab.classList.add('active');
            }
        }
    }

    showScreen(screenId) {
        console.log(`🎯 Attempting to show screen: ${screenId}`);

        // Hide all screens
        const allScreens = document.querySelectorAll('.screen');
        console.log(`🎯 Found ${allScreens.length} screens to hide`);
        allScreens.forEach(screen => {
            screen.classList.remove('active');
            console.log(`🎯 Hiding screen: ${screen.id}`);
        });

        // Show target screen
        const targetScreen = document.getElementById(screenId);
        console.log(`🎯 Looking for screen element: ${screenId}`);
        console.log(`🎯 Target screen found:`, !!targetScreen);

        if (targetScreen) {
            console.log(`🎯 Found screen element: ${screenId}`);
            targetScreen.classList.add('active');
            console.log(`🎯 Screen classes after adding active:`, targetScreen.className);
            this.currentScreen = screenId;

            // Update header visibility
            this.updateHeaderVisibility(screenId);

            // Manage header back button visibility
            this.updateHeaderBackButton(screenId);

            // Update dynamic translations for the new screen
            if (window.updateDynamicContent) {
                setTimeout(() => window.updateDynamicContent(), 100);
            }

            // Initialize PDF Editor if showing PDF Editor screen
            if (screenId === 'pdfEditorScreen') {
                setTimeout(() => {
                    this.switchPdfFeature('imageToPdf');
                }, 150);
            }

            // Initialize Text Tools if showing Text Tools screen
            if (screenId === 'textToolsScreen') {
                setTimeout(() => {
                    this.initializeTextTools();
                }, 150);
            }
        } else {
            console.error(`Screen element not found: ${screenId}`);
        }
    }

    updateHeaderVisibility(screenId) {
        const header = document.querySelector('.app-header');

        // Hide header on home screen and quiz generator related screens (which have their own navigation)
        const hideHeaderScreens = [
            'homeScreen',
            'quizGeneratorScreen',
            'historyScreen',
            'statisticsScreen'
        ];

        // Show header only on content and questions screens (legacy MCQ/TF screens)
        const showHeaderScreens = [
            'contentScreen',
            'questionsScreen'
        ];

        if (hideHeaderScreens.includes(screenId)) {
            header.style.display = 'none';
            document.body.classList.add('home-active');
            if (['quizGeneratorScreen', 'historyScreen', 'statisticsScreen'].includes(screenId)) {
                document.body.classList.add('quiz-generator-active');
            }
        } else if (showHeaderScreens.includes(screenId)) {
            header.style.display = 'block';
            document.body.classList.remove('home-active');
            document.body.classList.remove('quiz-generator-active');
        } else {
            header.style.display = 'none';
            document.body.classList.add('home-active');
            document.body.classList.remove('quiz-generator-active');
        }
    }

    updateHeaderBackButton(screenId) {
        const headerBackBtn = document.getElementById('headerBackBtn');
        const floatingBackBtn = document.getElementById('floatingBackBtn');

        // Show floating home button on ALL pages except home screen
        if (screenId === 'homeScreen') {
            headerBackBtn.style.display = 'none';
            floatingBackBtn.style.display = 'none';
        } else {
            headerBackBtn.style.display = 'none'; // Hide header back button (we only want floating)
            floatingBackBtn.style.display = 'flex'; // Show floating home button on all other screens
        }
    }

    updateHomeLanguageToggle() {
        const homeLanguageToggle = document.getElementById('homeLanguageToggle');
        if (homeLanguageToggle) {
            const languageText = homeLanguageToggle.querySelector('.language-text');
            if (languageText) {
                const currentLanguage = localStorage.getItem('language') || 'en';
                languageText.textContent = currentLanguage === 'en' ? 'عربي' : 'English';
            }
        }
    }

    showNotification(message, type = 'info') {
        const container = document.getElementById('notificationContainer');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    async generateFromText() {
        const textContent = document.getElementById('textContent').value.trim();

        if (!textContent) {
            this.showNotification('Please enter some text content', 'warning');
            return;
        }

        if (textContent.length < 50) {
            this.showNotification('Please enter more content (at least 50 characters)', 'warning');
            return;
        }

        await this.generateQuestions(textContent);
    }

    async generateFromFile() {
        try {
            // Use file selection dialog for desktop app
            const fileSelection = await window.electronAPI.selectFile();
            if (fileSelection.success) {
                await this.processFile({ path: fileSelection.filePath });
            } else {
                this.showNotification('No file selected', 'warning');
            }
        } catch (error) {
            console.error('Error selecting file:', error);
            this.showNotification('Error selecting file', 'error');
        }
    }

    async generateFromImage() {
        try {
            // Use file selection dialog for desktop app (images)
            const fileSelection = await window.electronAPI.selectFile();
            if (fileSelection.success) {
                // Check if it's an image file
                const filePath = fileSelection.filePath;
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                const isImage = imageExtensions.some(ext =>
                    filePath.toLowerCase().endsWith(ext)
                );

                if (isImage) {
                    await this.processFile({ path: filePath });
                } else {
                    this.showNotification('Please select an image file', 'warning');
                }
            } else {
                this.showNotification('No image selected', 'warning');
            }
        } catch (error) {
            console.error('Error selecting image:', error);
            this.showNotification('Error selecting image', 'error');
        }
    }

    // Mind Map Generation Methods
    async generateMindMapFromText() {
        const textContent = document.getElementById('mindMapTextContent').value.trim();
        console.log('generateMindMapFromText called with content length:', textContent.length);

        if (!textContent) {
            this.showNotification('Please enter some text content', 'warning');
            return;
        }

        if (textContent.length < 50) {
            this.showNotification('Please enter more content (at least 50 characters)', 'warning');
            return;
        }

        console.log('Starting mind map generation from text...');

        // Test with a simple mind map first
        if (textContent.toLowerCase().includes('test')) {
            console.log('Test mode detected, creating simple test mind map...');
            this.testMindMapDisplay();
            return;
        }

        await this.generateMindMap(textContent);
    }

    testMindMapDisplay() {
        console.log('🧪 Creating SIMPLE test mind map...');

        // Get the container directly
        const container = document.getElementById('mindMapContainer');
        if (!container) {
            console.error('❌ Mind map container not found!');
            return;
        }

        // Create super simple test HTML
        const testHTML = `
            <div style="
                padding: 30px;
                background: #1e1e1e;
                color: white;
                border-radius: 10px;
                border: 2px solid #4CAF50;
                text-align: center;
                min-height: 300px;
            ">
                <h1 style="color: #4CAF50; margin-bottom: 20px;">🧠 TEST MIND MAP</h1>
                <p style="font-size: 18px; margin-bottom: 30px;">This is a simple test to verify display works</p>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px;">
                    <div style="background: #333; padding: 20px; border-radius: 8px; border-left: 4px solid #2196F3;">
                        <h3 style="color: #2196F3; margin: 0 0 10px 0;">Topic 1</h3>
                        <p style="margin: 0; color: #ccc;">This is the first test topic</p>
                    </div>
                    <div style="background: #333; padding: 20px; border-radius: 8px; border-left: 4px solid #FF9800;">
                        <h3 style="color: #FF9800; margin: 0 0 10px 0;">Topic 2</h3>
                        <p style="margin: 0; color: #ccc;">This is the second test topic</p>
                    </div>
                </div>

                <p style="margin-top: 30px; color: #4CAF50; font-weight: bold;">
                    ✅ If you can see this, the display system works!
                </p>
            </div>
        `;

        // Add test directly - NO CLEARING
        container.innerHTML = testHTML;

        console.log('✅ Simple test mind map created!');
        console.log('Container innerHTML length:', container.innerHTML.length);

        // Force show the text tools screen
        this.showScreen('textToolsScreen');

        console.log('✅ Test mind map should now be visible!');
    }

    createPersistentTest() {
        console.log('🔒 Creating PERSISTENT test mind map...');

        const container = document.getElementById('mindMapContainer');
        if (!container) {
            console.error('❌ Container not found!');
            return;
        }

        // Create a very simple, persistent test
        const persistentHTML = `
            <div id="persistentMindMap" style="
                padding: 40px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 15px;
                text-align: center;
                min-height: 400px;
                position: relative;
            ">
                <h1 style="color: white; margin-bottom: 20px; font-size: 28px;">
                    🧠 PERSISTENT MIND MAP TEST
                </h1>
                <p style="font-size: 16px; margin-bottom: 40px; opacity: 0.9;">
                    This test should stay visible and not disappear
                </p>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 10px;">
                        <h3 style="color: #FFD700; margin: 0 0 10px 0;">✨ Topic A</h3>
                        <p style="margin: 0; font-size: 14px;">First test topic with some content</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 10px;">
                        <h3 style="color: #FF6B6B; margin: 0 0 10px 0;">🚀 Topic B</h3>
                        <p style="margin: 0; font-size: 14px;">Second test topic with more content</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 10px;">
                        <h3 style="color: #4ECDC4; margin: 0 0 10px 0;">💡 Topic C</h3>
                        <p style="margin: 0; font-size: 14px;">Third test topic to verify layout</p>
                    </div>
                </div>

                <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%);
                           background: rgba(0,0,0,0.3); padding: 10px 20px; border-radius: 20px;">
                    <span style="color: #4CAF50; font-weight: bold;">✅ DISPLAY SYSTEM WORKING!</span>
                </div>
            </div>
        `;

        // Set the content
        container.innerHTML = persistentHTML;

        // Make sure we're on the right screen
        this.showScreen('textToolsScreen');

        // Mark this as a test so we don't clear it
        container.setAttribute('data-test-mode', 'true');

        console.log('✅ Persistent test created!');
        console.log('📏 Container content length:', container.innerHTML.length);
    }

    async generateMindMapFromFile() {
        try {
            // Use file selection dialog for desktop app
            const fileSelection = await window.electronAPI.selectFile();
            if (fileSelection.success) {
                await this.processMindMapFile({ path: fileSelection.filePath });
            } else {
                this.showNotification('No file selected', 'warning');
            }
        } catch (error) {
            console.error('Error selecting file:', error);
            this.showNotification('Error selecting file', 'error');
        }
    }

    async generateMindMapFromImage() {
        try {
            // Use file selection dialog for desktop app (images)
            const fileSelection = await window.electronAPI.selectFile();
            if (fileSelection.success) {
                // Check if it's an image file
                const filePath = fileSelection.filePath;
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                const isImage = imageExtensions.some(ext =>
                    filePath.toLowerCase().endsWith(ext)
                );

                if (isImage) {
                    await this.processMindMapFile({ path: filePath });
                } else {
                    this.showNotification('Please select an image file', 'warning');
                }
            } else {
                this.showNotification('No image selected', 'warning');
            }
        } catch (error) {
            console.error('Error selecting image:', error);
            this.showNotification('Error selecting image', 'error');
        }
    }

    async processMindMapFile(file) {
        console.log('🧠 processMindMapFile called with:', file);
        try {
            this.showProcessingScreen();

            // Update processing screen for mind map generation
            this.updateProcessingScreenForMindMap();

            this.updateProgress(10, 'Analyzing file structure...', '~30 seconds');

            // For desktop app, we need to use the file selection dialog
            let filePath;

            if (file && file.path) {
                // If file has path (from drag & drop or external selection)
                filePath = file.path;
            } else {
                // Use file selection dialog
                const fileSelection = await window.electronAPI.selectFile();
                if (fileSelection.success) {
                    filePath = fileSelection.filePath;
                } else {
                    throw new Error('No file selected');
                }
            }

            // Process file through main process
            const result = await window.electronAPI.processFile(filePath);

            if (result.success) {
                this.updateProgress(50, 'Extracting content for mind map...', '~20 seconds');

                // Detect if this is an image file
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                const isImage = imageExtensions.some(ext =>
                    filePath.toLowerCase().endsWith(ext)
                );

                // Generate mind map directly without showing processing screen again
                this.updateProgress(70, 'Generating mind map structure...', '~15 seconds');

                console.log('🚀 Calling generateMindMap API with text length:', result.text.length, 'isImage:', isImage);

                let mindMapData;
                try {
                    mindMapData = await window.electronAPI.generateMindMap(result.text, isImage);
                    console.log('✅ Received mind map data from API:', mindMapData);
                    console.log('📊 Mind map data type:', typeof mindMapData);
                    console.log('🔑 Mind map data keys:', mindMapData ? Object.keys(mindMapData) : 'null');

                    if (!mindMapData) {
                        console.error('❌ Mind map data is null or undefined!');
                        throw new Error('No mind map data received from API');
                    }

                    if (!mindMapData.title) {
                        console.warn('⚠️ Mind map data missing title:', mindMapData);
                    }

                    if (!mindMapData.nodes) {
                        console.warn('⚠️ Mind map data missing nodes:', mindMapData);
                    }

                } catch (apiError) {
                    console.error('❌ Error calling generateMindMap API:', apiError);
                    throw apiError;
                }

                this.updateProgress(90, 'Creating mind map visualization...', '~5 seconds');

                console.log('🎨 About to display mind map...');
                console.log('🎨 Mind map data being passed to display:', JSON.stringify(mindMapData, null, 2));
                this.displayMindMap(mindMapData);
                console.log('✅ Mind map display method completed');

                this.updateProgress(100, 'Mind map generated successfully!', '');

                setTimeout(() => {
                    this.hideProcessingScreen();
                    this.showScreen('textToolsScreen');
                }, 800);
            } else {
                throw new Error(result.error || 'Failed to process file');
            }
        } catch (error) {
            console.error('Error processing file for mind map:', error);
            this.hideProcessingScreen();
            this.showNotification(`Error processing file: ${error.message}`, 'error');
        }
    }

    async generateMindMap(content, isScanned = false) {
        try {
            this.showProcessingScreen();

            // Update processing screen for mind map generation
            this.updateProcessingScreenForMindMap();

            this.updateProgress(20, 'Analyzing content structure...', '~15 seconds');

            // Generate mind map using the API
            console.log('Calling generateMindMap API...');
            const mindMapData = await window.electronAPI.generateMindMap(content, isScanned);
            console.log('Received mind map data:', mindMapData);
            console.log('Mind map data structure:', JSON.stringify(mindMapData, null, 2));

            this.updateProgress(80, 'Creating mind map visualization...', '~5 seconds');

            // Display the mind map
            console.log('Displaying mind map...');
            this.displayMindMap(mindMapData);

            this.updateProgress(100, 'Mind map generated successfully!', '');

            // Hide processing screen and show text tools screen
            setTimeout(() => {
                console.log('Hiding processing screen and showing text tools screen...');
                this.hideProcessingScreen();
                this.showScreen('textToolsScreen');

                // Ensure the mind map container is visible and scrolled into view
                setTimeout(() => {
                    const container = document.getElementById('mindMapContainer');
                    if (container && container.getAttribute('data-has-mindmap') === 'true') {
                        container.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        console.log('Mind map container scrolled into view');
                        console.log('✅ Mind map should now be visible with title:', container.getAttribute('data-mindmap-title'));
                    } else {
                        console.warn('⚠️ Mind map container not found or no mind map data');
                    }
                }, 100);

                console.log('Text tools screen should now be visible');
            }, 800);

        } catch (error) {
            console.error('Error generating mind map:', error);
            this.hideProcessingScreen();
            this.showNotification(`Error generating mind map: ${error.message}`, 'error');
        }
    }

    updateProcessingScreenForMindMap() {
        // Update the processing screen title and status for mind map generation
        const titleElement = document.querySelector('.processing-title .title-gradient');
        const statusElement = document.getElementById('processingStatus');

        if (titleElement) {
            if (window.t) {
                titleElement.textContent = window.t('aiMindMapGenerator');
            } else {
                titleElement.textContent = 'AI Mind Map Generator';
            }
        }

        if (statusElement) {
            if (window.t) {
                statusElement.textContent = window.t('initializingAiModelsForMindMap');
            } else {
                statusElement.textContent = 'Initializing AI models for mind map generation...';
            }
        }

        // Update processing steps for mind map
        const steps = [
            {
                id: 'step1',
                icon: 'fas fa-file-text',
                text: window.t ? window.t('analyzingContent') : 'Analyzing Content'
            },
            {
                id: 'step2',
                icon: 'fas fa-brain',
                text: window.t ? window.t('processingAiModels') : 'Processing AI Models'
            },
            {
                id: 'step3',
                icon: 'fas fa-project-diagram',
                text: window.t ? window.t('generatingMindMap') : 'Generating Mind Map'
            },
            {
                id: 'step4',
                icon: 'fas fa-check-circle',
                text: window.t ? window.t('finalizing') : 'Finalizing'
            }
        ];

        steps.forEach(step => {
            const stepElement = document.getElementById(step.id);
            if (stepElement) {
                const iconElement = stepElement.querySelector('.step-icon i');
                const textElement = stepElement.querySelector('.step-text');

                if (iconElement) {
                    iconElement.className = step.icon;
                }
                if (textElement) {
                    textElement.textContent = step.text;
                }
            }
        });
    }

    displayMindMap(mindMapData) {
        try {
            console.log('displayMindMap called with data:', mindMapData);

            const container = document.getElementById('mindMapContainer');
            console.log('Mind map container found:', !!container);

            if (!container) {
                console.error('Mind map container not found!');
                return;
            }

            // REMOVED ALL CLEARING - Never clear the container
            console.log('📌 Keeping existing content - no clearing allowed');
            console.log('Container current content length:', container.innerHTML.length, 'characters');

            // Create mind map visualization
            console.log('Creating mind map visualization...');
            this.createMindMapVisualization(container, mindMapData);
            console.log('Mind map visualization created');

            // Enable export button
            const exportBtn = document.getElementById('exportMindMapBtn');
            if (exportBtn) {
                exportBtn.disabled = false;
                console.log('Export button enabled');
            } else {
                console.log('Export button not found');
            }

            // Store mind map data for export
            this.currentMindMapData = mindMapData;
            console.log('Mind map data stored for export');

        } catch (error) {
            console.error('Error displaying mind map:', error);
            this.showNotification('Error displaying mind map', 'error');
        }
    }

    createMindMapVisualization(container, mindMapData) {
        console.log('🎨 Creating professional study-friendly mind map...');

        // Always use 2x2 grid layout
        const gridColumns = 'repeat(2, 1fr)';

        // Professional box-based mind map perfect for students
        const mindMapHTML = `
            <div class="mind-map-visualization" style="
                padding: 30px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                min-height: 600px;
                position: relative;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                transition: transform 0.3s ease;
                transform-origin: center top;
            ">
                <!-- Main Title Card -->
                <div style="
                    background: white;
                    border-radius: 15px;
                    padding: 25px;
                    text-align: center;
                    margin-bottom: 40px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    border-left: 6px solid #ff6b35;
                ">
                    <h1 style="
                        color: #2c3e50;
                        font-size: 28px;
                        font-weight: bold;
                        margin: 0 0 10px 0;
                    ">${mindMapData.title || 'Study Mind Map'}</h1>
                    <p style="
                        color: #7f8c8d;
                        font-size: 16px;
                        margin: 0;
                        font-style: italic;
                    ">${mindMapData.description || 'Key concepts and relationships'}</p>
                </div>

                <!-- Topics Grid -->
                <div style="
                    display: grid;
                    grid-template-columns: ${gridColumns};
                    gap: 20px;
                    margin-bottom: 30px;
                    justify-content: center;
                ">
                    ${mindMapData.nodes.map((node, index) => {
                        const colors = [
                            { bg: '#3498db', accent: '#2980b9' },
                            { bg: '#2ecc71', accent: '#27ae60' },
                            { bg: '#e74c3c', accent: '#c0392b' },
                            { bg: '#9b59b6', accent: '#8e44ad' },
                            { bg: '#f39c12', accent: '#e67e22' },
                            { bg: '#1abc9c', accent: '#16a085' }
                        ];
                        const color = colors[index % colors.length];

                        return `
                            <div style="
                                background: white;
                                border-radius: 12px;
                                overflow: visible;
                                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                                transition: transform 0.3s ease;
                                border-top: 4px solid ${color.bg};
                                display: flex;
                                flex-direction: column;
                                height: 100%;
                            ">
                                <!-- Topic Header -->
                                <div style="
                                    background: linear-gradient(135deg, ${color.bg} 0%, ${color.accent} 100%);
                                    padding: 12px 15px;
                                    color: white;
                                    flex-shrink: 0;
                                ">
                                    <h2 style="
                                        margin: 0;
                                        font-size: 16px;
                                        font-weight: bold;
                                        display: flex;
                                        align-items: center;
                                        gap: 8px;
                                    ">
                                        <span style="
                                            background: rgba(255,255,255,0.2);
                                            border-radius: 50%;
                                            width: 24px;
                                            height: 24px;
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            font-size: 12px;
                                            font-weight: bold;
                                        ">${index + 1}</span>
                                        ${node.label}
                                    </h2>
                                    <p style="
                                        margin: 6px 0 0 0;
                                        font-size: 12px;
                                        opacity: 0.9;
                                        line-height: 1.3;
                                    ">${node.description || ''}</p>
                                </div>

                                <!-- Subtopics -->
                                ${node.children && node.children.length > 0 ? `
                                    <div style="padding: 12px 15px; flex: 1;">
                                        <h3 style="
                                            color: ${color.bg};
                                            font-size: 14px;
                                            margin: 0 0 10px 0;
                                            font-weight: bold;
                                            border-bottom: 2px solid ${color.bg};
                                            padding-bottom: 3px;
                                        ">Key Points:</h3>

                                        <div style="
                                            display: grid;
                                            gap: 6px;
                                        ">
                                            ${node.children.map((child, childIndex) => `
                                                <div style="
                                                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                                                    border-radius: 8px;
                                                    padding: 10px 12px;
                                                    border-left: 3px solid ${color.bg};
                                                    position: relative;
                                                ">
                                                    <div style="
                                                        position: absolute;
                                                        top: -3px;
                                                        left: -3px;
                                                        background: ${color.bg};
                                                        color: white;
                                                        border-radius: 50%;
                                                        width: 16px;
                                                        height: 16px;
                                                        display: flex;
                                                        align-items: center;
                                                        justify-content: center;
                                                        font-size: 9px;
                                                        font-weight: bold;
                                                    ">${childIndex + 1}</div>

                                                    <h4 style="
                                                        color: #2c3e50;
                                                        font-size: 13px;
                                                        margin: 0 0 5px 0;
                                                        font-weight: bold;
                                                    ">${child.label}</h4>

                                                    <p style="
                                                        color: #5a6c7d;
                                                        font-size: 12px;
                                                        margin: 0;
                                                        line-height: 1.3;
                                                    ">${child.description || ''}</p>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                ` : `
                                    <div style="
                                        padding: 20px;
                                        text-align: center;
                                        color: #7f8c8d;
                                        font-style: italic;
                                    ">
                                        No additional details available
                                    </div>
                                `}
                            </div>
                        `;
                    }).join('')}
                </div>

                <!-- Study Tips Footer -->
                <div style="
                    background: rgba(255,255,255,0.1);
                    border-radius: 15px;
                    padding: 20px;
                    text-align: center;
                    backdrop-filter: blur(10px);
                ">
                    <p style="
                        color: white;
                        margin: 0;
                        font-size: 14px;
                        opacity: 0.9;
                    ">💡 <strong>Study Tip:</strong> Review each numbered section systematically and connect related concepts across different topics</p>
                </div>
            </div>
        `;

        // Set the HTML directly
        container.innerHTML = mindMapHTML;

        // Mark container as having valid content
        container.setAttribute('data-has-mindmap', 'true');
        container.setAttribute('data-mindmap-title', mindMapData.title || 'Mind Map');

        console.log('✅ Professional study mind map created!');
    }

    renderMindMapNodes(container, nodes, level = 1) {
        try {
            console.log(`renderMindMapNodes called with ${nodes?.length || 0} nodes at level ${level}`);

            if (!nodes || !Array.isArray(nodes)) {
                console.warn('Invalid nodes array:', nodes);
                return;
            }

            nodes.forEach((node, index) => {
                try {
                    console.log(`Rendering node ${index}:`, node);

                    const nodeDiv = document.createElement('div');
                    nodeDiv.className = `mind-map-node level-${level}`;
                    nodeDiv.setAttribute('data-node-id', node.id || `node-${level}-${index}`);

                    // Node content
                    const nodeContent = document.createElement('div');
                    nodeContent.className = 'node-content';

                    const nodeLabel = document.createElement('div');
                    nodeLabel.className = 'node-label';
                    nodeLabel.textContent = node.label || `Node ${index + 1}`;
                    nodeContent.appendChild(nodeLabel);

                    if (node.description) {
                        const nodeDesc = document.createElement('div');
                        nodeDesc.className = 'node-description';
                        nodeDesc.textContent = node.description;
                        nodeContent.appendChild(nodeDesc);
                    }

                    nodeDiv.appendChild(nodeContent);

                    // Render children if any
                    if (node.children && Array.isArray(node.children) && node.children.length > 0) {
                        console.log(`Node has ${node.children.length} children`);
                        const childrenContainer = document.createElement('div');
                        childrenContainer.className = 'node-children';
                        this.renderMindMapNodes(childrenContainer, node.children, level + 1);
                        nodeDiv.appendChild(childrenContainer);
                    }

                    container.appendChild(nodeDiv);
                    console.log(`Node ${index} rendered successfully`);

                } catch (nodeError) {
                    console.error(`Error rendering node ${index}:`, nodeError, node);

                    // Create a fallback node
                    const errorNode = document.createElement('div');
                    errorNode.className = `mind-map-node level-${level} error-node`;
                    errorNode.innerHTML = `<div class="node-content"><div class="node-label">Error rendering node</div></div>`;
                    container.appendChild(errorNode);
                }
            });

            console.log(`Finished rendering ${nodes.length} nodes at level ${level}`);

        } catch (error) {
            console.error('Error in renderMindMapNodes:', error);
        }
    }

    initializeTextTools() {
        // Set default input method to text
        this.selectMindMapInputMethod('text');

        // Reset mind map zoom
        this.currentMindMapScale = 1;

        // REMOVED: Don't clear mind map data or container
        // The mind map should persist when switching screens

        // Update translations if available
        if (window.updateDynamicContent) {
            setTimeout(() => window.updateDynamicContent(), 100);
        }
    }

    // Mind Map Zoom and Export Methods
    zoomMindMap(factor) {
        const visualization = document.querySelector('.mind-map-visualization');
        if (!visualization) {
            this.showNotification('No mind map to zoom', 'warning');
            return;
        }

        const currentScale = this.currentMindMapScale || 1;
        const newScale = Math.max(0.5, Math.min(2, currentScale * factor));

        visualization.style.transform = `scale(${newScale})`;
        visualization.style.transformOrigin = 'center top';

        this.currentMindMapScale = newScale;

        // Show zoom level feedback
        const zoomPercentage = Math.round(newScale * 100);
        this.showNotification(`Zoom: ${zoomPercentage}%`, 'info', 1000);
    }

    resetMindMapZoom() {
        const visualization = document.querySelector('.mind-map-visualization');
        if (!visualization) {
            this.showNotification('No mind map to reset zoom', 'warning');
            return;
        }

        visualization.style.transform = 'scale(1)';
        this.currentMindMapScale = 1;

        // Show reset feedback
        this.showNotification('Zoom reset to 100%', 'success', 1000);
    }

    async exportMindMap() {
        if (!this.currentMindMapData) {
            this.showNotification('No mind map to export', 'warning');
            return;
        }

        try {
            console.log('📄 Starting mind map PDF export...');

            // Show loading state on export button
            const exportBtn = document.getElementById('exportMindMapBtn');
            const originalContent = exportBtn.innerHTML;
            exportBtn.disabled = true;
            exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            this.showNotification('Preparing mind map PDF...', 'info');

            // Show save dialog for PDF
            const result = await window.electronAPI.saveFile({
                title: 'Save Mind Map as PDF',
                defaultPath: `mindmap_${(this.currentMindMapData.title || 'study_map').replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.success && result.filePath) {
                // Generate PDF-optimized HTML content
                const pdfContent = this.generateMindMapPDFContent(this.currentMindMapData);

                // Save PDF through main process
                const saveResult = await window.electronAPI.savePDF(result.filePath, pdfContent);

                if (saveResult.success) {
                    this.showNotification(`Mind map PDF saved successfully!`, 'success');

                    // Ask if user wants to open the PDF
                    const openResult = await window.electronAPI.showMessageBox({
                        type: 'question',
                        buttons: ['Open PDF', 'Open Folder', 'Close'],
                        defaultId: 0,
                        title: 'Mind Map PDF Created',
                        message: 'Your mind map has been saved as PDF successfully. Would you like to open it?'
                    });

                    if (openResult.response === 0) {
                        // Open the PDF file
                        await window.electronAPI.openExternal(result.filePath);
                    } else if (openResult.response === 1) {
                        // Open the folder containing the PDF
                        const outputDir = result.filePath.substring(0, result.filePath.lastIndexOf('\\') || result.filePath.lastIndexOf('/'));
                        await window.electronAPI.openExternal(outputDir);
                    }
                } else {
                    throw new Error(saveResult.error || 'Failed to save PDF');
                }
            } else if (result.canceled) {
                this.showNotification('PDF export canceled', 'info');
            } else {
                throw new Error('Failed to select save location');
            }
        } catch (error) {
            console.error('Mind map PDF export error:', error);
            this.showNotification(`PDF export failed: ${error.message}`, 'error');
        } finally {
            // Restore export button
            const exportBtn = document.getElementById('exportMindMapBtn');
            if (exportBtn) {
                exportBtn.disabled = false;
                exportBtn.innerHTML = originalContent;
            }
        }
    }

    generateMindMapPDFContent(mindMapData) {
        console.log('📄 Generating PDF-optimized mind map content...');

        // Create PDF-optimized HTML with single page layout
        const pdfHTML = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>${mindMapData.title || 'Mind Map'}</title>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        padding: 15px;
                        height: 100vh;
                        max-height: 100vh;
                        overflow: hidden;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }

                    .container {
                        background: white;
                        border-radius: 15px;
                        padding: 20px;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                        page-break-inside: avoid;
                        height: calc(100vh - 30px);
                        max-height: calc(100vh - 30px);
                        overflow: hidden;
                        display: flex;
                        flex-direction: column;
                    }

                    .header {
                        text-align: center;
                        margin-bottom: 15px;
                        padding-bottom: 10px;
                        border-bottom: 3px solid #ff6b35;
                        flex-shrink: 0;
                    }

                    .main-title {
                        color: #2c3e50;
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 5px;
                    }

                    .description {
                        color: #7f8c8d;
                        font-size: 14px;
                        font-style: italic;
                    }

                    .topics-grid {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        gap: 12px;
                        margin-bottom: 10px;
                        flex: 1;
                    }

                    .topic-card {
                        background: white;
                        border-radius: 12px;
                        overflow: hidden;
                        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                        border-top: 4px solid var(--topic-color);
                        page-break-inside: avoid;
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                        min-height: 300px;
                        max-height: 400px;
                    }

                    .topic-header {
                        background: linear-gradient(135deg, var(--topic-color) 0%, var(--topic-accent) 100%);
                        padding: 12px 15px;
                        color: white;
                        flex-shrink: 0;
                    }

                    .topic-number {
                        background: rgba(255,255,255,0.2);
                        border-radius: 50%;
                        width: 25px;
                        height: 25px;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 12px;
                        font-weight: bold;
                        margin-right: 10px;
                    }

                    .topic-title {
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 8px;
                    }

                    .topic-desc {
                        font-size: 13px;
                        opacity: 0.9;
                        line-height: 1.3;
                    }

                    .subtopics {
                        padding: 12px 15px;
                        flex: 1;
                        overflow-y: auto;
                    }

                    .subtopics-title {
                        color: var(--topic-color);
                        font-size: 14px;
                        font-weight: bold;
                        margin-bottom: 10px;
                        border-bottom: 2px solid var(--topic-color);
                        padding-bottom: 3px;
                    }

                    .subtopic-item {
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        border-radius: 8px;
                        padding: 10px 12px;
                        margin-bottom: 6px;
                        border-left: 3px solid var(--topic-color);
                        position: relative;
                    }

                    .subtopic-number {
                        position: absolute;
                        top: -3px;
                        left: -3px;
                        background: var(--topic-color);
                        color: white;
                        border-radius: 50%;
                        width: 16px;
                        height: 16px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 9px;
                        font-weight: bold;
                    }

                    .subtopic-title {
                        color: #2c3e50;
                        font-size: 13px;
                        font-weight: bold;
                        margin-bottom: 5px;
                    }

                    .subtopic-desc {
                        color: #5a6c7d;
                        font-size: 12px;
                        line-height: 1.3;
                    }

                    .footer {
                        text-align: center;
                        margin-top: 20px;
                        padding-top: 15px;
                        border-top: 2px solid #e9ecef;
                        color: #7f8c8d;
                        font-size: 12px;
                    }

                    @media print {
                        @page {
                            size: A4;
                            margin: 0.5in;
                        }
                        body {
                            margin: 0;
                            padding: 0;
                            font-size: 10px;
                            height: 100vh;
                            overflow: hidden;
                        }
                        .container {
                            box-shadow: none;
                            padding: 8px;
                            height: 100vh;
                            max-height: 100vh;
                            overflow: hidden;
                        }
                        .header {
                            margin-bottom: 8px;
                            padding-bottom: 5px;
                        }
                        .main-title {
                            font-size: 18px;
                            margin-bottom: 3px;
                        }
                        .description {
                            font-size: 12px;
                        }
                        .topics-grid {
                            gap: 8px;
                            grid-template-columns: repeat(2, 1fr);
                        }
                        .topic-card {
                            page-break-inside: avoid;
                            overflow: hidden;
                        }
                        .topic-header {
                            padding: 6px 10px;
                        }
                        .topic-title {
                            font-size: 14px;
                        }
                        .topic-desc {
                            font-size: 10px;
                        }
                        .subtopics {
                            padding: 6px 10px;
                        }
                        .subtopics-title {
                            font-size: 12px;
                            margin-bottom: 5px;
                        }
                        .subtopic-item {
                            padding: 4px 6px;
                            margin-bottom: 3px;
                        }
                        .subtopic-title {
                            font-size: 11px;
                        }
                        .subtopic-desc {
                            font-size: 10px;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1 class="main-title">${mindMapData.title || 'Study Mind Map'}</h1>
                        <p class="description">${mindMapData.description || 'Key concepts and relationships for effective learning'}</p>
                    </div>

                    <div class="topics-grid">
                        ${mindMapData.nodes.map((node, index) => {
                            const colors = [
                                { color: '#3498db', accent: '#2980b9' },
                                { color: '#2ecc71', accent: '#27ae60' },
                                { color: '#e74c3c', accent: '#c0392b' },
                                { color: '#9b59b6', accent: '#8e44ad' },
                                { color: '#f39c12', accent: '#e67e22' },
                                { color: '#1abc9c', accent: '#16a085' }
                            ];
                            const colorSet = colors[index % colors.length];

                            return `
                                <div class="topic-card" style="--topic-color: ${colorSet.color}; --topic-accent: ${colorSet.accent};">
                                    <div class="topic-header">
                                        <div class="topic-title">
                                            <span class="topic-number">${index + 1}</span>
                                            ${node.label}
                                        </div>
                                        ${node.description ? `<div class="topic-desc">${node.description}</div>` : ''}
                                    </div>

                                    ${node.children && node.children.length > 0 ? `
                                        <div class="subtopics">
                                            <div class="subtopics-title">Key Points:</div>
                                            ${node.children.map((child, childIndex) => `
                                                <div class="subtopic-item">
                                                    <div class="subtopic-number">${childIndex + 1}</div>
                                                    <div class="subtopic-title">${child.label}</div>
                                                    ${child.description ? `<div class="subtopic-desc">${child.description}</div>` : ''}
                                                </div>
                                            `).join('')}
                                        </div>
                                    ` : `
                                        <div class="subtopics">
                                            <div style="text-align: center; color: #7f8c8d; font-style: italic; padding: 10px;">
                                                No additional details available
                                            </div>
                                        </div>
                                    `}
                                </div>
                            `;
                        }).join('')}
                    </div>

                    <div class="footer">
                        <p>💡 <strong>Study Tip:</strong> Review each numbered section systematically and connect related concepts across different topics</p>
                        <p style="margin-top: 5px;">Generated on ${new Date().toLocaleDateString()} • Mind Map Study Guide</p>
                    </div>
                </div>
            </body>
            </html>
        `;

        console.log('✅ PDF content generated successfully');
        return pdfHTML;
    }

    async processFile(file) {
        try {
            this.showProcessingScreen();
            this.updateProgress(10, 'Analyzing file structure...', '~30 seconds');

            // For desktop app, we need to use the file selection dialog
            // since we can't access file.path directly from the file input
            let filePath;

            if (file && file.path) {
                // If file has path (from drag & drop or external selection)
                filePath = file.path;
            } else {
                // Use file selection dialog
                const fileSelection = await window.electronAPI.selectFile();
                if (fileSelection.success) {
                    filePath = fileSelection.filePath;
                } else {
                    throw new Error('No file selected');
                }
            }

            // Process file through main process
            const result = await window.electronAPI.processFile(filePath);

            if (result.success) {
                this.updateProgress(50, 'Extracting content with AI...', '~20 seconds');

                // Detect if this is an image file
                const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
                const isImage = imageExtensions.some(ext =>
                    filePath.toLowerCase().endsWith(ext)
                );

                // Store page count for per-page calculation
                const pageCount = result.pageCount || 1;
                console.log(`Document has ${pageCount} pages`);

                await this.generateQuestions(result.text, isImage, pageCount);
            } else {
                throw new Error(result.error || 'Failed to process file');
            }
        } catch (error) {
            console.error('File processing error:', error);
            this.showNotification(`Error processing file: ${error.message}`, 'error');
            // For file/image errors, go back to quiz generator since we removed those upload pages
            this.showScreen('quizGeneratorScreen');
        }
    }

    async generateQuestions(content, isImage = false, pageCount = 1) {
        try {
            // Store parameters for retry functionality
            this.lastGenerationParams = { content, isImage, pageCount };

            this.showProcessingScreen();

            // Get question count from the UI inputs or app settings FIRST
            let questionsPerPage = 5; // Default fallback
            let totalQuestionCount = 15; // Default fallback

            if (isImage) {
                // For images, use the image questions count (total for the image)
                const globalImageInput = document.getElementById('globalImageQuestionsCount');
                const inputValue = globalImageInput ? parseInt(globalImageInput.value) : null;
                const settingsValue = this.appSettings?.imageQuestionsCount || null;
                totalQuestionCount = inputValue || settingsValue || 5;

                console.log(`🖼️ IMAGE QUESTION COUNT DEBUG:`);
                console.log(`   - Global input element value: ${inputValue}`);
                console.log(`   - Settings value: ${settingsValue}`);
                console.log(`   - Final count: ${totalQuestionCount}`);
                console.log(`   - App settings object:`, this.appSettings);
                console.log(`Generating ${totalQuestionCount} questions total for image`);
            } else {
                // For files and text, use the per-page questions count and multiply by page count
                const globalFileInput = document.getElementById('globalFileQuestionsCount');
                const inputValue = globalFileInput ? parseInt(globalFileInput.value) : null;
                const settingsValue = this.appSettings?.questionsPerPage || null;
                questionsPerPage = inputValue || settingsValue || 5;
                totalQuestionCount = questionsPerPage * pageCount;

                // Apply maximum limit to prevent excessive question generation (silent)
                const maxQuestions = 30; // Maximum for any document
                if (totalQuestionCount > maxQuestions) {
                    totalQuestionCount = maxQuestions;
                }

                console.log(`Generating ${questionsPerPage} questions per page × ${pageCount} pages = ${totalQuestionCount} total questions`);
            }

            // Now update progress with the calculated values
            if (isImage) {
                const estimatedTime = totalQuestionCount <= 5 ? '~10 seconds' : '~15 seconds';
                this.updateProgress(60, `AI generating ${totalQuestionCount} questions from image...`, estimatedTime);
            } else if (pageCount > 1) {
                this.updateProgress(60, `AI generating ${questionsPerPage} questions per page (${pageCount} pages)...`, '~20 seconds');
            } else {
                this.updateProgress(60, `AI generating ${totalQuestionCount} questions...`, '~15 seconds');
            }

            // Get selected model
            const selectedModel = await this.getSelectedModel();
            console.log('Frontend: Selected model for generation:', selectedModel);

            const questions = await window.electronAPI.generateQuestions(
                content,
                this.selectedQuestionType,
                totalQuestionCount,
                selectedModel
            );

            if (questions && questions.length > 0) {
                // Backend has already validated and balanced the questions
                // Just do minimal cleanup without overriding the backend logic
                this.updateProgress(90, 'Finalizing questions...', '~2 seconds');
                this.currentQuestions = this.minimalCleanQuestions(questions);
                this.updateProgress(100, 'Questions generated successfully!', 'Complete!');

                setTimeout(() => {
                    this.displayQuestions();
                }, 1000);
            } else {
                throw new Error('No questions were generated');
            }
        } catch (error) {
            console.error('Question generation error:', error);
            this.handleQuestionGenerationError(error);
        }
    }

    minimalCleanQuestions(questions) {
        // Backend has already done FORCED validation and balancing
        // Frontend should NEVER override backend logic - just display what we get
        console.log('🎯 Frontend received questions from backend:', questions.length);

        return questions.map((question, index) => {
            console.log(`📋 Question ${index + 1}: Answer = ${question.answer}, Type = ${typeof question.answer}`);

            // Clean up the question - TRUST THE BACKEND COMPLETELY
            const cleanedQuestion = { ...question };

            // For True/False questions, ONLY ensure string format (never change the logic)
            if (this.selectedQuestionType === 'TF') {
                // Backend sends boolean or string - convert to display format only
                if (cleanedQuestion.answer === true || cleanedQuestion.answer === 'true' || cleanedQuestion.answer === 'True') {
                    cleanedQuestion.answer = 'True';
                } else if (cleanedQuestion.answer === false || cleanedQuestion.answer === 'false' || cleanedQuestion.answer === 'False') {
                    cleanedQuestion.answer = 'False';
                } else {
                    // Only if completely missing (should never happen with new backend)
                    console.error(`Question ${index + 1} has invalid answer:`, cleanedQuestion.answer);
                    cleanedQuestion.answer = 'True'; // Fallback only
                }

                console.log(`✅ Question ${index + 1} final answer: ${cleanedQuestion.answer}`);
            }

            // Ensure question text exists
            if (!cleanedQuestion.question || cleanedQuestion.question.trim() === '') {
                cleanedQuestion.question = `Question ${index + 1}`;
                console.warn(`Question ${index + 1} has no text, using default`);
            }

            return cleanedQuestion;
        });
    }

    validateAndCleanQuestions(questions) {
        // Legacy function - now redirects to minimal cleanup
        // This preserves compatibility while using the new approach
        return this.minimalCleanQuestions(questions);
    }

    validateTrueFalseLogic(question, questionNumber) {
        if (!question.explanation) return;

        const questionText = question.question.toLowerCase();
        const explanation = question.explanation.toLowerCase();
        const answer = question.answer;

        console.log(`🔍 Validating TF logic for question ${questionNumber}:`);
        console.log(`   Question: "${question.question}"`);
        console.log(`   Answer: ${answer}`);
        console.log(`   Explanation: "${question.explanation}"`);

        // Check for common logic error patterns
        const falseIndicators = [
            'unknown', 'no specific', 'not identifiable', 'not known', 'unclear',
            'cannot be determined', 'no identifiable', 'idiopathic', 'primary',
            'essential', 'no clear cause'
        ];

        const trueIndicators = [
            'specific', 'identifiable', 'known cause', 'secondary', 'caused by',
            'results from', 'due to', 'because of', 'specific etiology'
        ];

        // Medical terminology validation
        this.validateMedicalTerminology(question, questionNumber);

        // Check if explanation contradicts the answer
        let explanationSuggestsFalse = falseIndicators.some(indicator =>
            explanation.includes(indicator)
        );

        let explanationSuggestsTrue = trueIndicators.some(indicator =>
            explanation.includes(indicator)
        );

        // Special case: Primary hypertension questions
        if (questionText.includes('primary') && questionText.includes('hypertension')) {
            if (questionText.includes('specific') || questionText.includes('identifiable')) {
                // Question asks if primary hypertension has specific cause
                // Correct answer should be False (primary = unknown cause)
                if (answer === 'True') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Primary hypertension questions about "specific cause" should be FALSE`);
                    console.warn(`   Current answer: ${answer} - Consider changing to FALSE`);

                    // Auto-correct this common error
                    question.answer = 'False';
                    console.log(`✅ Auto-corrected answer to: False`);
                }
            }
        }

        // Special case: Angiotensin II vasodilator/vasoconstrictor questions
        if (questionText.includes('angiotensin ii') || questionText.includes('angiotensin 2')) {
            if (questionText.includes('vasodilator')) {
                // Question incorrectly calls angiotensin II a vasodilator
                // Angiotensin II is a vasoconstrictor, so this should be FALSE
                if (answer === 'True') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Angiotensin II is a VASOCONSTRICTOR, not vasodilator`);
                    console.warn(`   Question calls it vasodilator - should be FALSE`);

                    // Auto-correct this medical error
                    question.answer = 'False';
                    console.log(`✅ Auto-corrected answer to: False`);
                }
            }
            if (questionText.includes('vasoconstrictor')) {
                // Question correctly calls angiotensin II a vasoconstrictor
                // This should be TRUE
                if (answer === 'False') {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Angiotensin II IS a vasoconstrictor`);
                    console.warn(`   Question correctly identifies it - should be TRUE`);

                    // Auto-correct this medical error
                    question.answer = 'True';
                    console.log(`✅ Auto-corrected answer to: True`);
                }
            }
        }

        // General contradiction check
        if (answer === 'True' && explanationSuggestsFalse && !explanationSuggestsTrue) {
            console.warn(`⚠️  POTENTIAL LOGIC ERROR in question ${questionNumber}:`);
            console.warn(`   Answer is TRUE but explanation suggests FALSE`);
            console.warn(`   False indicators found: ${falseIndicators.filter(i => explanation.includes(i))}`);
        }

        if (answer === 'False' && explanationSuggestsTrue && !explanationSuggestsFalse) {
            console.warn(`⚠️  POTENTIAL LOGIC ERROR in question ${questionNumber}:`);
            console.warn(`   Answer is FALSE but explanation suggests TRUE`);
            console.warn(`   True indicators found: ${trueIndicators.filter(i => explanation.includes(i))}`);
        }
    }

    validateMedicalTerminology(question, questionNumber) {
        const questionText = question.question.toLowerCase();
        const explanation = question.explanation ? question.explanation.toLowerCase() : '';
        const answer = question.answer;

        console.log(`🧠 Universal logic validation for question ${questionNumber}`);

        // Define universal logical fact patterns (works for any profession/subject)
        const universalLogicPatterns = [
            // Medical patterns (keep existing medical knowledge)
            {
                pattern: /angiotensin ii.*vasodilator/i,
                correctAnswer: 'False',
                reason: 'Angiotensin II is a vasoconstrictor, not a vasodilator'
            },
            {
                pattern: /angiotensin ii.*vasoconstrictor/i,
                correctAnswer: 'True',
                reason: 'Angiotensin II is indeed a vasoconstrictor'
            },
            {
                pattern: /ace inhibitors.*increase.*angiotensin ii/i,
                correctAnswer: 'False',
                reason: 'ACE inhibitors decrease angiotensin II by blocking its formation'
            },
            {
                pattern: /ace inhibitors.*decrease.*angiotensin ii/i,
                correctAnswer: 'True',
                reason: 'ACE inhibitors decrease angiotensin II by blocking its formation'
            },
            {
                pattern: /primary hypertension.*specific.*cause/i,
                correctAnswer: 'False',
                reason: 'Primary hypertension has no specific identifiable cause'
            },
            {
                pattern: /secondary hypertension.*specific.*cause/i,
                correctAnswer: 'True',
                reason: 'Secondary hypertension has specific identifiable causes'
            },
            {
                pattern: /insulin.*decrease.*blood glucose/i,
                correctAnswer: 'True',
                reason: 'Insulin decreases blood glucose levels'
            },
            {
                pattern: /insulin.*increase.*blood glucose/i,
                correctAnswer: 'False',
                reason: 'Insulin decreases, not increases, blood glucose levels'
            },
            {
                pattern: /microbiology.*solely.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Microbiology labs handle viral, fungal, and parasitic infections, not solely bacterial'
            },
            {
                pattern: /microbiology.*only.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Microbiology labs handle multiple types of pathogens, not only bacterial'
            },
            {
                pattern: /laboratories.*exclusively.*bacterial/i,
                correctAnswer: 'False',
                reason: 'Medical laboratories handle various types of infections, not exclusively bacterial'
            },

            // Engineering patterns
            {
                pattern: /software engineers.*only.*code/i,
                correctAnswer: 'False',
                reason: 'Software engineers also design, test, debug, document, and maintain systems'
            },
            {
                pattern: /civil engineers.*solely.*buildings/i,
                correctAnswer: 'False',
                reason: 'Civil engineers also work on bridges, roads, dams, and infrastructure systems'
            },

            // Legal patterns
            {
                pattern: /lawyers.*exclusively.*court/i,
                correctAnswer: 'False',
                reason: 'Lawyers also provide legal advice, draft documents, negotiate, and handle transactions'
            },
            {
                pattern: /judges.*only.*criminal cases/i,
                correctAnswer: 'False',
                reason: 'Judges handle criminal, civil, family, and administrative cases'
            },

            // Education patterns
            {
                pattern: /teachers.*solely.*lecture/i,
                correctAnswer: 'False',
                reason: 'Teachers also facilitate discussions, assess students, provide feedback, and mentor'
            },
            {
                pattern: /professors.*only.*research/i,
                correctAnswer: 'False',
                reason: 'Professors also teach, mentor students, serve on committees, and engage in service'
            },

            // Business patterns
            {
                pattern: /accountants.*exclusively.*taxes/i,
                correctAnswer: 'False',
                reason: 'Accountants also handle auditing, financial reporting, budgeting, and advisory services'
            },
            {
                pattern: /managers.*only.*supervise/i,
                correctAnswer: 'False',
                reason: 'Managers also plan, organize, coordinate, and make strategic decisions'
            },

            // Technology patterns
            {
                pattern: /databases.*solely.*store.*data/i,
                correctAnswer: 'False',
                reason: 'Databases also retrieve, organize, secure, backup, and process data'
            },
            {
                pattern: /firewalls.*only.*block/i,
                correctAnswer: 'False',
                reason: 'Firewalls also monitor, log, filter, and allow authorized traffic'
            }
        ];

        // Check each universal logic pattern
        for (const fact of universalLogicPatterns) {
            if (fact.pattern.test(questionText)) {
                console.log(`   📋 Medical pattern matched: ${fact.pattern}`);
                console.log(`   📚 Medical fact: ${fact.reason}`);
                console.log(`   🎯 Expected answer: ${fact.correctAnswer}`);
                console.log(`   🤖 AI answer: ${answer}`);

                if (answer !== fact.correctAnswer) {
                    console.warn(`⚠️  LOGIC ERROR DETECTED in question ${questionNumber}:`);
                    console.warn(`   Pattern: ${fact.pattern}`);
                    console.warn(`   Logic rule: ${fact.reason}`);
                    console.warn(`   AI gave: ${answer}, should be: ${fact.correctAnswer}`);

                    // Auto-correct logic errors
                    question.answer = fact.correctAnswer;
                    console.log(`✅ Auto-corrected logic error: ${answer} → ${fact.correctAnswer}`);

                    // Update explanation if it contradicts the correction
                    if (question.explanation && !question.explanation.toLowerCase().includes(fact.reason.toLowerCase())) {
                        question.explanation += ` (Note: ${fact.reason})`;
                        console.log(`📝 Enhanced explanation with logical fact`);
                    }
                }
                break; // Only apply first matching pattern
            }
        }

        // Check for explanation contradictions
        if (explanation) {
            // If explanation mentions "vasoconstrictor" but question says "vasodilator"
            if (questionText.includes('vasodilator') && explanation.includes('vasoconstrictor')) {
                if (answer === 'True') {
                    console.warn(`⚠️  CONTRADICTION DETECTED in question ${questionNumber}:`);
                    console.warn(`   Question mentions 'vasodilator' but explanation says 'vasoconstrictor'`);
                    console.warn(`   Auto-correcting to FALSE`);
                    question.answer = 'False';
                }
            }

            // If explanation mentions "vasodilator" but question says "vasoconstrictor"
            if (questionText.includes('vasoconstrictor') && explanation.includes('vasodilator')) {
                if (answer === 'True') {
                    console.warn(`⚠️  CONTRADICTION DETECTED in question ${questionNumber}:`);
                    console.warn(`   Question mentions 'vasoconstrictor' but explanation says 'vasodilator'`);
                    console.warn(`   Auto-correcting to FALSE`);
                    question.answer = 'False';
                }
            }

            // Universal Logic Contradiction Detection
            this.detectUniversalContradictions(question, questionNumber, questionText, explanation, answer);
        }
    }

    showProcessingScreen() {
        this.showScreen('processingScreen');
        this.updateProgress(0, 'Initializing AI models...', 'Calculating...');
    }

    hideProcessingScreen() {
        console.log('Hiding processing screen...');
        // Simply hide the processing screen by making it inactive
        const processingScreen = document.getElementById('processingScreen');
        if (processingScreen) {
            processingScreen.classList.remove('active');
        }
    }

    updateProgress(percentage, status, eta = null) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const processingStatus = document.getElementById('processingStatus');
        const progressETA = document.getElementById('progressETA');

        if (!progressFill || !progressText || !processingStatus) {
            console.error('Progress elements not found!');
            return;
        }

        // Update progress bar
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `${percentage}%`;
        processingStatus.textContent = status;

        // Update ETA if provided
        if (progressETA && eta) {
            progressETA.textContent = eta;
        }

        // Update processing steps based on percentage
        this.updateProcessingSteps(percentage);

        // Force a repaint to ensure the progress bar updates
        progressFill.offsetHeight;
    }

    updateProcessingSteps(percentage) {
        const steps = ['step1', 'step2', 'step3', 'step4'];
        const stepPercentages = [25, 50, 75, 100];

        steps.forEach((stepId, index) => {
            const stepElement = document.getElementById(stepId);
            if (!stepElement) return;

            stepElement.classList.remove('active', 'completed');

            if (percentage >= stepPercentages[index]) {
                stepElement.classList.add('completed');
            } else if (percentage >= (stepPercentages[index] - 25)) {
                stepElement.classList.add('active');
            }
        });
    }

    handleQuestionGenerationError(error) {
        const errorMessage = error.message || 'Unknown error occurred';

        // Check for specific error types
        if (errorMessage.includes('Rate limit exceeded')) {
            this.handleRateLimitError();
        } else if (errorMessage.includes('Failed to generate questions after')) {
            this.handleGenerationFailure(error);
        } else if (errorMessage.includes('Network')) {
            this.handleNetworkError();
        } else {
            this.handleGenericError(error);
        }
    }

    handleRateLimitError() {
        this.updateProgress(0, 'API rate limit reached. Retrying with different models...', 'Please wait...');

        // Show retry options
        this.showRateLimitDialog();
    }

    handleGenerationFailure(error) {
        this.updateProgress(0, 'Question generation failed. Checking alternatives...', 'Retrying...');

        // Show retry with options
        this.showGenerationFailureDialog(error);
    }

    handleNetworkError() {
        this.updateProgress(0, 'Network connection issue detected...', 'Check connection');

        this.showNotification('Network error. Please check your internet connection and try again.', 'error');
        setTimeout(() => {
            this.showScreen('quizGeneratorScreen');
        }, 3000);
    }

    handleGenericError(error) {
        this.updateProgress(0, 'An unexpected error occurred...', 'Error');

        console.error('Generic error:', error);
        this.showNotification(`Error: ${error.message}`, 'error');
        setTimeout(() => {
            this.showScreen('quizGeneratorScreen');
        }, 2000);
    }

    showRateLimitDialog() {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Create a custom dialog for rate limit handling
        const dialog = document.createElement('div');
        dialog.className = 'error-dialog rate-limit-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3>${isArabic ? 'تم الوصول إلى حد معدل API' : 'API Rate Limit Reached'}</h3>
                <p>${isArabic ? 'تواجه خدمة الذكاء الاصطناعي حالياً طلباً عالياً. جميع النماذج المتاحة محدودة المعدل مؤقتاً.' : 'The AI service is currently experiencing high demand. All available models are temporarily rate-limited.'}</p>
                <div class="dialog-options">
                    <button class="btn btn-primary" id="tryDifferentModelBtn">
                        <i class="fas fa-exchange-alt"></i> ${isArabic ? 'جرب نموذج مختلف' : 'Try Different Model'}
                    </button>
                    <button class="btn btn-secondary" id="cancelBtn">
                        <i class="fas fa-times"></i> ${isArabic ? 'إلغاء' : 'Cancel'}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Add event listeners
        dialog.querySelector('#tryDifferentModelBtn').addEventListener('click', async () => await this.showModelSelectionDialog());
        dialog.querySelector('#cancelBtn').addEventListener('click', () => this.cancelGeneration());
    }

    showGenerationFailureDialog(error) {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const dialog = document.createElement('div');
        dialog.className = 'error-dialog generation-failure-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>${isArabic ? 'فشل في إنشاء الأسئلة' : 'Question Generation Failed'}</h3>
                <p>${isArabic ? 'واجهنا مشاكل في إنشاء الأسئلة. قد يكون هذا بسبب:' : 'We encountered issues generating questions. This might be due to:'}</p>
                <ul>
                    <li>${isArabic ? 'طلب عالي على API' : 'High API demand'}</li>
                    <li>${isArabic ? 'محتوى معقد يتطلب وقت معالجة أكثر' : 'Complex content requiring more processing time'}</li>
                    <li>${isArabic ? 'قيود خدمة مؤقتة' : 'Temporary service limitations'}</li>
                </ul>
                <div class="dialog-options">
                    <button class="btn btn-primary" id="retrySelectedBtn">
                        <i class="fas fa-redo"></i> ${isArabic ? 'حاول مرة أخرى' : 'Try Again'}
                    </button>
                    <button class="btn btn-secondary" id="tryDifferentBtn">
                        <i class="fas fa-exchange-alt"></i> ${isArabic ? 'جرب نموذج مختلف' : 'Try Different Model'}
                    </button>
                    <button class="btn btn-secondary" id="cancelFailureBtn">
                        <i class="fas fa-times"></i> ${isArabic ? 'إلغاء' : 'Cancel'}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Add event listeners
        dialog.querySelector('#retrySelectedBtn').addEventListener('click', () => this.retryWithSelectedModel());
        dialog.querySelector('#tryDifferentBtn').addEventListener('click', async () => await this.showModelSelectionDialog());
        dialog.querySelector('#cancelFailureBtn').addEventListener('click', () => this.cancelGeneration());
    }





    async showModelSelectionDialog() {
        this.closeErrorDialogs();

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const dialog = document.createElement('div');
        dialog.className = 'error-dialog model-selection-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <div class="dialog-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3>${isArabic ? 'اختر نموذج الذكاء الاصطناعي' : 'Select AI Model'}</h3>
                <p>${isArabic ? 'اختر نموذج الذكاء الاصطناعي الذي تريد تجربته لإنشاء الأسئلة:' : 'Choose which AI model you\'d like to try for generating questions:'}</p>
                <div class="model-selection-container">
                    <label for="dialogModelSelect">${isArabic ? 'نموذج الذكاء الاصطناعي:' : 'AI Model:'}</label>
                    <select id="dialogModelSelect" class="setting-select">
                        <option value="auto">${isArabic ? 'تلقائي (الأفضل المتاح)' : 'Auto (Best Available)'}</option>
                        <!-- Models will be loaded dynamically -->
                    </select>
                </div>
                <div class="dialog-options">
                    <button class="btn btn-primary" id="trySelectedModelBtn">
                        <i class="fas fa-play"></i> ${isArabic ? 'جرب النموذج المحدد' : 'Try Selected Model'}
                    </button>
                    <button class="btn btn-secondary" id="cancelModelSelectionBtn">
                        <i class="fas fa-times"></i> ${isArabic ? 'إلغاء' : 'Cancel'}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Load available models into the dialog dropdown and set current selection
        await this.loadModelsIntoDialog(dialog);

        // Add event listeners
        dialog.querySelector('#trySelectedModelBtn').addEventListener('click', () => this.trySelectedModelFromDialog());
        dialog.querySelector('#cancelModelSelectionBtn').addEventListener('click', () => this.cancelGeneration());
    }

    async loadModelsIntoDialog(dialog) {
        const dialogSelect = dialog.querySelector('#dialogModelSelect');
        if (!dialogSelect) return;

        try {
            // Get all models from backend
            const result = await window.electronAPI.getAllModels();
            console.log('Models loaded for dialog:', result);

            if (result.success && result.models && result.models.length > 0) {
                // Add all available models to the dialog dropdown
                result.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name || model.id;
                    dialogSelect.appendChild(option);
                });

                // Set current model as selected
                const currentModel = await this.getSelectedModel();
                dialogSelect.value = currentModel;
            } else {
                // No custom models available - add a helpful message
                const currentLanguage = localStorage.getItem('language') || 'en';
                const isArabic = currentLanguage === 'ar';

                const noModelsOption = document.createElement('option');
                noModelsOption.value = '';
                noModelsOption.textContent = isArabic ? 'يجب إضافة نماذج أولاً من إعدادات النماذج' : 'Please add models first from Model Management';
                noModelsOption.disabled = true;
                dialogSelect.appendChild(noModelsOption);

                // Disable the try button since no models are available
                const tryBtn = dialog.querySelector('#trySelectedModelBtn');
                if (tryBtn) {
                    tryBtn.disabled = true;
                    tryBtn.title = isArabic ? 'لا توجد نماذج متاحة' : 'No models available';
                }

                console.log('No custom models found - user needs to add models first');
            }
        } catch (error) {
            console.error('Error loading models into dialog:', error);
        }
    }

    async trySelectedModelFromDialog() {
        const dialogSelect = document.getElementById('dialogModelSelect');
        const selectedModel = dialogSelect.value;

        this.closeErrorDialogs();

        // Update the global model selection dropdown
        const globalModelSelect = document.getElementById('globalModelSelect');
        if (globalModelSelect) {
            globalModelSelect.value = selectedModel;
            await this.saveModelPreference(selectedModel);
        }

        const modelName = this.getModelDisplayName(selectedModel);
        this.updateProgress(10, `Trying with ${modelName}...`, 'Switching models...');

        try {
            if (this.lastGenerationParams) {
                await this.generateQuestions(
                    this.lastGenerationParams.content,
                    this.lastGenerationParams.isImage,
                    this.lastGenerationParams.pageCount
                );
            }
        } catch (error) {
            this.handleQuestionGenerationError(error);
        }
    }





    cancelGeneration() {
        console.log('Cancel generation called');

        this.closeErrorDialogs();

        // Reset progress
        this.updateProgress(0, 'Generation canceled', 'Canceled');

        // Show notification and return to content screen
        this.showNotification('Question generation canceled', 'info');

        // Small delay to show the canceled status
        setTimeout(() => {
            this.showScreen('quizGeneratorScreen');
        }, 1000);
    }

    closeErrorDialogs() {
        const dialogs = document.querySelectorAll('.error-dialog');
        dialogs.forEach(dialog => dialog.remove());
    }

    getQuestionCount() {
        // Get the current question count setting
        if (this.appSettings?.questionsPerPage) {
            return this.appSettings.questionsPerPage;
        }

        // Fallback to default
        return 10;
    }

    initializeModelSelection() {
        console.log('initializeModelSelection() called - using global model select');
        const globalModelSelect = document.getElementById('globalModelSelect');
        if (!globalModelSelect) {
            console.log('Global model select element not found!');
            return;
        }

        console.log('Global model select element found, current value:', globalModelSelect.value);

        // Load models dynamically from backend
        this.refreshGlobalModelDropdown();

        // Load saved model preference
        this.loadGlobalModelPreference();

        // Set up change listener
        globalModelSelect.addEventListener('change', () => {
            console.log('Global model selection changed to:', globalModelSelect.value);
            this.saveModelPreference(globalModelSelect.value);
            this.updateGlobalModelStatus();
        });

        // Initial status check
        this.updateGlobalModelStatus();

    }

    async loadModelPreference() {
        try {
            if (!window.electronAPI || !window.electronAPI.getSettings) {
                console.warn('electronAPI not ready for model settings');
                return;
            }

            console.log('Loading model preference...');
            const result = await window.electronAPI.getSettings();
            console.log('Settings result:', result);

            if (result.success && result.settings) {
                console.log('Settings loaded:', result.settings);

                // Store the preference in class variable
                if (result.settings.preferredModel) {
                    this.savedModelPreference = result.settings.preferredModel;
                    console.log('Stored model preference:', this.savedModelPreference);

                    // Set the dropdown value
                    const modelSelect = document.getElementById('modelSelect');
                    if (modelSelect) {
                        console.log('Setting model select to:', result.settings.preferredModel);
                        modelSelect.value = result.settings.preferredModel;
                        console.log('Model select value after setting:', modelSelect.value);

                        // Trigger change event to update status
                        this.updateModelStatus(result.settings.preferredModel);
                    } else {
                        console.log('modelSelect element not found');
                    }
                } else {
                    console.log('No preferredModel in settings');
                }
            } else {
                console.log('Failed to load settings or settings empty');
            }
        } catch (error) {
            console.warn('Could not load model preference:', error);
        }
    }

    async loadGlobalModelPreference() {
        try {
            if (!window.electronAPI || !window.electronAPI.getSettings) {
                console.warn('electronAPI not ready for model settings');
                return;
            }

            console.log('Loading global model preference...');
            const result = await window.electronAPI.getSettings();
            console.log('Settings result:', result);

            if (result.success && result.settings) {
                console.log('Settings loaded:', result.settings);

                // Store the preference in class variable
                if (result.settings.preferredModel) {
                    this.savedModelPreference = result.settings.preferredModel;
                    console.log('Stored model preference:', this.savedModelPreference);

                    // Set the global dropdown value
                    const globalModelSelect = document.getElementById('globalModelSelect');
                    if (globalModelSelect) {
                        console.log('Setting global model select to:', result.settings.preferredModel);
                        globalModelSelect.value = result.settings.preferredModel;
                        console.log('Global model select value after setting:', globalModelSelect.value);

                        // Trigger change event to update status
                        this.updateGlobalModelStatus();
                    } else {
                        console.log('globalModelSelect element not found');
                    }
                } else {
                    console.log('No preferredModel in settings');
                }
            } else {
                console.log('Failed to load settings or settings empty');
            }
        } catch (error) {
            console.warn('Could not load global model preference:', error);
        }
    }

    async refreshGlobalModelDropdown() {
        try {
            const result = await window.electronAPI.getAllModels();

            if (result.success && result.models) {
                const globalModelSelect = document.getElementById('globalModelSelect');
                if (globalModelSelect) {
                    // Save current selection
                    const currentValue = globalModelSelect.value;

                    // Clear existing options except auto
                    globalModelSelect.innerHTML = '<option value="auto">Auto (Best Available)</option>';

                    // Add all available models
                    result.models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.id;
                        option.textContent = model.name || model.id;
                        globalModelSelect.appendChild(option);
                    });

                    // Restore selection if still available
                    if (currentValue && Array.from(globalModelSelect.options).some(opt => opt.value === currentValue)) {
                        globalModelSelect.value = currentValue;
                    }
                }
            }
        } catch (error) {
            console.error('Error refreshing global model dropdown:', error);
        }
    }

    async saveModelPreference(model) {
        try {
            if (!window.electronAPI || !window.electronAPI.saveSettings) {
                console.warn('electronAPI not ready for saving model preference');
                return;
            }

            // Save just the model preference
            const result = await window.electronAPI.saveSettings({ preferredModel: model });
            if (result.success) {
                console.log('Model preference saved:', model);
                this.showNotification(`AI model updated to ${this.getModelDisplayName(model)}`, 'success');
            } else {
                this.showNotification('Failed to save model preference', 'error');
            }
        } catch (error) {
            console.error('Error saving model preference:', error);
            this.showNotification('Error saving model preference', 'error');
        }
    }

    getModelDisplayName(modelId) {
        // Only handle special cases, let custom models use their configured names
        if (modelId === 'auto') {
            return 'Auto (Best Available)';
        }

        // For all other models, return the ID as-is or get from backend
        return modelId;
    }

    async updateModelStatus(modelId) {
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        const statusDescription = document.querySelector('.status-description');

        if (!statusIndicator || !statusText) return;

        // Get current language for direct translations
        const currentLanguage = localStorage.getItem('language') || 'en';

        if (modelId === 'auto') {
            statusIndicator.className = 'status-indicator status-available';
            const autoText = currentLanguage === 'ar' ? 'تلقائي (الأفضل المتاح)' : 'Auto (Best Available)';
            const availableText = currentLanguage === 'ar' ? 'النموذج متاح' : 'Model available';

            statusText.textContent = autoText;
            if (statusDescription) {
                statusDescription.textContent = availableText;
            }
        } else if (modelId) {
            // Show checking status first
            statusIndicator.className = 'status-indicator status-unknown';
            const checkingText = currentLanguage === 'ar' ? 'فحص التوفر...' : 'Checking availability...';
            const willAppearText = currentLanguage === 'ar' ? 'ستظهر حالة النموذج هنا' : 'Model status will appear here';

            statusText.textContent = checkingText;
            if (statusDescription) {
                statusDescription.textContent = willAppearText;
            }

            // Check if model is rate limited
            try {
                const rateLimitResult = await window.electronAPI.getRateLimitedModels();
                if (rateLimitResult.success) {
                    const rateLimitedModel = rateLimitResult.models.find(m => m.model === modelId);
                    if (rateLimitedModel) {
                        // Model is rate limited
                        statusIndicator.className = 'status-indicator status-rate-limited';

                        // Get current language
                        const currentLanguage = localStorage.getItem('language') || 'en';
                        const rateLimitText = currentLanguage === 'ar' ? 'محدود المعدل' : 'Rate Limited';
                        const unavailableText = currentLanguage === 'ar' ? 'النموذج غير متاح مؤقتاً بسبب حدود المعدل' : 'Model temporarily unavailable due to rate limits';

                        statusText.textContent = `${rateLimitText} (${rateLimitedModel.timeAgo}m ago)`;
                        if (statusDescription) {
                            statusDescription.textContent = unavailableText;
                        }
                        return;
                    }
                }
            } catch (error) {
                console.warn('Could not check rate limit status:', error);
            }

            // Model appears to be available (not rate limited)
            statusIndicator.className = 'status-indicator status-available';
            const availableText = currentLanguage === 'ar' ? 'النموذج متاح' : 'Model available';

            // Check if we have recent test results for this model
            const lastTestResult = this.getLastTestResult(modelId);
            if (lastTestResult) {
                const statusWithTiming = currentLanguage === 'ar' ?
                    `النموذج متاح (${lastTestResult.duration}ms)` :
                    `Model available (${lastTestResult.duration}ms)`;
                statusText.textContent = statusWithTiming;

                if (statusDescription) {
                    const successText = currentLanguage === 'ar' ?
                        `آخر اختبار: ${lastTestResult.questions} أسئلة في ${lastTestResult.duration}ms` :
                        `Last test: ${lastTestResult.questions} questions in ${lastTestResult.duration}ms`;
                    statusDescription.textContent = successText;
                }
            } else {
                statusText.textContent = availableText;
                if (statusDescription) {
                    statusDescription.textContent = availableText;
                }
            }
        } else {
            // No model selected
            statusIndicator.className = 'status-indicator status-unknown';
            const checkingText = currentLanguage === 'ar' ? 'فحص التوفر...' : 'Checking availability...';
            const willAppearText = currentLanguage === 'ar' ? 'ستظهر حالة النموذج هنا' : 'Model status will appear here';

            statusText.textContent = checkingText;
            if (statusDescription) {
                statusDescription.textContent = willAppearText;
            }
        }
    }

    getTranslationText(key) {
        // Get current language from localStorage or default to English
        const currentLanguage = localStorage.getItem('language') || 'en';

        // Access the global translations object
        if (window.translations && window.translations[currentLanguage] && window.translations[currentLanguage][key]) {
            return window.translations[currentLanguage][key];
        }

        // Fallback to English if translation not found
        if (window.translations && window.translations['en'] && window.translations['en'][key]) {
            return window.translations['en'][key];
        }

        // Final fallback to the key itself
        return key;
    }

    getLastTestResult(modelId) {
        // Check if we have stored test results for this model
        if (this.testResults && Array.isArray(this.testResults)) {
            const modelResult = this.testResults.find(result => result.model === modelId);
            if (modelResult && modelResult.success) {
                return {
                    duration: modelResult.duration,
                    questions: modelResult.questionsGenerated || 0
                };
            }
        }
        return null;
    }



    async getSelectedModel() {
        // First, try to get the current dropdown value from global AI settings
        const globalModelSelect = document.getElementById('globalModelSelect');
        const dropdownValue = globalModelSelect ? globalModelSelect.value : 'auto';

        // If dropdown has a specific model selected, use it
        if (dropdownValue && dropdownValue !== 'auto') {
            console.log('getSelectedModel() using dropdown value:', dropdownValue);
            return dropdownValue;
        }

        // If dropdown is auto, try to load from settings directly
        try {
            if (window.electronAPI && window.electronAPI.getSettings) {
                const result = await window.electronAPI.getSettings();
                if (result.success && result.settings && result.settings.preferredModel && result.settings.preferredModel !== 'auto') {
                    console.log('getSelectedModel() using settings preference:', result.settings.preferredModel);
                    return result.settings.preferredModel;
                }
            }
        } catch (error) {
            console.warn('Could not load settings in getSelectedModel:', error);
        }

        // Check saved preference variable as fallback
        if (this.savedModelPreference && this.savedModelPreference !== 'auto') {
            console.log('getSelectedModel() using saved preference:', this.savedModelPreference);
            return this.savedModelPreference;
        }

        // Default to auto
        console.log('getSelectedModel() defaulting to auto');
        return 'auto';
    }

    // Model Management Functions
    initializeModelManagement() {
        console.log('Initializing model management...');

        // Add Model Button
        const addModelBtn = document.getElementById('addModelBtn');
        if (addModelBtn) {
            addModelBtn.addEventListener('click', () => this.showAddModelDialog());
        }

        // Remove Model Button
        const removeModelBtn = document.getElementById('removeModelBtn');
        if (removeModelBtn) {
            removeModelBtn.addEventListener('click', () => this.showRemoveModelDialog());
        }



        // Test Models Button
        const testModelsBtn = document.getElementById('testModelsBtn');
        if (testModelsBtn) {
            testModelsBtn.addEventListener('click', () => this.showTestModelsDialog());
        }

        // API Key Management Button
        const manageApiKeyBtn = document.getElementById('manageApiKeyBtn');
        if (manageApiKeyBtn) {
            manageApiKeyBtn.addEventListener('click', () => this.openApiKeyManager());
        }

        // Add Model Dialog Events
        this.setupAddModelDialog();

        // Remove Model Dialog Events
        this.setupRemoveModelDialog();

        // Test Models Dialog Events
        this.setupTestModelsDialog();
    }

    setupAddModelDialog() {
        // Close dialog events
        const closeBtn = document.getElementById('closeAddModelDialog');
        const cancelBtn = document.getElementById('cancelAddModel');

        if (closeBtn) closeBtn.addEventListener('click', () => this.hideAddModelDialog());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.hideAddModelDialog());

        // Confirm add model
        const confirmBtn = document.getElementById('confirmAddModel');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.addNewModel());
        }

        // Close on overlay click
        const overlay = document.getElementById('addModelDialog');
        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) this.hideAddModelDialog();
            });
        }
    }

    setupRemoveModelDialog() {
        // Close dialog events
        const closeBtn = document.getElementById('closeRemoveModelDialog');
        const cancelBtn = document.getElementById('cancelRemoveModel');

        if (closeBtn) closeBtn.addEventListener('click', () => this.hideRemoveModelDialog());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.hideRemoveModelDialog());

        // Model selection change
        const selectElement = document.getElementById('removeModelSelect');
        if (selectElement) {
            selectElement.addEventListener('change', () => this.updateRemoveModelInfo());
        }

        // Confirm remove model
        const confirmBtn = document.getElementById('confirmRemoveModel');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => this.removeSelectedModel());
        }

        // Close on overlay click
        const overlay = document.getElementById('removeModelDialog');
        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) this.hideRemoveModelDialog();
            });
        }
    }



    // Dialog Show/Hide Methods
    showAddModelDialog() {
        const dialog = document.getElementById('addModelDialog');
        if (dialog) {
            // Clear form
            document.getElementById('newModelId').value = '';
            document.getElementById('newModelName').value = '';
            document.getElementById('newModelDescription').value = '';

            dialog.style.display = 'flex';

            // Update translations for the dialog
            if (window.updateLanguage) {
                window.updateLanguage();
            }

            // Focus on first input
            setTimeout(() => {
                document.getElementById('newModelId').focus();
            }, 100);
        }
    }

    hideAddModelDialog() {
        const dialog = document.getElementById('addModelDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }

    showRemoveModelDialog() {
        const dialog = document.getElementById('removeModelDialog');
        if (dialog) {
            // Populate model list
            this.populateRemoveModelList();
            dialog.style.display = 'flex';

            // Update translations for the dialog
            if (window.updateLanguage) {
                window.updateLanguage();
            }
        }
    }

    hideRemoveModelDialog() {
        const dialog = document.getElementById('removeModelDialog');
        if (dialog) {
            dialog.style.display = 'none';
        }
    }



    // Model Management Core Functions
    async addNewModel() {
        const modelId = document.getElementById('newModelId').value.trim();
        const modelName = document.getElementById('newModelName').value.trim();
        const modelDescription = document.getElementById('newModelDescription').value.trim();

        // Validation
        if (!modelId) {
            this.showNotification('Please enter a model ID', 'error');
            return;
        }

        if (!modelName) {
            this.showNotification('Please enter a display name', 'error');
            return;
        }

        // Validate model ID format (basic check)
        if (!modelId.includes('/') || !modelId.includes(':')) {
            this.showNotification('Model ID should be in format: provider/model-name:tier', 'error');
            return;
        }

        try {
            // Call backend to add model
            const result = await window.electronAPI.addModel({
                id: modelId,
                name: modelName,
                description: modelDescription
            });

            if (result.success) {
                this.showNotification('Model added successfully!', 'success');
                this.hideAddModelDialog();

                // Refresh model dropdown
                await this.refreshModelDropdown();
            } else {
                this.showNotification(result.error || 'Failed to add model', 'error');
            }
        } catch (error) {
            console.error('Error adding model:', error);
            this.showNotification('Error adding model: ' + error.message, 'error');
        }
    }

    async populateRemoveModelList() {
        const selectElement = document.getElementById('removeModelSelect');
        if (!selectElement) return;

        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="">Choose a model to remove...</option>';

        try {
            // Get all models from backend
            const result = await window.electronAPI.getAllModels();

            if (result.success && result.models) {
                // Show ALL models - no restrictions
                const allModels = result.models;

                if (allModels.length === 0) {
                    const noModelsOption = document.createElement('option');
                    noModelsOption.value = '';
                    noModelsOption.textContent = 'No models available to remove';
                    noModelsOption.disabled = true;
                    selectElement.appendChild(noModelsOption);
                } else {
                    allModels.forEach(model => {
                        const newOption = document.createElement('option');
                        newOption.value = model.id;
                        newOption.textContent = `${model.name || model.id}${model.custom ? ' (Custom)' : ''}`;
                        selectElement.appendChild(newOption);
                    });
                }
            }
        } catch (error) {
            console.error('Error loading models for removal:', error);
            const errorOption = document.createElement('option');
            errorOption.value = '';
            errorOption.textContent = 'Error loading models';
            errorOption.disabled = true;
            selectElement.appendChild(errorOption);
        }

        // Reset UI
        this.updateRemoveModelInfo();
    }

    updateRemoveModelInfo() {
        const selectElement = document.getElementById('removeModelSelect');
        const infoDiv = document.getElementById('removeModelInfo');
        const confirmBtn = document.getElementById('confirmRemoveModel');

        if (!selectElement || !infoDiv || !confirmBtn) return;

        const selectedValue = selectElement.value;

        if (selectedValue) {
            // Show model info
            const selectedOption = selectElement.options[selectElement.selectedIndex];
            document.getElementById('removeModelId').textContent = selectedValue;
            document.getElementById('removeModelName').textContent = selectedOption.textContent;

            infoDiv.style.display = 'block';
            confirmBtn.disabled = false;
        } else {
            // Hide model info
            infoDiv.style.display = 'none';
            confirmBtn.disabled = true;
        }
    }

    async removeSelectedModel() {
        const selectElement = document.getElementById('removeModelSelect');
        if (!selectElement || !selectElement.value) return;

        const modelId = selectElement.value;
        const modelName = selectElement.options[selectElement.selectedIndex].textContent;

        // Confirm deletion
        const confirmed = confirm(`Are you sure you want to remove the model "${modelName}"?\n\nThis action cannot be undone.`);
        if (!confirmed) return;

        try {
            // Call backend to remove model
            const result = await window.electronAPI.removeModel(modelId);

            if (result.success) {
                this.showNotification('Model removed successfully!', 'success');
                this.hideRemoveModelDialog();

                // Refresh model dropdown
                await this.refreshModelDropdown();
            } else {
                this.showNotification(result.error || 'Failed to remove model', 'error');
            }
        } catch (error) {
            console.error('Error removing model:', error);
            this.showNotification('Error removing model: ' + error.message, 'error');
        }
    }



    async refreshModelDropdown() {
        // Use the global model dropdown refresh method
        await this.refreshGlobalModelDropdown();
    }

    setupTestModelsDialog() {
        // Close dialog events
        const closeBtn = document.getElementById('closeTestModelsDialog');
        const closeBtn2 = document.getElementById('closeTestModels');

        if (closeBtn) closeBtn.addEventListener('click', () => this.hideTestModelsDialog());
        if (closeBtn2) closeBtn2.addEventListener('click', () => this.hideTestModelsDialog());

        // Start testing
        const startBtn = document.getElementById('startModelTest');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.startModelTesting());
        }

        // Stop testing
        const stopBtn = document.getElementById('stopModelTest');
        if (stopBtn) {
            stopBtn.addEventListener('click', () => this.stopModelTesting());
        }

        // Clear rate limits
        const clearRateLimitsBtn = document.getElementById('clearRateLimits');
        if (clearRateLimitsBtn) {
            clearRateLimitsBtn.addEventListener('click', () => this.clearAllRateLimits());
        }

        // Export results
        const exportBtn = document.getElementById('exportTestResults');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportTestResults());
        }

        // Close on overlay click
        const overlay = document.getElementById('testModelsDialog');
        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) this.hideTestModelsDialog();
            });
        }
    }

    showTestModelsDialog() {
        const dialog = document.getElementById('testModelsDialog');
        if (dialog) {
            dialog.style.display = 'flex';
            this.resetTestResults();

            // Update translations for the dialog
            if (window.updateLanguage) {
                window.updateLanguage();
            }
        }
    }

    hideTestModelsDialog() {
        const dialog = document.getElementById('testModelsDialog');
        if (dialog) {
            dialog.style.display = 'none';
            this.stopModelTesting();
        }
    }

    // API Key Manager
    async openApiKeyManager() {
        console.log('Opening API Key Manager...');

        try {
            // Show the dialog
            this.showApiKeyDialog();

            // Load current API key info
            await this.loadApiKeyInfo();

        } catch (error) {
            console.error('Error opening API key manager:', error);
            this.showAlert('error', `Failed to open API key manager: ${error.message}`);
        }
    }

    showApiKeyDialog() {
        const dialog = document.getElementById('apiKeyDialog');
        if (dialog) {
            dialog.style.display = 'flex';
            this.setupApiKeyDialogEvents();

            // Update translations for the dialog
            if (window.updateLanguage) {
                window.updateLanguage();
            }
        }
    }

    hideApiKeyDialog() {
        const dialog = document.getElementById('apiKeyDialog');
        if (dialog) {
            dialog.style.display = 'none';
            this.clearApiKeyAlert();
        }
    }

    setupApiKeyDialogEvents() {
        // Close button events
        const closeBtn = document.getElementById('closeApiKeyDialog');
        const cancelBtn = document.getElementById('cancelApiKeyDialog');

        if (closeBtn) {
            closeBtn.onclick = () => this.hideApiKeyDialog();
        }

        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideApiKeyDialog();
        }

        // Update API key button
        const updateBtn = document.getElementById('updateApiKeyBtn');
        if (updateBtn) {
            updateBtn.onclick = () => this.updateApiKey();
        }

        // Test API key button
        const testBtn = document.getElementById('testApiKeyBtn');
        if (testBtn) {
            testBtn.onclick = () => this.testApiKey();
        }

        // Toggle visibility button
        const toggleBtn = document.getElementById('toggleApiKeyVisibility');
        if (toggleBtn) {
            toggleBtn.onclick = () => this.toggleApiKeyVisibility();
        }

        // Input validation
        const input = document.getElementById('newApiKeyInput');
        if (input) {
            input.oninput = () => this.validateApiKeyInput();
            input.onkeypress = (e) => {
                if (e.key === 'Enter') {
                    this.updateApiKey();
                }
            };
        }

        // Close on overlay click
        const dialog = document.getElementById('apiKeyDialog');
        if (dialog) {
            dialog.onclick = (e) => {
                if (e.target === dialog) {
                    this.hideApiKeyDialog();
                }
            };
        }
    }

    async loadApiKeyInfo() {
        try {
            const container = document.getElementById('currentApiKeyInfo');
            if (!container) return;

            container.innerHTML = `
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    Loading API key information...
                </div>
            `;

            const result = await window.electronAPI.getApiKeyInfo();

            if (result.success && result.hasKey) {
                container.innerHTML = `
                    <div class="api-key-info-grid">
                        <div class="api-key-info-label">Status:</div>
                        <div class="api-key-info-value">
                            <span class="status-indicator ${result.isValidFormat ? 'status-available' : 'status-unavailable'}"></span>
                            ${result.isValidFormat ? 'Valid Format' : 'Invalid Format'}
                        </div>

                        <div class="api-key-info-label">Provider:</div>
                        <div class="api-key-info-value">${result.provider}</div>

                        <div class="api-key-info-label">Key (Masked):</div>
                        <div class="api-key-info-value">${result.maskedKey}</div>

                        <div class="api-key-info-label">Length:</div>
                        <div class="api-key-info-value">${result.keyLength} characters</div>
                    </div>
                `;

                if (!result.isValidFormat) {
                    this.showApiKeyAlert('warning', 'Your current API key format is invalid. Please update it with a valid OpenRouter key.');
                }
            } else {
                container.innerHTML = `
                    <div class="api-key-alert error">
                        <strong>No API Key Found</strong><br>
                        Please add an OpenRouter API key to use the question generation features.
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading API key info:', error);
            const container = document.getElementById('currentApiKeyInfo');
            if (container) {
                container.innerHTML = `
                    <div class="api-key-alert error">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }
    }

    async updateApiKey() {
        const input = document.getElementById('newApiKeyInput');
        const updateBtn = document.getElementById('updateApiKeyBtn');

        if (!input || !updateBtn) return;

        const newKey = input.value.trim();

        if (!newKey) {
            this.showApiKeyAlert('error', 'Please enter an API key.');
            return;
        }

        // Validate format
        if (!newKey.startsWith('sk-or-v1-')) {
            this.showApiKeyAlert('error', 'API key must start with "sk-or-v1-"');
            return;
        }

        if (newKey.length !== 73) {
            this.showApiKeyAlert('error', 'API key must be exactly 73 characters long.');
            return;
        }

        // Show loading state
        updateBtn.disabled = true;
        updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';

        try {
            const result = await window.electronAPI.updateApiKey(newKey);

            if (result.success) {
                this.showApiKeyAlert('success', result.message);
                input.value = '';
                await this.loadApiKeyInfo(); // Refresh the current key info
            } else {
                this.showApiKeyAlert('error', result.error);
            }
        } catch (error) {
            console.error('Error updating API key:', error);
            this.showApiKeyAlert('error', `Failed to update API key: ${error.message}`);
        } finally {
            updateBtn.disabled = false;
            updateBtn.innerHTML = '<i class="fas fa-save"></i> Update API Key';
        }
    }

    async testApiKey() {
        const testBtn = document.getElementById('testApiKeyBtn');
        const testResults = document.getElementById('apiKeyTestResults');
        const testContent = document.getElementById('testResultsContent');

        if (!testBtn || !testResults || !testContent) return;

        // Show loading state
        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
        testResults.style.display = 'block';
        testContent.innerHTML = `
            <div class="api-key-alert info">
                <i class="fas fa-spinner fa-spin"></i> Testing API key with sample request...
            </div>
        `;

        try {
            const result = await window.electronAPI.testApiKey();

            if (result.success) {
                testContent.innerHTML = `
                    <div class="api-key-alert success">
                        <strong>✅ API Key Test Successful!</strong><br>
                        ${result.message}<br>
                        Questions generated: ${result.questionsGenerated}
                    </div>
                `;
            } else {
                testContent.innerHTML = `
                    <div class="api-key-alert error">
                        <strong>❌ API Key Test Failed</strong><br>
                        ${result.error}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error testing API key:', error);
            testContent.innerHTML = `
                <div class="api-key-alert error">
                    <strong>❌ Test Error</strong><br>
                    ${error.message}
                </div>
            `;
        } finally {
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-flask"></i> Test Current Key';
        }
    }

    toggleApiKeyVisibility() {
        const input = document.getElementById('newApiKeyInput');
        const button = document.getElementById('toggleApiKeyVisibility');

        if (!input || !button) return;

        if (input.type === 'password') {
            input.type = 'text';
            button.innerHTML = '<i class="fas fa-eye-slash"></i>';
        } else {
            input.type = 'password';
            button.innerHTML = '<i class="fas fa-eye"></i>';
        }
    }

    validateApiKeyInput() {
        const input = document.getElementById('newApiKeyInput');
        if (!input) return;

        const value = input.value;

        // Remove existing classes
        input.classList.remove('valid', 'invalid');

        if (value.length === 0) {
            // No styling for empty input
        } else if (value.startsWith('sk-or-v1-') && value.length === 73) {
            input.classList.add('valid');
        } else {
            input.classList.add('invalid');
        }
    }

    showApiKeyAlert(type, message) {
        const container = document.getElementById('apiKeyAlertContainer');
        if (!container) return;

        const alertId = 'api-key-alert-' + Date.now();

        const alertHtml = `
            <div id="${alertId}" class="api-key-alert ${type}">
                ${message}
            </div>
        `;

        container.innerHTML = alertHtml;

        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.remove();
                }
            }, 5000);
        }
    }

    clearApiKeyAlert() {
        const container = document.getElementById('apiKeyAlertContainer');
        if (container) {
            container.innerHTML = '';
        }
    }

    resetTestResults() {
        const resultsContainer = document.getElementById('testResults');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="test-placeholder">
                    <i class="fas fa-flask"></i>
                    <p>Click "Start Testing" to test all available models</p>
                </div>
            `;
        }

        // Hide export button
        const exportBtn = document.getElementById('exportTestResults');
        if (exportBtn) exportBtn.style.display = 'none';

        // Reset testing state
        this.testingInProgress = false;
        this.testResults = [];
    }

    async startModelTesting() {
        if (this.testingInProgress) return;

        this.testingInProgress = true;
        this.testResults = [];

        // Get test parameters
        const content = document.getElementById('testContent').value.trim();
        const questionType = document.getElementById('testQuestionType').value;
        const questionCount = parseInt(document.getElementById('testQuestionCount').value) || 3;

        if (!content) {
            this.showNotification('Please enter test content', 'error');
            this.testingInProgress = false;
            return;
        }

        // Update UI
        const startBtn = document.getElementById('startModelTest');
        const stopBtn = document.getElementById('stopModelTest');
        if (startBtn) startBtn.style.display = 'none';
        if (stopBtn) stopBtn.style.display = 'inline-flex';

        try {
            // Get all available models
            const result = await window.electronAPI.getAllModels();
            if (!result.success || !result.models) {
                throw new Error('Failed to get models list');
            }

            const models = result.models;
            this.setupTestResultsDisplay(models);

            // Test each model sequentially
            for (let i = 0; i < models.length && this.testingInProgress; i++) {
                const model = models[i];
                await this.testSingleModel(model, content, questionType, questionCount, i);

                // Small delay between tests
                if (this.testingInProgress) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

        } catch (error) {
            console.error('Error during model testing:', error);
            this.showNotification('Error during testing: ' + error.message, 'error');
        } finally {
            this.finishModelTesting();
        }
    }

    setupTestResultsDisplay(models) {
        const resultsContainer = document.getElementById('testResults');
        if (!resultsContainer) return;

        const modelsHtml = models.map((model, index) => `
            <div class="test-item" id="test-item-${index}">
                <div class="test-model-info">
                    <div class="test-model-name">${model.name || model.id}</div>
                    <div class="test-model-id">${model.id}</div>
                </div>
                <div class="test-status">
                    <div class="test-status-icon test-status-pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <span class="test-timing">Pending...</span>
                </div>
            </div>
        `).join('');

        resultsContainer.innerHTML = modelsHtml;
    }

    async testSingleModel(model, content, questionType, questionCount, index) {
        const testItem = document.getElementById(`test-item-${index}`);
        if (!testItem) return;

        const statusIcon = testItem.querySelector('.test-status-icon');
        const timing = testItem.querySelector('.test-timing');

        // Update to testing state
        statusIcon.className = 'test-status-icon test-status-testing';
        statusIcon.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        timing.textContent = 'Testing...';

        const startTime = Date.now();

        try {
            // Test the model using dedicated test function
            const result = await window.electronAPI.testModel(
                model.id,
                content,
                questionType,
                questionCount
            );

            const endTime = Date.now();
            const duration = endTime - startTime;

            if (result.success && result.questions && result.questions.length > 0) {
                // Success
                statusIcon.className = 'test-status-icon test-status-success';
                statusIcon.innerHTML = '<i class="fas fa-check"></i>';
                timing.innerHTML = `
                    <span class="test-timing">✓ ${duration}ms</span>
                    <div class="test-success-info">${result.questions.length} questions generated</div>
                `;

                this.testResults.push({
                    model: model,
                    success: true,
                    duration: duration,
                    questionsGenerated: result.questions.length,
                    questions: result.questions
                });
            } else {
                // Failed
                statusIcon.className = 'test-status-icon test-status-failed';
                statusIcon.innerHTML = '<i class="fas fa-times"></i>';
                timing.innerHTML = `
                    <span class="test-timing">✗ ${duration}ms</span>
                    <div class="test-error">${result.error || 'No questions generated'}</div>
                `;

                this.testResults.push({
                    model: model,
                    success: false,
                    duration: duration,
                    error: result.error || 'No questions generated'
                });
            }

        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            // Error
            statusIcon.className = 'test-status-icon test-status-failed';
            statusIcon.innerHTML = '<i class="fas fa-exclamation"></i>';
            timing.innerHTML = `
                <span class="test-timing">✗ ${duration}ms</span>
                <div class="test-error">${error.message}</div>
            `;

            this.testResults.push({
                model: model,
                success: false,
                duration: duration,
                error: error.message
            });
        }
    }

    stopModelTesting() {
        this.testingInProgress = false;
        this.finishModelTesting();
    }

    finishModelTesting() {
        this.testingInProgress = false;

        // Update UI
        const startBtn = document.getElementById('startModelTest');
        const stopBtn = document.getElementById('stopModelTest');
        const exportBtn = document.getElementById('exportTestResults');

        if (startBtn) startBtn.style.display = 'inline-flex';
        if (stopBtn) stopBtn.style.display = 'none';
        if (exportBtn && this.testResults.length > 0) exportBtn.style.display = 'inline-flex';

        // Show summary
        const successCount = this.testResults.filter(r => r.success).length;
        const totalCount = this.testResults.length;

        if (totalCount > 0) {
            this.showNotification(
                `Testing complete: ${successCount}/${totalCount} models working`,
                successCount > 0 ? 'success' : 'warning'
            );
        }
    }

    async clearAllRateLimits() {
        try {
            const result = await window.electronAPI.clearAllRateLimits();

            if (result.success) {
                const clearedCount = result.clearedModels ? result.clearedModels.length : 0;
                this.showNotification(
                    `Cleared rate limits for ${clearedCount} models`,
                    'success'
                );

                // If we have cleared models, show which ones
                if (clearedCount > 0) {
                    console.log('Cleared rate limits for models:', result.clearedModels);
                }
            } else {
                this.showNotification('Failed to clear rate limits: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('Error clearing rate limits:', error);
            this.showNotification('Error clearing rate limits: ' + error.message, 'error');
        }
    }

    async exportTestResults() {
        if (!this.testResults || this.testResults.length === 0) {
            this.showNotification('No test results to export', 'warning');
            return;
        }

        try {
            // Create export data
            const exportData = {
                timestamp: new Date().toISOString(),
                testParameters: {
                    content: document.getElementById('testContent').value.trim(),
                    questionType: document.getElementById('testQuestionType').value,
                    questionCount: parseInt(document.getElementById('testQuestionCount').value) || 3
                },
                results: this.testResults.map(result => ({
                    modelId: result.model.id,
                    modelName: result.model.name || result.model.id,
                    success: result.success,
                    duration: result.duration,
                    questionsGenerated: result.questionsGenerated || 0,
                    error: result.error || null
                })),
                summary: {
                    totalModels: this.testResults.length,
                    successfulModels: this.testResults.filter(r => r.success).length,
                    failedModels: this.testResults.filter(r => !r.success).length,
                    averageDuration: Math.round(
                        this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length
                    )
                }
            };

            // Generate filename
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const filename = `model-test-results-${timestamp}.json`;

            // Save file
            const result = await window.electronAPI.saveFile({
                defaultPath: filename,
                filters: [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ],
                content: JSON.stringify(exportData, null, 2)
            });

            if (result.success) {
                this.showNotification('Test results exported successfully!', 'success');
            } else {
                this.showNotification('Failed to export test results', 'error');
            }

        } catch (error) {
            console.error('Error exporting test results:', error);
            this.showNotification('Error exporting test results: ' + error.message, 'error');
        }
    }





    displayQuestions() {
        this.showScreen('questionsScreen');
        const questionsDisplay = document.getElementById('questionsDisplay');
        questionsDisplay.innerHTML = '';

        // Show questions without answers initially
        this.currentQuestions.forEach((question, index) => {
            const questionElement = this.createQuestionElement(question, index, false); // false = hide answers
            questionsDisplay.appendChild(questionElement);
        });

        // Hide export button initially
        document.getElementById('exportQuestionsBtn').style.display = 'none';
    }

    createQuestionElement(question, index, showAnswers = true) {
        const questionDiv = document.createElement('div');
        questionDiv.className = 'question-item';

        let optionsHtml = '';
        if (question.options) {
            // MCQ question
            optionsHtml = question.options.map((option, optIndex) => {
                const letter = String.fromCharCode(65 + optIndex); // A, B, C, D
                const isCorrect = option === question.answer;
                return `
                    <div class="option-item ${showAnswers && isCorrect ? 'correct' : ''}">
                        <div class="option-letter">${letter}</div>
                        <span>${option}</span>
                    </div>
                `;
            }).join('');
        } else {
            // True/False question - use normalized comparison
            const normalizedAnswer = this.normalizeAnswer(question.answer);
            optionsHtml = `
                <div class="option-item ${showAnswers && normalizedAnswer === 'true' ? 'correct' : ''}">
                    <div class="option-letter">T</div>
                    <span>True</span>
                </div>
                <div class="option-item ${showAnswers && normalizedAnswer === 'false' ? 'correct' : ''}">
                    <div class="option-letter">F</div>
                    <span>False</span>
                </div>
            `;
        }

        questionDiv.innerHTML = `
            <div class="question-header">
                <div class="question-number">${index + 1}</div>
                <div class="question-text">${question.question}</div>
            </div>
            <div class="question-options">
                ${optionsHtml}
            </div>
            ${showAnswers ? `
                <div class="question-answer">
                    <div class="answer-label">Correct Answer:</div>
                    <div>${this.formatAnswerForDisplay(question.answer, this.selectedQuestionType)}</div>
                </div>
                ${question.explanation ? `
                    <div class="question-explanation">
                        <div class="explanation-label">Explanation:</div>
                        <div>${question.explanation}</div>
                    </div>
                ` : ''}
            ` : ''}
        `;

        return questionDiv;
    }

    showQuestionsWithAnswers() {
        const questionsDisplay = document.getElementById('questionsDisplay');
        questionsDisplay.innerHTML = '';

        // Show questions with answers and explanations
        this.currentQuestions.forEach((question, index) => {
            const questionElement = this.createQuestionElement(question, index, true); // true = show answers
            questionsDisplay.appendChild(questionElement);
        });

        // Show export button when answers are visible
        document.getElementById('exportQuestionsBtn').style.display = 'inline-block';

        // Update the button to "Hide Answers"
        const showAnswersBtn = document.getElementById('showAnswersBtn');
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const hideAnswersText = window.translations[currentLanguage].hideAnswers;
        showAnswersBtn.innerHTML = `<i class="fas fa-eye-slash"></i> ${hideAnswersText}`;

        // Remove existing event listener and add new one
        showAnswersBtn.replaceWith(showAnswersBtn.cloneNode(true));
        const newShowAnswersBtn = document.getElementById('showAnswersBtn');
        newShowAnswersBtn.addEventListener('click', () => {
            this.hideAnswers();
        });
    }

    hideAnswers() {
        const questionsDisplay = document.getElementById('questionsDisplay');
        questionsDisplay.innerHTML = '';

        // Show questions without answers
        this.currentQuestions.forEach((question, index) => {
            const questionElement = this.createQuestionElement(question, index, false); // false = hide answers
            questionsDisplay.appendChild(questionElement);
        });

        // Hide export button when answers are hidden
        document.getElementById('exportQuestionsBtn').style.display = 'none';

        // Update the button back to "Show Questions with Answers"
        const showAnswersBtn = document.getElementById('showAnswersBtn');
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const showAnswersText = window.translations[currentLanguage].showQuestionsWithAnswers;
        showAnswersBtn.innerHTML = `<i class="fas fa-eye"></i> ${showAnswersText}`;

        // Remove existing event listener and add new one
        showAnswersBtn.replaceWith(showAnswersBtn.cloneNode(true));
        const newShowAnswersBtn = document.getElementById('showAnswersBtn');
        newShowAnswersBtn.addEventListener('click', () => {
            this.showQuestionsWithAnswers();
        });
    }

    async exportQuestionsToPDF() {
        try {
            if (!this.currentQuestions || this.currentQuestions.length === 0) {
                this.showNotification('No questions to export', 'warning');
                return;
            }

            // Show loading notification
            this.showNotification('Preparing PDF export...', 'info');

            // Use Electron's save dialog to let user choose location
            const result = await window.electronAPI.saveFile({
                title: 'Save Questions as PDF',
                defaultPath: `questions_${this.selectedQuestionType}_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.success && result.filePath) {
                // Generate PDF content
                const pdfContent = this.generatePDFContent();

                // Save PDF through main process
                const saveResult = await window.electronAPI.savePDF(result.filePath, pdfContent);

                if (saveResult.success) {
                    this.showNotification(`PDF exported successfully to ${result.filePath}`, 'success');
                } else {
                    throw new Error(saveResult.error || 'Failed to save PDF');
                }
            } else if (result.canceled) {
                this.showNotification('Export canceled', 'info');
            } else {
                throw new Error('Failed to select save location');
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification(`Export failed: ${error.message}`, 'error');
        }
    }

    generatePDFContent() {
        // Generate HTML content for PDF
        let htmlContent = `
            <html>
            <head>
                <title>${this.selectedQuestionType} Questions</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                    .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 10px; }
                    .question { margin-bottom: 25px; page-break-inside: avoid; }
                    .question-number { font-weight: bold; color: #333; margin-bottom: 8px; }
                    .question-text { font-size: 16px; margin-bottom: 12px; }
                    .options { margin-left: 20px; margin-bottom: 12px; }
                    .option { margin-bottom: 5px; }
                    .correct-answer { background-color: #d4edda; padding: 8px; border-left: 4px solid #28a745; margin-bottom: 10px; }
                    .explanation { background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; font-style: italic; }
                    .answer-label { font-weight: bold; color: #28a745; }
                    .explanation-label { font-weight: bold; color: #007bff; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${this.selectedQuestionType} Questions</h1>
                    <p>Generated on ${new Date().toLocaleDateString('en-US')}</p>
                    <p>Total Questions: ${this.currentQuestions.length}</p>
                </div>
        `;

        this.currentQuestions.forEach((question, index) => {
            htmlContent += `
                <div class="question">
                    <div class="question-number">Question ${index + 1}</div>
                    <div class="question-text">${question.question}</div>
                    <div class="options">
            `;

            if (question.options) {
                // MCQ question
                question.options.forEach((option, optIndex) => {
                    const letter = String.fromCharCode(65 + optIndex);
                    htmlContent += `<div class="option">${letter}. ${option}</div>`;
                });
            } else {
                // True/False question
                htmlContent += `
                    <div class="option">T. True</div>
                    <div class="option">F. False</div>
                `;
            }

            htmlContent += `
                    </div>
                    <div class="correct-answer">
                        <span class="answer-label">Correct Answer:</span>
                        ${this.formatAnswerForDisplay(question.answer, this.selectedQuestionType)}
                    </div>
            `;

            if (question.explanation) {
                htmlContent += `
                    <div class="explanation">
                        <span class="explanation-label">Explanation:</span>
                        ${question.explanation}
                    </div>
                `;
            }

            htmlContent += `</div>`;
        });

        htmlContent += `
            </body>
            </html>
        `;

        return htmlContent;
    }

    // History and Statistics Methods
    async showHistoryScreen() {
        console.log('Showing history screen...');
        this.showScreen('historyScreen');
        await this.loadHistory();
        await this.loadSavedQuizzes();
    }

    async showStatisticsScreen() {
        console.log('Showing statistics screen...');
        this.showScreen('statisticsScreen');
        await this.loadStatistics();
    }











    async loadHistory() {
        try {
            // Reset pagination when loading
            this.historyPage = 1;

            const historyList = document.getElementById('historyList');
            // Get current language for translations
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const loadingText = isArabic ? 'تحميل تاريخ الاختبارات...' : 'Loading quiz history...';

            historyList.innerHTML = `<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i><p>${loadingText}</p></div>`;

            const result = await window.electronAPI.getQuizHistory();

            if (result.success && result.sessions && result.sessions.length > 0) {
                this.displayHistory(result.sessions);
                this.updateHistorySummary(result.sessions);
            } else {
                // Show empty state when no history exists
                this.showEmptyHistory();
                this.updateHistorySummary([]); // Update summary with empty data
            }
        } catch (error) {
            console.error('Error loading history:', error);
            // Show empty state on error
            this.showEmptyHistory();
            this.updateHistorySummary([]);
        }
    }

    async loadSavedQuizzes() {
        try {
            // Reset pagination when loading
            this.savedQuizzesPage = 1;

            const savedQuizzesList = document.getElementById('savedQuizzesList');
            // Get current language for translations
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const loadingText = isArabic ? 'تحميل الاختبارات المحفوظة...' : 'Loading saved quizzes...';

            savedQuizzesList.innerHTML = `<div class="loading-placeholder"><i class="fas fa-spinner fa-spin"></i><p>${loadingText}</p></div>`;

            // Automatically fix Islamic dates in existing quiz titles (silently)
            await this.autoFixIslamicDates();

            const result = await window.electronAPI.getSavedQuizzes();
            console.log('Get saved quizzes result:', result);

            if (result.success && result.savedQuizzes && result.savedQuizzes.length > 0) {
                console.log(`Displaying ${result.savedQuizzes.length} saved quizzes`);
                this.displaySavedQuizzes(result.savedQuizzes);
            } else {
                console.log('No saved quizzes found, showing empty state');
                this.showEmptySavedQuizzes();
            }
        } catch (error) {
            console.error('Error loading saved quizzes:', error);
            this.showEmptySavedQuizzes();
        }
    }

    displaySavedQuizzes(savedQuizzes) {
        const savedQuizzesList = document.getElementById('savedQuizzesList');

        if (!savedQuizzes || savedQuizzes.length === 0) {
            this.showEmptySavedQuizzes();
            document.getElementById('savedQuizzesPagination').style.display = 'none';
            return;
        }

        // Store all quizzes for pagination
        this.allSavedQuizzes = savedQuizzes;

        // Calculate pagination
        const startIndex = (this.savedQuizzesPage - 1) * this.savedQuizzesPerPage;
        const endIndex = startIndex + this.savedQuizzesPerPage;
        const paginatedQuizzes = savedQuizzes.slice(startIndex, endIndex);

        // Clear and populate list
        savedQuizzesList.innerHTML = '';

        paginatedQuizzes.forEach(quiz => {
            const quizItem = this.createSavedQuizItem(quiz);
            savedQuizzesList.appendChild(quizItem);
        });

        // Update pagination controls
        this.updateSavedQuizzesPagination();
    }

    createSavedQuizItem(quiz) {
        const item = document.createElement('div');
        item.className = 'saved-quiz-item';

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Fix date parsing and formatting
        let date;
        try {
            date = new Date(quiz.created_at || quiz.createdAt);
            // If date is invalid, use current date
            if (isNaN(date.getTime())) {
                date = new Date();
            }
        } catch (error) {
            console.warn('Error parsing quiz date:', error);
            date = new Date();
        }

        const questionCount = quiz.questions ? quiz.questions.length : 0;

        // Get the question type from either field name (handle both camelCase and snake_case)
        const questionType = quiz.questionType || quiz.question_type || 'Quiz';

        item.innerHTML = `
            <div class="saved-quiz-header">
                <div class="saved-quiz-title">${quiz.title}</div>
                <div class="saved-quiz-type">${questionType}</div>
            </div>
            <div class="saved-quiz-info">
                <div class="saved-quiz-date">
                    <i class="fas fa-calendar"></i>
                    ${date.toLocaleDateString('en-US')}
                </div>
                <div class="saved-quiz-questions">
                    <i class="fas fa-question-circle"></i>
                    ${questionCount} ${isArabic ? 'سؤال' : 'questions'}
                </div>
            </div>
            <div class="saved-quiz-actions">
                <button class="btn btn-primary btn-sm take-quiz-btn" data-quiz-id="${quiz.id}">
                    <i class="fas fa-play"></i> ${isArabic ? 'خذ الاختبار' : 'Take Quiz'}
                </button>
                <button class="btn btn-danger btn-sm delete-quiz-btn" data-quiz-id="${quiz.id}">
                    <i class="fas fa-trash"></i> ${isArabic ? 'حذف' : 'Delete'}
                </button>
            </div>
        `;

        // Add event listeners
        const takeQuizBtn = item.querySelector('.take-quiz-btn');
        const deleteQuizBtn = item.querySelector('.delete-quiz-btn');

        takeQuizBtn.addEventListener('click', () => this.takeSavedQuiz(quiz));
        deleteQuizBtn.addEventListener('click', () => this.deleteSavedQuiz(quiz.id));

        return item;
    }

    showEmptySavedQuizzes() {
        const savedQuizzesList = document.getElementById('savedQuizzesList');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        savedQuizzesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-bookmark"></i>
                <h3>${isArabic ? 'لا توجد اختبارات محفوظة' : 'No Saved Quizzes'}</h3>
                <p>${isArabic ? 'احفظ اختباراً من صفحة النتائج لرؤيته هنا!' : 'Save a quiz from the results page to see it here!'}</p>
            </div>
        `;
    }

    async clearAllSavedQuizzes() {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const confirmMessage = isArabic
            ? 'هل أنت متأكد من أنك تريد حذف جميع الاختبارات المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.'
            : 'Are you sure you want to delete all saved quizzes? This action cannot be undone.';

        if (confirm(confirmMessage)) {
            try {
                const result = await window.electronAPI.clearAllSavedQuizzes();

                if (result.success) {
                    const successMessage = isArabic ? 'تم حذف جميع الاختبارات المحفوظة بنجاح' : 'All saved quizzes deleted successfully';
                    this.showNotification(successMessage, 'success');
                    await this.loadSavedQuizzes(); // Reload the list
                } else {
                    const errorMessage = isArabic ? 'فشل في حذف الاختبارات المحفوظة' : 'Failed to delete saved quizzes';
                    this.showNotification(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Error clearing all saved quizzes:', error);
                const errorMessage = isArabic ? 'حدث خطأ أثناء حذف الاختبارات المحفوظة' : 'An error occurred while deleting saved quizzes';
                this.showNotification(errorMessage, 'error');
            }
        }
    }

    async autoFixIslamicDates() {
        // Silently fix Islamic dates without showing notifications
        try {
            const result = await window.electronAPI.fixIslamicDates();

            if (result.success && result.updatedCount > 0) {
                console.log(`Automatically fixed ${result.updatedCount} Islamic dates in quiz titles`);
            }
        } catch (error) {
            console.warn('Error auto-fixing Islamic dates:', error);
            // Don't show error notifications for automatic fixes
        }
    }

    async takeSavedQuiz(quiz) {
        try {
            // Load the saved quiz questions
            this.currentQuestions = quiz.questions;
            this.selectedQuestionType = quiz.questionType || quiz.question_type || 'MCQ';
            this.currentFileName = quiz.source;

            // Get current language for user-friendly messages
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';

            const message = isArabic ?
                `تم تحميل الاختبار: ${quiz.title}` :
                `Loaded quiz: ${quiz.title}`;
            this.showNotification(message, 'success');

            // Start the quiz
            this.startInteractiveQuiz();
        } catch (error) {
            console.error('Error taking saved quiz:', error);
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ?
                'فشل في تحميل الاختبار المحفوظ.' :
                'Failed to load saved quiz.';
            this.showNotification(message, 'error');
        }
    }

    async deleteSavedQuiz(quizId) {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const confirmMessage = isArabic
            ? 'هل أنت متأكد من أنك تريد حذف هذا الاختبار المحفوظ؟'
            : 'Are you sure you want to delete this saved quiz?';

        if (confirm(confirmMessage)) {
            try {
                // TODO: Implement delete saved quiz API call
                const result = await window.electronAPI.deleteSavedQuiz(quizId);

                if (result.success) {
                    const successMessage = isArabic ? 'تم حذف الاختبار بنجاح' : 'Quiz deleted successfully';
                    this.showNotification(successMessage, 'success');
                    await this.loadSavedQuizzes(); // Reload the list
                } else {
                    const errorMessage = isArabic ? 'فشل في حذف الاختبار' : 'Failed to delete quiz';
                    this.showNotification(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Error deleting saved quiz:', error);
                const errorMessage = isArabic ? 'حدث خطأ أثناء حذف الاختبار' : 'An error occurred while deleting the quiz';
                this.showNotification(errorMessage, 'error');
            }
        }
    }

    displaySampleHistory() {
        const sampleSessions = [
            {
                id: 1,
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
                question_type: 'MCQ',
                score_correct: 8,
                score_total: 10,
                duration: 180,
                answers: '[]',
                questions: '[]'
            },
            {
                id: 2,
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
                question_type: 'TF',
                score_correct: 7,
                score_total: 10,
                duration: 120,
                answers: '[]',
                questions: '[]'
            },
            {
                id: 3,
                timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
                question_type: 'MCQ',
                score_correct: 9,
                score_total: 12,
                duration: 240,
                answers: '[]',
                questions: '[]'
            },
            {
                id: 4,
                timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 1 week ago
                question_type: 'TF',
                score_correct: 15,
                score_total: 15,
                duration: 300,
                answers: '[]',
                questions: '[]'
            }
        ];

        this.displayHistory(sampleSessions);
        this.updateHistorySummary(sampleSessions);
    }

    displayHistory(sessions) {
        const historyList = document.getElementById('historyList');

        if (!sessions || sessions.length === 0) {
            this.showEmptyHistory();
            document.getElementById('historyPagination').style.display = 'none';
            return;
        }

        // Store filtered sessions for pagination (don't overwrite allHistorySessions if this is filtered data)
        const isFiltered = this.isHistoryFiltered();
        if (!isFiltered) {
            this.allHistorySessions = sessions;
        }

        // Calculate pagination
        const startIndex = (this.historyPage - 1) * this.historyPerPage;
        const endIndex = startIndex + this.historyPerPage;
        const paginatedSessions = sessions.slice(startIndex, endIndex);

        // Clear and populate list
        historyList.innerHTML = '';

        paginatedSessions.forEach(session => {
            const historyItem = this.createHistoryItem(session);
            historyList.appendChild(historyItem);
        });

        // Update pagination controls with current session data
        this.updateHistoryPagination(sessions);
    }

    isHistoryFiltered() {
        const typeFilter = document.getElementById('historyTypeFilter')?.value;
        const dateFilter = document.getElementById('historyDateFilter')?.value;
        return (typeFilter && typeFilter !== 'all') || (dateFilter && dateFilter !== 'all');
    }

    createHistoryItem(session) {
        const item = document.createElement('div');
        item.className = 'history-item';

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Fix date parsing for session timestamp
        let date;
        try {
            date = new Date(session.timestamp);
            if (isNaN(date.getTime())) {
                date = new Date(); // Use current date if invalid
            }
        } catch (error) {
            console.warn('Error parsing session timestamp:', error);
            date = new Date();
        }
        // Calculate percentage with null safety and NaN protection
        let scorePercentage = 0;
        if (session.score_total > 0 && session.score_correct >= 0) {
            scorePercentage = Math.round((session.score_correct / session.score_total) * 100);
            if (isNaN(scorePercentage)) {
                scorePercentage = 0;
            }
        }
        const scoreClass = this.getScoreClass(scorePercentage);
        const duration = this.formatDuration(session.duration);

        // Get translated question type - handle both string and object cases
        let questionType = session.question_type;
        if (typeof questionType === 'object') {
            questionType = 'MCQ'; // Default fallback
        }

        const questionTypeText = questionType === 'MCQ'
            ? (isArabic ? 'متعدد الخيارات' : 'Multiple Choice')
            : (isArabic ? 'صح أو خطأ' : 'True/False');

        item.innerHTML = `
            <div class="history-item-header">
                <div class="history-item-title">
                    <i class="fas fa-${questionType === 'MCQ' ? 'list' : 'check'}"></i>
                    ${questionTypeText} ${isArabic ? 'اختبار' : 'Quiz'}
                </div>
                <div class="history-item-date">
                    ${date.toLocaleDateString('en-US')} ${date.toLocaleTimeString('en-US', { hour12: false })}
                </div>
            </div>
            <div class="history-item-stats">
                <div class="history-stat">
                    <div class="history-stat-value ${scoreClass}">${scorePercentage}%</div>
                    <div class="history-stat-label">${isArabic ? 'النتيجة' : 'Score'}</div>
                </div>
                <div class="history-stat">
                    <div class="history-stat-value">${session.score_correct}/${session.score_total}</div>
                    <div class="history-stat-label">${isArabic ? 'صحيح' : 'Correct'}</div>
                </div>
                <div class="history-stat">
                    <div class="history-stat-value">${duration}</div>
                    <div class="history-stat-label">${isArabic ? 'المدة' : 'Duration'}</div>
                </div>
                <div class="history-stat">
                    <div class="history-stat-value">${session.score_total}</div>
                    <div class="history-stat-label">${isArabic ? 'الأسئلة' : 'Questions'}</div>
                </div>
            </div>
        `;

        return item;
    }

    getScoreClass(percentage) {
        if (percentage >= 90) return 'score-excellent';
        if (percentage >= 75) return 'score-good';
        if (percentage >= 60) return 'score-average';
        return 'score-poor';
    }

    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        if (minutes > 0) {
            return `${minutes}m ${remainingSeconds}s`;
        }
        return `${remainingSeconds}s`;
    }

    updateHistorySummary(sessions) {
        if (!sessions || sessions.length === 0) {
            document.getElementById('totalQuizzesHistory').textContent = '0';
            document.getElementById('avgScoreHistory').textContent = '0%';
            document.getElementById('avgTimeHistory').textContent = '0m';
            return;
        }

        const totalQuizzes = sessions.length;
        const totalScore = sessions.reduce((sum, session) =>
            sum + (session.score_correct / session.score_total), 0);
        const avgScore = Math.round((totalScore / totalQuizzes) * 100);
        const totalTime = sessions.reduce((sum, session) => sum + session.duration, 0);
        const avgTime = Math.round(totalTime / totalQuizzes);

        document.getElementById('totalQuizzesHistory').textContent = totalQuizzes;
        document.getElementById('avgScoreHistory').textContent = `${avgScore}%`;
        document.getElementById('avgTimeHistory').textContent = this.formatDuration(avgTime);
    }

    showEmptyHistory() {
        const historyList = document.getElementById('historyList');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        historyList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-history"></i>
                <h3>${isArabic ? 'لا يوجد تاريخ اختبارات' : 'No Quiz History'}</h3>
                <p>${isArabic ? 'لم تقم بأي اختبارات بعد.<br>ابدأ اختباراً لرؤية تاريخك هنا!' : 'You haven\'t taken any quizzes yet.<br>Start a quiz to see your history here!'}</p>
            </div>
        `;
    }

    async loadStatistics() {
        try {
            // Load quiz history to calculate statistics from actual data
            const historyResult = await window.electronAPI.getQuizHistory();

            if (historyResult.success && historyResult.sessions && historyResult.sessions.length > 0) {
                // Calculate statistics from quiz history
                this.calculateStatisticsFromHistory(historyResult.sessions);
                this.calculateDetailedStats(historyResult.sessions);
            } else {
                // Show empty statistics when no quiz history exists
                this.showEmptyStatistics();
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
            // Show empty statistics on error
            this.showEmptyStatistics();
        }
    }

    displaySampleStatistics() {
        const sampleStats = {
            totalQuizzes: 4,
            averageScore: 82,
            totalQuestions: 47,
            lastQuizDate: new Date().toISOString()
        };

        this.displayStatistics(sampleStats);

        // Use the same sample sessions for detailed stats
        const sampleSessions = [
            {
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                question_type: 'MCQ',
                score_correct: 8,
                score_total: 10,
                duration: 180
            },
            {
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                question_type: 'TF',
                score_correct: 7,
                score_total: 10,
                duration: 120
            },
            {
                timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                question_type: 'MCQ',
                score_correct: 9,
                score_total: 12,
                duration: 240
            },
            {
                timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                question_type: 'TF',
                score_correct: 15,
                score_total: 15,
                duration: 300
            }
        ];

        this.calculateDetailedStats(sampleSessions);
    }

    async loadDetailedStats() {
        try {
            const historyResult = await window.electronAPI.getQuizHistory();

            if (historyResult.success && historyResult.sessions) {
                this.calculateDetailedStats(historyResult.sessions);
            }
        } catch (error) {
            console.error('Error loading detailed stats:', error);
        }
    }

    calculateStatisticsFromHistory(sessions) {
        if (!sessions || sessions.length === 0) {
            this.showEmptyStatistics();
            return;
        }

        // Calculate overall statistics
        let totalCorrect = 0;
        let totalQuestions = 0;
        let totalQuizzes = sessions.length;

        sessions.forEach(session => {
            totalCorrect += session.score_correct || 0;
            totalQuestions += session.score_total || 0;
        });

        // Calculate average score
        const averageScore = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;

        // Update overview stats
        document.getElementById('overallScore').textContent = `${averageScore}%`;
        document.getElementById('currentStreak').textContent = '0'; // TODO: Implement streak calculation

        // Update basic stats
        document.getElementById('totalQuestions').textContent = totalQuestions;
        document.getElementById('correctAnswers').textContent = totalCorrect;
        document.getElementById('incorrectAnswers').textContent = totalQuestions - totalCorrect;
    }

    displayStatistics(stats) {
        // Update overview stats with NaN protection
        const averageScore = isNaN(stats.averageScore) || stats.averageScore === null ? 0 : stats.averageScore;
        document.getElementById('overallScore').textContent = `${averageScore}%`;
        document.getElementById('currentStreak').textContent = '0'; // TODO: Implement streak calculation

        // Update basic stats with null safety
        document.getElementById('totalQuestions').textContent = stats.totalQuestions || 0;
        document.getElementById('correctAnswers').textContent = '0'; // Will be calculated from detailed stats
        document.getElementById('incorrectAnswers').textContent = '0'; // Will be calculated from detailed stats
    }

    calculateDetailedStats(sessions) {
        if (!sessions || sessions.length === 0) {
            this.showEmptyStatistics();
            return;
        }

        // Calculate totals
        let totalCorrect = 0;
        let totalQuestions = 0;
        let mcqCount = 0;
        let tfCount = 0;
        let todayCount = 0;
        let weekCount = 0;
        let monthCount = 0;

        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

        sessions.forEach(session => {
            totalCorrect += session.score_correct;
            totalQuestions += session.score_total;

            if (session.question_type === 'MCQ') mcqCount++;
            else tfCount++;

            // Fix date parsing for session timestamp
            let sessionDate;
            try {
                sessionDate = new Date(session.timestamp);
                if (isNaN(sessionDate.getTime())) {
                    sessionDate = new Date(); // Use current date if invalid
                }
            } catch (error) {
                sessionDate = new Date();
            }
            if (sessionDate.toDateString() === today.toDateString()) todayCount++;
            if (sessionDate >= weekAgo) weekCount++;
            if (sessionDate >= monthAgo) monthCount++;
        });

        const totalIncorrect = totalQuestions - totalCorrect;

        // Update performance metrics
        document.getElementById('totalQuestions').textContent = totalQuestions;
        document.getElementById('correctAnswers').textContent = totalCorrect;
        document.getElementById('incorrectAnswers').textContent = totalIncorrect;

        // Update question types with progress bars
        const totalQuizzes = sessions.length;
        const mcqPercentage = totalQuizzes > 0 ? Math.round((mcqCount / totalQuizzes) * 100) : 0;
        const tfPercentage = totalQuizzes > 0 ? Math.round((tfCount / totalQuizzes) * 100) : 0;

        document.getElementById('mcqQuizzes').textContent = mcqCount;
        document.getElementById('tfQuizzes').textContent = tfCount;
        document.getElementById('mcqPercentage').textContent = `${mcqPercentage}%`;
        document.getElementById('tfPercentage').textContent = `${tfPercentage}%`;

        // Animate progress bars
        setTimeout(() => {
            document.getElementById('mcqProgress').style.width = `${mcqPercentage}%`;
            document.getElementById('tfProgress').style.width = `${tfPercentage}%`;
        }, 500);

        // Update activity stats
        document.getElementById('quizzesToday').textContent = todayCount;
        document.getElementById('quizzesThisWeek').textContent = weekCount;
        document.getElementById('quizzesThisMonth').textContent = monthCount;
    }



    showEmptyStatistics() {
        // Show empty state for statistics
        const statsContent = document.querySelector('.statistics-content');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        statsContent.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-chart-bar"></i>
                <h3>${isArabic ? 'لا توجد إحصائيات متاحة' : 'No Statistics Available'}</h3>
                <p>${isArabic ? 'قم ببعض الاختبارات لرؤية إحصائياتك وإنجازاتك!' : 'Take some quizzes to see your statistics and achievements!'}</p>
            </div>
        `;
    }

    async clearHistory() {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const confirmMessage = isArabic
            ? 'هل أنت متأكد من أنك تريد مسح كل تاريخ الاختبارات؟ لا يمكن التراجع عن هذا الإجراء.'
            : 'Are you sure you want to clear all quiz history? This action cannot be undone.';

        if (confirm(confirmMessage)) {
            try {
                // Call the backend API to clear history
                const result = await window.electronAPI.clearQuizHistory();

                if (result.success) {
                    const successMessage = isArabic ? 'تم مسح التاريخ بنجاح' : 'History cleared successfully';
                    this.showNotification(successMessage, 'success');

                    // Reload the history page to show empty state
                    await this.loadHistory();

                    // Also refresh statistics since they depend on history data
                    if (this.currentScreen === 'statisticsScreen') {
                        await this.loadStatistics();
                    }
                } else {
                    const errorMessage = isArabic ? 'فشل في مسح التاريخ' : 'Failed to clear history';
                    this.showNotification(errorMessage, 'error');
                }
            } catch (error) {
                console.error('Error clearing history:', error);
                const errorMessage = isArabic ? 'فشل في مسح التاريخ' : 'Failed to clear history';
                this.showNotification(errorMessage, 'error');
            }
        }
    }



    filterHistory() {
        if (!this.allHistorySessions || this.allHistorySessions.length === 0) {
            return;
        }

        const typeFilter = document.getElementById('historyTypeFilter').value;
        const dateFilter = document.getElementById('historyDateFilter').value;

        let filteredSessions = [...this.allHistorySessions];

        // Filter by question type
        if (typeFilter !== 'all') {
            filteredSessions = filteredSessions.filter(session => {
                let questionType = session.question_type;
                if (typeof questionType === 'object') {
                    questionType = 'MCQ'; // Default fallback
                }
                return questionType === typeFilter;
            });
        }

        // Filter by date
        if (dateFilter !== 'all') {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            filteredSessions = filteredSessions.filter(session => {
                let sessionDate;
                try {
                    sessionDate = new Date(session.timestamp);
                    if (isNaN(sessionDate.getTime())) {
                        return false; // Invalid date
                    }
                } catch (error) {
                    return false; // Invalid date
                }

                switch (dateFilter) {
                    case 'today':
                        return sessionDate >= today;
                    case 'yesterday':
                        return sessionDate >= yesterday && sessionDate < today;
                    case 'week':
                        return sessionDate >= weekAgo;
                    case 'month':
                        return sessionDate >= monthAgo;
                    default:
                        return true;
                }
            });
        }

        // Reset pagination and display filtered results
        this.historyPage = 1;
        this.displayHistory(filteredSessions);
    }

    // Pagination methods
    changeSavedQuizzesPage(direction) {
        const newPage = this.savedQuizzesPage + direction;
        const totalPages = Math.ceil(this.allSavedQuizzes.length / this.savedQuizzesPerPage);

        if (newPage >= 1 && newPage <= totalPages) {
            this.savedQuizzesPage = newPage;
            this.displaySavedQuizzes(this.allSavedQuizzes);
        }
    }

    changeHistoryPage(direction) {
        // Check if we're currently filtering
        if (this.isHistoryFiltered()) {
            // Re-apply filters to get current filtered data
            this.historyPage += direction;
            this.filterHistory();
        } else {
            const newPage = this.historyPage + direction;
            const totalPages = Math.ceil(this.allHistorySessions.length / this.historyPerPage);

            if (newPage >= 1 && newPage <= totalPages) {
                this.historyPage = newPage;
                this.displayHistory(this.allHistorySessions);
            }
        }
    }

    updateSavedQuizzesPagination() {
        const totalPages = Math.ceil(this.allSavedQuizzes.length / this.savedQuizzesPerPage);
        const paginationContainer = document.getElementById('savedQuizzesPagination');
        const pageInfo = document.getElementById('savedQuizzesPageInfo');
        const prevBtn = document.getElementById('savedQuizzesPrevBtn');
        const nextBtn = document.getElementById('savedQuizzesNextBtn');
        const pageNumbers = document.getElementById('savedQuizzesPageNumbers');

        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        pageInfo.textContent = isArabic
            ? `صفحة ${this.savedQuizzesPage} من ${totalPages}`
            : `Page ${this.savedQuizzesPage} of ${totalPages}`;

        prevBtn.disabled = this.savedQuizzesPage === 1;
        nextBtn.disabled = this.savedQuizzesPage === totalPages;

        // Generate page numbers
        pageNumbers.innerHTML = '';
        const startPage = Math.max(1, this.savedQuizzesPage - 2);
        const endPage = Math.min(totalPages, this.savedQuizzesPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.className = `page-number ${i === this.savedQuizzesPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.savedQuizzesPage = i;
                this.displaySavedQuizzes(this.allSavedQuizzes);
            });
            pageNumbers.appendChild(pageBtn);
        }
    }

    updateHistoryPagination(sessions = null) {
        const sessionsToUse = sessions || this.allHistorySessions;
        const totalPages = Math.ceil(sessionsToUse.length / this.historyPerPage);
        const paginationContainer = document.getElementById('historyPagination');
        const pageInfo = document.getElementById('historyPageInfo');
        const prevBtn = document.getElementById('historyPrevBtn');
        const nextBtn = document.getElementById('historyNextBtn');
        const pageNumbers = document.getElementById('historyPageNumbers');

        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'flex';

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        pageInfo.textContent = isArabic
            ? `صفحة ${this.historyPage} من ${totalPages}`
            : `Page ${this.historyPage} of ${totalPages}`;

        prevBtn.disabled = this.historyPage === 1;
        nextBtn.disabled = this.historyPage === totalPages;

        // Generate page numbers
        pageNumbers.innerHTML = '';
        const startPage = Math.max(1, this.historyPage - 2);
        const endPage = Math.min(totalPages, this.historyPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('span');
            pageBtn.className = `page-number ${i === this.historyPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.addEventListener('click', () => {
                this.historyPage = i;
                // Re-apply filters when changing pages
                if (this.isHistoryFiltered()) {
                    this.filterHistory();
                } else {
                    this.displayHistory(this.allHistorySessions);
                }
            });
            pageNumbers.appendChild(pageBtn);
        }
    }

    async exportStatistics() {
        try {
            // Generate statistics content
            const statsContent = this.generateStatisticsContent();

            // Use Electron's save dialog to let user choose location
            const result = await window.electronAPI.saveFile({
                title: 'Save Statistics as PDF',
                defaultPath: `statistics_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.success && result.filePath) {
                // Save PDF through main process
                const saveResult = await window.electronAPI.savePDF(result.filePath, statsContent);

                if (saveResult.success) {
                    this.showNotification('Statistics exported successfully!', 'success');
                } else {
                    this.showNotification('Failed to export statistics', 'error');
                }
            }
        } catch (error) {
            console.error('Error exporting statistics:', error);
            this.showNotification('Failed to export statistics', 'error');
        }
    }

    generateStatisticsContent() {
        // Generate HTML content for statistics PDF
        return `
            <html>
            <head>
                <title>Quiz Statistics</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .stat-section { margin-bottom: 20px; }
                    .stat-item { margin: 10px 0; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Quiz Statistics Report</h1>
                    <p>Generated on ${new Date().toLocaleDateString('en-US')}</p>
                </div>
                <div class="stat-section">
                    <h2>Performance Overview</h2>
                    <div class="stat-item">Overall Score: ${document.getElementById('overallScore')?.textContent || 'N/A'}</div>
                    <div class="stat-item">Total Questions: ${document.getElementById('totalQuestions')?.textContent || 'N/A'}</div>
                    <div class="stat-item">Correct Answers: ${document.getElementById('correctAnswers')?.textContent || 'N/A'}</div>
                </div>
            </body>
            </html>
        `;
    }

    startInteractiveQuiz() {
        if (!this.currentQuestions || this.currentQuestions.length === 0) {
            this.showNotification('No questions available for quiz', 'warning');
            return;
        }

        // Initialize quiz state
        this.quizState = {
            currentQuestionIndex: 0,
            answers: [],
            score: { correct: 0, total: this.currentQuestions.length },
            startTime: new Date(),
            endTime: null
        };

        this.showScreen('quizScreen');
        this.displayQuizQuestion();
    }

    displayQuizQuestion() {
        const currentQuestion = this.currentQuestions[this.quizState.currentQuestionIndex];
        const questionNumber = this.quizState.currentQuestionIndex + 1;
        const totalQuestions = this.currentQuestions.length;

        console.log(`Displaying question ${questionNumber} of ${totalQuestions}`);
        console.log('Question:', currentQuestion.question);

        // Update header
        document.getElementById('questionNumber').textContent = `Question ${questionNumber}`;
        document.getElementById('questionCount').textContent = `of ${totalQuestions}`;
        document.getElementById('currentScore').textContent =
            `${this.quizState.score.correct}/${this.quizState.currentQuestionIndex}`;

        // Display question
        document.getElementById('quizQuestion').textContent = currentQuestion.question;

        // Display options
        const optionsContainer = document.getElementById('quizOptions');
        optionsContainer.innerHTML = '';

        if (currentQuestion.options) {
            // MCQ question
            currentQuestion.options.forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'quiz-option';
                const letter = String.fromCharCode(65 + index); // A, B, C, D
                optionElement.dataset.answer = letter; // Store the letter, not the option text

                optionElement.innerHTML = `
                    <div class="option-indicator">${letter}</div>
                    <span>${option}</span>
                `;

                optionElement.addEventListener('click', () => {
                    this.selectQuizOption(optionElement);
                });

                optionsContainer.appendChild(optionElement);
            });
        } else {
            // True/False question
            ['True', 'False'].forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'quiz-option';
                optionElement.dataset.answer = option;

                optionElement.innerHTML = `
                    <div class="option-indicator">${option[0]}</div>
                    <span>${option}</span>
                `;

                optionElement.addEventListener('click', () => {
                    this.selectQuizOption(optionElement);
                });

                optionsContainer.appendChild(optionElement);
            });
        }

        // Reset UI state
        this.resetQuizButtonStates();

        // Re-enable option selection for new question
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.style.pointerEvents = 'auto';
            option.classList.remove('selected', 'correct', 'incorrect');
        });
    }

    resetQuizButtonStates() {
        console.log('Resetting quiz button states');

        const submitBtn = document.getElementById('submitAnswer');
        const nextBtn = document.getElementById('nextQuestion');
        const finishBtn = document.getElementById('finishQuiz');
        const feedbackElement = document.getElementById('quizFeedback');

        // Reset submit button
        submitBtn.disabled = true;
        submitBtn.classList.remove('hidden');

        // Hide other buttons
        nextBtn.classList.add('hidden');
        finishBtn.classList.add('hidden');

        // Hide feedback
        feedbackElement.classList.add('hidden');

        console.log('Quiz button states reset successfully');
    }

    selectQuizOption(selectedElement) {
        console.log('selectQuizOption called with:', selectedElement);

        // Remove previous selection
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.classList.remove('selected');
        });

        // Select current option
        selectedElement.classList.add('selected');

        // Enable submit button
        const submitBtn = document.getElementById('submitAnswer');
        submitBtn.disabled = false;
        submitBtn.classList.remove('hidden'); // Ensure button is visible

        console.log('Submit button enabled and made visible');
    }

    submitQuizAnswer() {
        console.log('submitQuizAnswer called');

        // Check if button is disabled
        const submitBtn = document.getElementById('submitAnswer');
        if (submitBtn.disabled) {
            console.log('Submit button is disabled, ignoring click');
            return;
        }

        // Add visual feedback to show button was clicked
        submitBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            submitBtn.style.transform = 'scale(1)';
        }, 100);

        const selectedOption = document.querySelector('.quiz-option.selected');
        if (!selectedOption) {
            console.log('No option selected');
            // Get current language for user-friendly message
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ? 'يرجى اختيار إجابة أولاً' : 'Please select an answer first';
            this.showNotification(message, 'warning');
            return;
        }

        const userAnswer = selectedOption.dataset.answer;
        const currentQuestion = this.currentQuestions[this.quizState.currentQuestionIndex];

        // For MCQ questions, compare letters directly; for TF, use normalized comparison
        let isCorrect;
        if (currentQuestion.options) {
            // MCQ: Direct letter comparison (A, B, C, D)
            isCorrect = userAnswer === currentQuestion.answer;
        } else {
            // TF: Use normalized comparison for True/False
            const normalizedUserAnswer = this.normalizeAnswer(userAnswer);
            const normalizedCorrectAnswer = this.normalizeAnswer(currentQuestion.answer);
            isCorrect = normalizedUserAnswer === normalizedCorrectAnswer;
        }

        console.log(`Question ${this.quizState.currentQuestionIndex + 1}:`);
        console.log(`  Question text: "${currentQuestion.question}"`);
        console.log(`  User answered: "${userAnswer}"`);
        console.log(`  Correct answer: "${currentQuestion.answer}"`);
        console.log(`  Question type: ${currentQuestion.options ? 'MCQ' : 'TF'}`);
        console.log(`  Explanation: "${currentQuestion.explanation || 'No explanation'}"`);
        console.log(`  Is correct: ${isCorrect}`);

        // Additional validation for True/False questions
        if (this.selectedQuestionType === 'TF') {
            console.log(`  TF Question Analysis:`);
            console.log(`    - Question asks if statement is true`);
            console.log(`    - AI says correct answer is: ${currentQuestion.answer}`);
            console.log(`    - User selected: ${userAnswer}`);
            console.log(`    - Match result: ${isCorrect}`);

            // Check for potential logic errors
            if (currentQuestion.explanation) {
                const explanation = currentQuestion.explanation.toLowerCase();
                if ((explanation.includes('unknown') || explanation.includes('no specific') || explanation.includes('not identifiable')) &&
                    normalizedCorrectAnswer === 'true') {
                    console.warn(`  ⚠️  POTENTIAL LOGIC ERROR: Explanation suggests FALSE but answer is TRUE`);
                }
                if ((explanation.includes('specific') || explanation.includes('identifiable') || explanation.includes('known cause')) &&
                    normalizedCorrectAnswer === 'false') {
                    console.warn(`  ⚠️  POTENTIAL LOGIC ERROR: Explanation suggests TRUE but answer is FALSE`);
                }
            }
        }

        // Record answer
        this.quizState.answers.push({
            questionIndex: this.quizState.currentQuestionIndex,
            userAnswer: userAnswer,
            correctAnswer: currentQuestion.answer,
            isCorrect: isCorrect,
            question: currentQuestion.question
        });

        if (isCorrect) {
            this.quizState.score.correct++;
        }

        // Show feedback
        this.showQuizFeedback(isCorrect, currentQuestion, userAnswer);

        // Update UI - Hide submit button and show appropriate next button
        submitBtn.classList.add('hidden');
        submitBtn.disabled = true; // Disable to prevent double submission

        const nextBtn = document.getElementById('nextQuestion');
        const finishBtn = document.getElementById('finishQuiz');

        const isLastQuestion = this.quizState.currentQuestionIndex >= this.currentQuestions.length - 1;
        console.log(`Current question index: ${this.quizState.currentQuestionIndex}, Total questions: ${this.currentQuestions.length}, Is last question: ${isLastQuestion}`);

        if (isLastQuestion) {
            nextBtn.classList.add('hidden');
            finishBtn.classList.remove('hidden');
            console.log('Showing finish button');
        } else {
            finishBtn.classList.add('hidden');
            nextBtn.classList.remove('hidden');
            console.log('Showing next button');
        }

        // Disable option selection and highlight correct/incorrect answers
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.style.pointerEvents = 'none';

            // For MCQ questions, compare letters directly; for TF, use normalized comparison
            const optionAnswer = option.dataset.answer;
            const correctAnswer = currentQuestion.answer;

            let isCorrectOption;
            if (currentQuestion.options) {
                // MCQ: Direct letter comparison (A, B, C, D)
                isCorrectOption = optionAnswer === correctAnswer;
            } else {
                // TF: Use normalized comparison
                isCorrectOption = this.normalizeAnswer(optionAnswer) === this.normalizeAnswer(correctAnswer);
            }

            if (isCorrectOption) {
                option.classList.add('correct');
            } else if (option.classList.contains('selected') && !isCorrect) {
                option.classList.add('incorrect');
            }
        });
    }

    normalizeAnswer(answer) {
        if (!answer && answer !== 0 && answer !== false) return '';

        // Handle boolean values
        if (answer === true || answer === false) {
            return answer ? 'true' : 'false';
        }

        // Convert to string and normalize
        const normalized = String(answer).toLowerCase().trim();

        // Handle common variations - expanded patterns
        if (normalized === 'true' || normalized === 't' || normalized === '1' ||
            normalized === 'yes' || normalized === 'correct' || normalized === 'right' ||
            normalized === 'verdadero' || normalized === 'vrai' || normalized === 'wahr') {
            return 'true';
        }

        if (normalized === 'false' || normalized === 'f' || normalized === '0' ||
            normalized === 'no' || normalized === 'incorrect' || normalized === 'wrong' ||
            normalized === 'falso' || normalized === 'faux' || normalized === 'falsch') {
            return 'false';
        }

        // Check if it contains true/false keywords
        if (normalized.includes('true') || normalized.includes('correct') || normalized.includes('yes')) {
            return 'true';
        }

        if (normalized.includes('false') || normalized.includes('incorrect') || normalized.includes('no')) {
            return 'false';
        }

        // For MCQ, return as-is but normalized
        return normalized;
    }

    formatAnswerForDisplay(answer, questionType = null) {
        // Handle boolean values FIRST (including false)
        if (answer === true || answer === false) {
            console.log('formatAnswerForDisplay: Boolean value detected:', answer);
            return answer ? 'True' : 'False';
        }

        // Handle null/undefined (but not false!)
        if (answer === null || answer === undefined) {
            console.warn('formatAnswerForDisplay: Null/undefined answer received:', answer);
            return questionType === 'TF' ? 'True' : 'Unknown'; // Default only for null/undefined
        }

        const normalized = String(answer).toLowerCase().trim();
        console.log('formatAnswerForDisplay: Processing answer:', answer, 'normalized:', normalized);

        // Format True/False answers nicely - expanded patterns
        if (normalized === 'true' || normalized === 't' || normalized === '1' ||
            normalized === 'yes' || normalized === 'correct' || normalized === 'right' ||
            normalized === 'verdadero' || normalized === 'vrai' || normalized === 'wahr') {
            return 'True';
        }

        if (normalized === 'false' || normalized === 'f' || normalized === '0' ||
            normalized === 'no' || normalized === 'incorrect' || normalized === 'wrong' ||
            normalized === 'falso' || normalized === 'faux' || normalized === 'falsch') {
            return 'False';
        }

        // Check if it contains true/false keywords
        if (normalized.includes('true') || normalized.includes('correct') || normalized.includes('yes')) {
            console.log('formatAnswerForDisplay: Found True keyword in:', normalized);
            return 'True';
        }

        if (normalized.includes('false') || normalized.includes('incorrect') || normalized.includes('no')) {
            console.log('formatAnswerForDisplay: Found False keyword in:', normalized);
            return 'False';
        }

        // For True/False questions, if we can't determine the answer, log error and return as-is
        if (questionType === 'TF') {
            console.error('formatAnswerForDisplay: Could not determine True/False for answer:', answer, 'type:', typeof answer);
            // Don't default to True - return the original value to debug the issue
            return String(answer);
        }

        // For MCQ answers, return the original answer
        return String(answer);
    }

    showQuizFeedback(isCorrect, question, userAnswer) {
        const feedbackElement = document.getElementById('quizFeedback');
        feedbackElement.className = `quiz-feedback ${isCorrect ? 'feedback-correct' : 'feedback-incorrect'}`;

        const formattedCorrectAnswer = this.formatAnswerForDisplay(question.answer, this.selectedQuestionType);
        const formattedUserAnswer = this.formatAnswerForDisplay(userAnswer, this.selectedQuestionType);

        feedbackElement.innerHTML = `
            <div class="feedback-title">
                ${isCorrect ? '✅ Correct!' : '❌ Incorrect'}
            </div>
            <div>
                ${isCorrect
                    ? `You selected: <strong>${formattedUserAnswer}</strong> ✓`
                    : `You selected: <strong>${formattedUserAnswer}</strong><br>The correct answer is: <strong>${formattedCorrectAnswer}</strong>`
                }
            </div>
            ${question.explanation ? `<div style="margin-top: 0.5rem;"><strong>Explanation:</strong> ${question.explanation}</div>` : ''}
        `;

        feedbackElement.classList.remove('hidden');
    }

    nextQuizQuestion() {
        console.log(`Moving to next question. Current index: ${this.quizState.currentQuestionIndex}, Total questions: ${this.currentQuestions.length}`);

        this.quizState.currentQuestionIndex++;

        if (this.quizState.currentQuestionIndex >= this.currentQuestions.length) {
            console.log('Reached end of quiz, finishing...');
            this.finishQuiz();
            return;
        }

        console.log(`Displaying question ${this.quizState.currentQuestionIndex + 1}`);
        this.displayQuizQuestion();
    }

    async finishQuiz() {
        this.quizState.endTime = new Date();

        // Save quiz session to database
        await this.saveQuizSession();

        this.showQuizResults();
    }

    async saveQuizSession() {
        try {
            const duration = Math.round((this.quizState.endTime - this.quizState.startTime) / 1000);
            const scorePercentage = Math.round((this.quizState.score.correct / this.currentQuestions.length) * 100);

            const session = {
                timestamp: new Date().toISOString(),
                question_type: this.selectedQuestionType, // Use snake_case for database compatibility
                score: {
                    correct: this.quizState.score.correct,
                    total: this.currentQuestions.length
                },
                duration: duration,
                answers: this.quizState.answers,
                questions: this.currentQuestions
            };

            const saveResult = await window.electronAPI.saveQuizSession(session);
            if (saveResult.success) {
                console.log('Quiz session saved successfully');

                // Add activity to live feed
                this.addActivityToFeed({
                    type: 'quiz_completed',
                    questionType: this.selectedQuestionType,
                    score: scorePercentage
                });

                // Check for high score (above 90%)
                if (scorePercentage >= 90) {
                    this.addActivityToFeed({
                        type: 'high_score',
                        score: scorePercentage
                    });
                }
            } else {
                console.warn('Failed to save quiz session:', saveResult.error);
            }
        } catch (error) {
            console.error('Error saving quiz session:', error);
        }
    }

    showQuizResults() {
        this.showScreen('resultsScreen');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        const score = this.quizState.score;
        const percentage = Math.round((score.correct / score.total) * 100);
        const duration = Math.round((this.quizState.endTime - this.quizState.startTime) / 1000);

        const resultsDisplay = document.getElementById('resultsDisplay');
        resultsDisplay.innerHTML = `
            <div class="score-summary">
                <div class="score-item">
                    <span class="score-value">${percentage}%</span>
                    <div class="score-label">${isArabic ? 'النتيجة الإجمالية' : 'Overall Score'}</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${score.correct}</span>
                    <div class="score-label">${isArabic ? 'الإجابات الصحيحة' : 'Correct Answers'}</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${score.total - score.correct}</span>
                    <div class="score-label">${isArabic ? 'الإجابات الخاطئة' : 'Incorrect Answers'}</div>
                </div>
                <div class="score-item">
                    <span class="score-value">${duration}s</span>
                    <div class="score-label">${isArabic ? 'الوقت المستغرق' : 'Time Taken'}</div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <h3>${isArabic ? 'الأداء' : 'Performance'}: ${this.getPerformanceLevel(percentage)}</h3>
                <p style="color: #666; margin-top: 0.5rem;">
                    ${this.getPerformanceMessage(percentage)}
                </p>
            </div>
        `;

    }

    getPerformanceLevel(percentage) {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        if (percentage >= 90) return isArabic ? 'ممتاز! 🏆' : 'Excellent! 🏆';
        if (percentage >= 80) return isArabic ? 'جيد جداً! 🌟' : 'Very Good! 🌟';
        if (percentage >= 70) return isArabic ? 'جيد! 👍' : 'Good! 👍';
        if (percentage >= 60) return isArabic ? 'مقبول 📚' : 'Fair 📚';
        return isArabic ? 'يحتاج تحسين 💪' : 'Needs Improvement 💪';
    }

    getPerformanceMessage(percentage) {
        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        if (percentage >= 90) {
            return isArabic
                ? 'أداء متميز! لقد أتقنت هذا الموضوع.'
                : 'Outstanding performance! You have mastered this topic.';
        }
        if (percentage >= 80) {
            return isArabic
                ? 'عمل رائع! لديك فهم قوي للمادة.'
                : 'Great job! You have a strong understanding of the material.';
        }
        if (percentage >= 70) {
            return isArabic
                ? 'أحسنت! فكر في مراجعة المواضيع التي أخطأت فيها.'
                : 'Well done! Consider reviewing the topics you missed.';
        }
        if (percentage >= 60) {
            return isArabic
                ? 'أنت على الطريق الصحيح. المزيد من الممارسة سيساعد في تحسين نتيجتك.'
                : 'You\'re on the right track. More practice will help improve your score.';
        }
        return isArabic
            ? 'استمر في الدراسة! راجع المادة وحاول مرة أخرى لتحسين فهمك.'
            : 'Keep studying! Review the material and try again to improve your understanding.';
    }

    handleFileSelect(file) {
        if (!file) return;

        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const generateBtn = document.getElementById('generateFromFile');

        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        fileInfo.classList.remove('hidden');
        generateBtn.disabled = false;
    }

    handleImageSelect(file) {
        if (!file) return;

        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        const generateBtn = document.getElementById('generateFromImage');

        const reader = new FileReader();
        reader.onload = (e) => {
            previewImg.src = e.target.result;
            imagePreview.classList.remove('hidden');
            generateBtn.disabled = false;
        };
        reader.readAsDataURL(file);
    }

    removeFile() {
        document.getElementById('fileInput').value = '';
        document.getElementById('fileInfo').classList.add('hidden');
        document.getElementById('generateFromFile').disabled = true;
    }

    removeImage() {
        document.getElementById('imageInput').value = '';
        document.getElementById('imagePreview').classList.add('hidden');
        document.getElementById('generateFromImage').disabled = true;
    }

    removeMindMapFile() {
        document.getElementById('mindMapFileInput').value = '';
        document.getElementById('mindMapFileInfo').classList.add('hidden');
        document.getElementById('generateMindMapFromFile').disabled = true;
    }

    removeMindMapImage() {
        document.getElementById('mindMapImageInput').value = '';
        document.getElementById('mindMapImagePreview').classList.add('hidden');
        document.getElementById('generateMindMapFromImage').disabled = true;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Placeholder methods for future implementation

    showAbout() {
        // Show about information
        const aboutContent = `
            <div style="text-align: center; padding: 20px;">
                <h2>MCQ & TF Question Generator</h2>
                <p>Version 1.0.0</p>
                <p>Desktop application for generating multiple choice and true/false questions from documents and images.</p>
                <p>Built with Electron and powered by AI.</p>
            </div>
        `;

        // Create a simple modal or use existing notification system
        this.showNotification('MCQ & TF Generator v1.0.0 - AI-powered question generation', 'info');
    }

    showHelp() {
        // Show help information
        const helpContent = `
            <div>
                <h3>How to use:</h3>
                <ul>
                    <li>Upload a PDF file or image</li>
                    <li>Select question type (MCQ or True/False)</li>
                    <li>Set number of questions per page</li>
                    <li>Click "Start Quiz" to begin</li>
                    <li>View your history and statistics</li>
                </ul>
            </div>
        `;

        this.showNotification('Check the main interface for easy-to-use controls and navigation', 'info');
    }

    reviewAnswers() {
        this.showScreen('questionsScreen');
    }

    showSaveQuizNameDialog(validQuestions) {
        const dialog = document.getElementById('saveQuizNameDialog');
        const customQuizName = document.getElementById('customQuizName');
        const previewQuestionType = document.getElementById('previewQuestionType');
        const previewQuestionCount = document.getElementById('previewQuestionCount');
        const previewSource = document.getElementById('previewSource');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Set up preview information
        previewQuestionType.textContent = this.selectedQuestionType;
        previewQuestionCount.textContent = validQuestions.length;
        previewSource.textContent = this.currentFileName || (isArabic ? 'محتوى نصي' : 'Text Content');

        // Clear previous input and set placeholder
        customQuizName.value = '';
        customQuizName.placeholder = isArabic ? 'أدخل اسم الاختبار...' : 'Enter quiz name...';

        // Store valid questions for later use
        this.pendingQuizQuestions = validQuestions;

        // Show dialog
        dialog.style.display = 'flex';
        customQuizName.focus();

        // Set up event listeners if not already set
        this.setupSaveQuizDialogListeners();
    }

    setupSaveQuizDialogListeners() {
        const dialog = document.getElementById('saveQuizNameDialog');
        const customQuizName = document.getElementById('customQuizName');
        const confirmBtn = document.getElementById('confirmSaveQuiz');
        const cancelBtn = document.getElementById('cancelSaveQuiz');
        const closeBtn = document.getElementById('closeSaveQuizNameDialog');

        // Remove existing listeners to prevent duplicates
        confirmBtn.replaceWith(confirmBtn.cloneNode(true));
        cancelBtn.replaceWith(cancelBtn.cloneNode(true));
        closeBtn.replaceWith(closeBtn.cloneNode(true));

        // Get fresh references after cloning
        const newConfirmBtn = document.getElementById('confirmSaveQuiz');
        const newCancelBtn = document.getElementById('cancelSaveQuiz');
        const newCloseBtn = document.getElementById('closeSaveQuizNameDialog');

        // Confirm save
        newConfirmBtn.addEventListener('click', () => {
            const customName = customQuizName.value.trim();
            this.confirmSaveQuiz(customName);
        });

        // Cancel/Close
        const closeDialog = () => {
            dialog.style.display = 'none';
            this.pendingQuizQuestions = null;
        };

        newCancelBtn.addEventListener('click', closeDialog);
        newCloseBtn.addEventListener('click', closeDialog);

        // Handle Enter key in input
        customQuizName.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const customName = customQuizName.value.trim();
                this.confirmSaveQuiz(customName);
            }
        });

        // Handle Escape key
        dialog.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeDialog();
            }
        });
    }

    async confirmSaveQuiz(customName) {
        try {
            const dialog = document.getElementById('saveQuizNameDialog');
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';

            if (!this.pendingQuizQuestions) {
                this.showNotification(
                    isArabic ? 'خطأ: لا توجد أسئلة للحفظ' : 'Error: No questions to save',
                    'error'
                );
                return;
            }

            // Create a saved quiz object with explicit Gregorian date formatting
            const currentDate = new Date();
            const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
            const day = currentDate.getDate().toString().padStart(2, '0');
            const year = currentDate.getFullYear();
            const formattedDate = `${month}/${day}/${year}`;

            // Use custom name if provided, otherwise use default
            let quizTitle;
            if (customName) {
                quizTitle = customName;
            } else {
                quizTitle = isArabic ?
                    `اختبار محفوظ - ${this.selectedQuestionType} - ${formattedDate}` :
                    `Saved Quiz - ${this.selectedQuestionType} - ${formattedDate}`;
            }

            const savedQuiz = {
                id: Date.now().toString(),
                title: quizTitle,
                questionType: this.selectedQuestionType,
                questions: this.pendingQuizQuestions,
                createdAt: currentDate.toISOString(),
                source: this.currentFileName || (isArabic ? 'محتوى نصي' : 'Text Content')
            };

            console.log('Saving quiz:', savedQuiz);

            // Close dialog first
            dialog.style.display = 'none';
            this.pendingQuizQuestions = null;

            // Save to database
            const result = await window.electronAPI.saveSavedQuiz(savedQuiz);

            console.log('Save result:', result);

            if (result && result.success) {
                const message = isArabic ?
                    'تم حفظ الاختبار بنجاح! يمكنك العثور عليه في صفحة التاريخ.' :
                    'Quiz saved successfully! You can find it in the history page.';
                this.showNotification(message, 'success');

                // If we're currently on the history page, refresh the saved quizzes list
                const currentScreen = document.querySelector('.screen:not([style*="display: none"])');
                if (currentScreen && currentScreen.id === 'historyScreen') {
                    console.log('Refreshing saved quizzes list after save...');
                    await this.loadSavedQuizzes();
                }
            } else {
                const errorMsg = result?.error || 'Unknown error';
                console.error('Save failed:', errorMsg);
                const message = isArabic ?
                    `فشل في حفظ الاختبار: ${errorMsg}` :
                    `Failed to save quiz: ${errorMsg}`;
                this.showNotification(message, 'error');
            }
        } catch (error) {
            console.error('Error confirming save quiz:', error);
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ?
                `حدث خطأ أثناء حفظ الاختبار: ${error.message}` :
                `An error occurred while saving the quiz: ${error.message}`;
            this.showNotification(message, 'error');
        }
    }

    async saveQuizForLater() {
        try {
            if (!this.currentQuestions || this.currentQuestions.length === 0) {
                const currentLanguage = localStorage.getItem('language') || 'en';
                const isArabic = currentLanguage === 'ar';
                const message = isArabic ? 'لا توجد أسئلة اختبار للحفظ' : 'No quiz questions to save';
                this.showNotification(message, 'warning');
                return;
            }

            // Get current language for user-friendly messages
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';

            // Validate questions have required fields
            const validQuestions = this.currentQuestions.filter(q => q.question && q.answer);
            if (validQuestions.length === 0) {
                const message = isArabic ? 'الأسئلة غير صالحة للحفظ' : 'Questions are not valid for saving';
                this.showNotification(message, 'warning');
                return;
            }

            // Show the inline save quiz form
            this.showInlineSaveQuizForm(validQuestions);
        } catch (error) {
            console.error('Error saving quiz for later:', error);
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ?
                `حدث خطأ أثناء حفظ الاختبار: ${error.message}` :
                `An error occurred while saving the quiz: ${error.message}`;
            this.showNotification(message, 'error');
        }
    }

    showInlineSaveQuizForm(validQuestions) {
        const form = document.getElementById('inlineSaveQuizForm');
        const nameInput = document.getElementById('inlineQuizName');
        const previewType = document.getElementById('inlinePreviewType');
        const previewCount = document.getElementById('inlinePreviewCount');
        const previewSource = document.getElementById('inlinePreviewSource');

        // Get current language for translations
        const currentLanguage = localStorage.getItem('language') || 'en';
        const isArabic = currentLanguage === 'ar';

        // Set up preview information
        previewType.textContent = this.selectedQuestionType;
        previewCount.textContent = validQuestions.length;
        previewSource.textContent = this.currentFileName || (isArabic ? 'محتوى نصي' : 'Text Content');

        // Clear previous input and set placeholder
        nameInput.value = '';
        nameInput.placeholder = isArabic ? 'أدخل اسم الاختبار...' : 'Enter quiz name...';

        // Store valid questions for later use
        this.pendingQuizQuestions = validQuestions;

        // Show form with animation
        form.style.display = 'block';
        setTimeout(() => {
            form.classList.add('show');
        }, 10);

        // Focus on input
        setTimeout(() => {
            nameInput.focus();
        }, 400);

        // Set up event listeners
        this.setupInlineSaveFormListeners();
    }

    setupInlineSaveFormListeners() {
        const form = document.getElementById('inlineSaveQuizForm');
        const nameInput = document.getElementById('inlineQuizName');
        const confirmBtn = document.getElementById('confirmInlineSave');
        const cancelBtn = document.getElementById('cancelInlineSave');

        // Remove existing listeners to prevent duplicates
        confirmBtn.replaceWith(confirmBtn.cloneNode(true));
        cancelBtn.replaceWith(cancelBtn.cloneNode(true));

        // Get fresh references after cloning
        const newConfirmBtn = document.getElementById('confirmInlineSave');
        const newCancelBtn = document.getElementById('cancelInlineSave');

        // Confirm save
        newConfirmBtn.addEventListener('click', () => {
            const customName = nameInput.value.trim();
            this.confirmInlineSaveQuiz(customName);
        });

        // Cancel
        const hideForm = () => {
            form.classList.remove('show');
            setTimeout(() => {
                form.style.display = 'none';
                this.pendingQuizQuestions = null;
            }, 400);
        };

        newCancelBtn.addEventListener('click', hideForm);

        // Handle Enter key in input
        nameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const customName = nameInput.value.trim();
                this.confirmInlineSaveQuiz(customName);
            }
        });

        // Handle Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && form.classList.contains('show')) {
                hideForm();
            }
        });
    }

    async confirmInlineSaveQuiz(customName) {
        try {
            const form = document.getElementById('inlineSaveQuizForm');
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';

            if (!this.pendingQuizQuestions) {
                this.showNotification(
                    isArabic ? 'خطأ: لا توجد أسئلة للحفظ' : 'Error: No questions to save',
                    'error'
                );
                return;
            }

            // Generate quiz title
            const currentDate = new Date();
            const defaultTitle = isArabic ?
                `اختبار ${this.selectedQuestionType} - ${currentDate.toLocaleDateString('ar-SA')}` :
                `${this.selectedQuestionType} Quiz - ${currentDate.toLocaleDateString('en-US')}`;

            const quizTitle = customName || defaultTitle;

            const savedQuiz = {
                id: Date.now().toString(),
                title: quizTitle,
                questionType: this.selectedQuestionType,
                questions: this.pendingQuizQuestions,
                createdAt: currentDate.toISOString(),
                source: this.currentFileName || (isArabic ? 'محتوى نصي' : 'Text Content')
            };

            console.log('Saving quiz:', savedQuiz);

            // Hide form first
            form.classList.remove('show');
            setTimeout(() => {
                form.style.display = 'none';
                this.pendingQuizQuestions = null;
            }, 400);

            // Save to database
            const result = await window.electronAPI.saveSavedQuiz(savedQuiz);

            console.log('Save result:', result);

            if (result.success) {
                const successMessage = isArabic ?
                    `تم حفظ الاختبار "${quizTitle}" بنجاح` :
                    `Quiz "${quizTitle}" saved successfully`;
                this.showNotification(successMessage, 'success');
            } else {
                const errorMessage = isArabic ?
                    `فشل في حفظ الاختبار: ${result.error}` :
                    `Failed to save quiz: ${result.error}`;
                this.showNotification(errorMessage, 'error');
            }
        } catch (error) {
            console.error('Error confirming save quiz:', error);
            const currentLanguage = localStorage.getItem('language') || 'en';
            const isArabic = currentLanguage === 'ar';
            const message = isArabic ?
                `حدث خطأ أثناء حفظ الاختبار: ${error.message}` :
                `An error occurred while saving the quiz: ${error.message}`;
            this.showNotification(message, 'error');
        }
    }

    handleExternalFile(filePath) {
        this.showNotification(`File selected: ${filePath}`, 'info');
        // Handle external file selection from menu
    }

    detectUniversalContradictions(question, questionNumber, questionText, explanation, answer) {
        console.log(`🔍 Universal contradiction analysis for question ${questionNumber}`);

        // 1. Exclusivity vs. Plurality Contradictions
        const exclusiveWords = ['solely', 'only', 'exclusively', 'just', 'merely', 'purely', 'simply', 'uniquely'];
        const pluralityWords = ['also', 'additionally', 'furthermore', 'not just', 'not only', 'more than', 'as well as', 'including', 'plus', 'and', 'various', 'multiple', 'several'];

        const hasExclusiveWord = exclusiveWords.some(word => questionText.includes(word));
        const hasPluralityWord = pluralityWords.some(word => explanation.includes(word));

        if (hasExclusiveWord && hasPluralityWord && answer === 'True') {
            console.warn(`⚠️  EXCLUSIVITY CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question claims exclusivity but explanation suggests plurality`);
            console.warn(`   Exclusive words: ${exclusiveWords.filter(w => questionText.includes(w))}`);
            console.warn(`   Plurality words in explanation: ${pluralityWords.filter(w => explanation.includes(w))}`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        // 2. Absolute vs. Conditional Contradictions
        const absoluteWords = ['always', 'never', 'all', 'none', 'every', 'no', 'completely', 'totally', 'entirely', 'absolutely'];
        const conditionalWords = ['sometimes', 'often', 'usually', 'typically', 'generally', 'most', 'some', 'may', 'might', 'can', 'could', 'depends', 'varies'];

        const hasAbsoluteWord = absoluteWords.some(word => questionText.includes(word));
        const hasConditionalWord = conditionalWords.some(word => explanation.includes(word));

        if (hasAbsoluteWord && hasConditionalWord && answer === 'True') {
            console.warn(`⚠️  ABSOLUTE vs CONDITIONAL CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question uses absolute terms but explanation suggests conditions/exceptions`);
            console.warn(`   Absolute words: ${absoluteWords.filter(w => questionText.includes(w))}`);
            console.warn(`   Conditional words in explanation: ${conditionalWords.filter(w => explanation.includes(w))}`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        // 3. Scope Limitation vs. Broad Scope Contradictions
        const limitingWords = ['limited to', 'restricted to', 'confined to', 'specific to', 'dedicated to'];
        const broadScopeWords = ['wide range', 'broad spectrum', 'various types', 'multiple areas', 'diverse', 'comprehensive'];

        const hasLimitingWord = limitingWords.some(phrase => questionText.includes(phrase));
        const hasBroadScopeWord = broadScopeWords.some(phrase => explanation.includes(phrase));

        if (hasLimitingWord && hasBroadScopeWord && answer === 'True') {
            console.warn(`⚠️  SCOPE CONTRADICTION DETECTED in question ${questionNumber}:`);
            console.warn(`   Question suggests limited scope but explanation indicates broad scope`);
            console.warn(`   Auto-correcting to FALSE`);
            question.answer = 'False';
            return;
        }

        console.log(`✅ No universal contradictions detected in question ${questionNumber}`);
    }



    // Navigation Confirmation
    confirmBackToMain(message) {
        if (confirm(message)) {
            this.showScreen('homeScreen');
            // Reset quiz state if in progress
            if (this.quizState) {
                this.quizState = {
                    currentQuestionIndex: 0,
                    answers: [],
                    score: { correct: 0, total: 0 },
                    startTime: null,
                    endTime: null
                };
            }
            this.showNotification('Returned to main menu', 'info');
        }
    }

    // PDF Editor Methods
    setupPdfEditorListeners() {
        // PDF Editor service launcher
        document.getElementById('pdfEditorService').addEventListener('click', () => {
            this.showScreen('pdfEditorScreen');
        });

        // PDF Editor navigation
        this.setupPdfEditorNavigation();

        // Setup individual feature listeners
        this.setupImageToPdfListeners();
        this.setupMergePdfListeners();
        this.setupSplitPdfListeners();
        this.setupTextToPdfListeners();
        this.setupLockPdfListeners();
        this.setupDeletePagesListeners();
    }

    setupPdfEditorNavigation() {
        const navButtons = document.querySelectorAll('#pdfEditorScreen .nav-btn');
        navButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const feature = btn.dataset.feature;
                if (feature) {
                    this.switchPdfFeature(feature);
                }
            });
        });
    }

    switchPdfFeature(feature) {
        // Update active nav button (only within PDF editor screen)
        document.querySelectorAll('#pdfEditorScreen .nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeBtn = document.querySelector(`#pdfEditorScreen [data-feature="${feature}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Update active content (only within PDF editor screen)
        document.querySelectorAll('#pdfEditorScreen .feature-content').forEach(content => {
            content.classList.remove('active');
        });
        const activeContent = document.getElementById(`${feature}Content`);
        if (activeContent) {
            activeContent.classList.add('active');
        }

        // Update active settings
        document.querySelectorAll('.settings-group').forEach(group => {
            group.classList.remove('active');
        });
        const settingsGroup = document.getElementById(`${feature}Settings`);
        if (settingsGroup) {
            settingsGroup.classList.add('active');
        }

        // Update active actions
        document.querySelectorAll('.action-group').forEach(group => {
            group.classList.remove('active');
        });
        document.getElementById(`${feature}Action`).classList.add('active');

        // Update action card title and subtitle
        this.updateActionCardTitle(feature);

        // Store current feature
        this.currentFeature = feature;
    }

    updateActionCardTitle(feature) {
        const titleElement = document.getElementById('actionCardTitle');
        const subtitleElement = document.getElementById('actionCardSubtitle');

        const titles = {
            imageToPdf: { title: 'Convert to PDF', subtitle: 'Generate your PDF document' },
            mergePdf: { title: 'Merge PDFs', subtitle: 'Combine multiple PDF files' },
            splitPdf: { title: 'Split PDF', subtitle: 'Divide PDF into separate files' },
            textToPdf: { title: 'Create PDF', subtitle: 'Generate PDF from text' },
            lockPdf: { title: 'Lock PDF', subtitle: 'Add password protection' },
            deletePages: { title: 'Delete Pages', subtitle: 'Remove selected pages' }
        };

        if (titles[feature]) {
            titleElement.textContent = titles[feature].title;
            subtitleElement.textContent = titles[feature].subtitle;
        }
    }

    setupImageToPdfListeners() {
        // PDF image drop zone
        const pdfImageDropZone = document.getElementById('pdfImageDropZone');
        const pdfImageInput = document.getElementById('pdfImageInput');

        // Check if elements exist and haven't been set up already
        if (!pdfImageDropZone || !pdfImageInput || pdfImageDropZone.dataset.listenersSetup) {
            return;
        }

        // Mark as setup to prevent duplicate listeners
        pdfImageDropZone.dataset.listenersSetup = 'true';

        // Drag and drop functionality
        pdfImageDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            pdfImageDropZone.classList.add('dragover');
        });

        pdfImageDropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            pdfImageDropZone.classList.remove('dragover');
        });

        pdfImageDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            pdfImageDropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            this.handleImageFiles(files);
        });

        pdfImageDropZone.addEventListener('click', () => {
            pdfImageInput.click();
        });

        pdfImageInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleImageFiles(files);
            // Clear the input so the same files can be selected again
            e.target.value = '';
        });

        // Settings listeners
        document.getElementById('pdfQuality').addEventListener('change', (e) => {
            this.pdfSettings.quality = e.target.value;
        });

        document.getElementById('pageSize').addEventListener('change', (e) => {
            this.pdfSettings.pageSize = e.target.value;
        });



        document.getElementById('margin').addEventListener('change', (e) => {
            this.pdfSettings.margin = e.target.value;
        });

        // Action buttons
        document.getElementById('clearAllImages').addEventListener('click', () => {
            this.clearAllImages();
        });

        document.getElementById('convertToPdfBtn').addEventListener('click', () => {
            this.convertImagesToPdf();
        });
    }

    handleImageFiles(files) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'];
        const validFiles = files.filter(file => {
            const extension = '.' + file.name.split('.').pop().toLowerCase();
            return imageExtensions.includes(extension);
        });

        if (validFiles.length === 0) {
            this.showNotification('Please select valid image files', 'warning');
            return;
        }

        let addedCount = 0;
        validFiles.forEach(file => {
            if (!this.selectedImages.find(img => img.name === file.name)) {
                const imageData = {
                    file: file,
                    name: file.name,
                    size: file.size,
                    url: URL.createObjectURL(file)
                };
                this.selectedImages.push(imageData);
                addedCount++;
            }
        });

        this.updateImagesList();
        this.updateConversionInfo();

        if (addedCount > 0) {
            this.showNotification(`Added ${addedCount} image(s). Total: ${this.selectedImages.length}`, 'success');
        } else if (validFiles.length > 0) {
            this.showNotification('All selected images are already in the list', 'info');
        }
    }

    updateImagesList() {
        const container = document.getElementById('selectedImagesContainer');
        const imagesList = document.getElementById('imagesList');

        if (!container || !imagesList) {
            return;
        }

        if (this.selectedImages.length === 0) {
            container.classList.add('hidden');
            return;
        }

        container.classList.remove('hidden');
        imagesList.innerHTML = '';

        this.selectedImages.forEach((imageData, index) => {
            const imageItem = document.createElement('div');
            imageItem.className = 'image-item';
            imageItem.innerHTML = `
                <img src="${imageData.url}" alt="${imageData.name}" class="image-preview">
                <div class="image-info">
                    <div class="image-name">${imageData.name}</div>
                    <div class="image-size">${this.formatFileSize(imageData.size)}</div>
                </div>
                <button class="remove-image" onclick="app.removeImage(${index})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            imagesList.appendChild(imageItem);
        });
    }

    removeImage(index) {
        if (this.selectedImages[index]) {
            URL.revokeObjectURL(this.selectedImages[index].url);
            this.selectedImages.splice(index, 1);
            this.updateImagesList();
            this.updateConversionInfo();
        }
    }

    clearAllImages() {
        this.selectedImages.forEach(imageData => {
            URL.revokeObjectURL(imageData.url);
        });
        this.selectedImages = [];
        this.updateImagesList();
        this.updateConversionInfo();
    }

    updateConversionInfo() {
        const imageCount = document.getElementById('imageCount');
        const estimatedSize = document.getElementById('estimatedSize');
        const convertBtn = document.getElementById('convertToPdfBtn');
        const actionGroup = document.getElementById('imageToPdfAction');

        imageCount.textContent = this.selectedImages.length;

        // Estimate PDF size (rough calculation)
        const totalSize = this.selectedImages.reduce((sum, img) => sum + img.size, 0);
        const estimatedMB = (totalSize / (1024 * 1024)).toFixed(1);
        estimatedSize.textContent = `${estimatedMB} MB`;

        // Enable/disable convert button
        convertBtn.disabled = this.selectedImages.length === 0;

        // Show/hide the action group based on whether images are selected
        if (actionGroup) {
            if (this.selectedImages.length > 0) {
                actionGroup.classList.remove('hidden');
            } else {
                actionGroup.classList.add('hidden');
            }
        }
    }

    async convertImagesToPdf() {
        if (this.selectedImages.length === 0) {
            this.showNotification('Please select images first', 'warning');
            return;
        }

        try {
            this.showNotification('Converting images to PDF...', 'info');

            // Show save dialog
            const result = await window.electronAPI.saveFile({
                title: 'Save PDF',
                defaultPath: `images_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.success && result.filePath) {
                // Generate PDF content
                const pdfContent = await this.generateImagePdfContent();

                // Save PDF through main process
                const saveResult = await window.electronAPI.savePDF(result.filePath, pdfContent);

                if (saveResult.success) {
                    this.showNotification(`PDF saved successfully to ${result.filePath}`, 'success');

                    // Ask if user wants to open the PDF or folder
                    const openResult = await window.electronAPI.showMessageBox({
                        type: 'question',
                        buttons: ['Open PDF', 'Open Folder', 'Close'],
                        defaultId: 0,
                        title: 'PDF Created Successfully',
                        message: 'Images have been converted to PDF successfully. Would you like to open the PDF?'
                    });

                    if (openResult.response === 0) {
                        // Open the PDF file
                        await window.electronAPI.openExternal(result.filePath);
                    } else if (openResult.response === 1) {
                        // Open the folder containing the PDF
                        const outputDir = result.filePath.substring(0, result.filePath.lastIndexOf('\\') || result.filePath.lastIndexOf('/'));
                        await window.electronAPI.openExternal(outputDir);
                    }

                    // Clear selected images after successful conversion
                    this.clearAllImages();
                } else {
                    throw new Error(saveResult.error || 'Failed to save PDF');
                }
            } else if (result.canceled) {
                this.showNotification('Conversion canceled', 'info');
            } else {
                throw new Error('Failed to select save location');
            }
        } catch (error) {
            console.error('PDF conversion error:', error);
            this.showNotification(`Conversion failed: ${error.message}`, 'error');
        }
    }

    async generateImagePdfContent() {
        // Generate HTML content for PDF with images
        let htmlContent = `
            <html>
            <head>
                <title>Image to PDF</title>
                <style>
                    @page {
                        ${this.getMarginStyles()}
                        size: ${this.pdfSettings.pageSize.toUpperCase()};
                    }
                    body {
                        margin: 0;
                        padding: 0;
                        font-family: Arial, sans-serif;
                    }
                    .page {
                        page-break-after: always;
                        page-break-inside: avoid;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 100%;
                        height: 100vh;
                        box-sizing: border-box;
                    }
                    .page:last-child {
                        page-break-after: auto;
                    }
                    .image {
                        max-width: 100%;
                        max-height: 100%;
                        width: auto;
                        height: auto;
                        object-fit: contain;
                        display: block;
                    }
                </style>
            </head>
            <body>
        `;

        // Add each image as a separate page
        for (let i = 0; i < this.selectedImages.length; i++) {
            const imageData = this.selectedImages[i];
            const base64 = await this.fileToBase64(imageData.file);
            const isLastImage = i === this.selectedImages.length - 1;

            htmlContent += `
                <div class="page"${isLastImage ? ' style="page-break-after: avoid;"' : ''}>
                    <img src="${base64}" alt="${imageData.name}" class="image">
                </div>
            `;
        }

        htmlContent += `
            </body>
            </html>
        `;

        return htmlContent;
    }

    getMarginStyles() {
        const margins = {
            none: 'margin: 0;',
            small: 'margin: 10mm;',
            medium: 'margin: 20mm;',
            large: 'margin: 30mm;'
        };
        return margins[this.pdfSettings.margin] || margins.small;
    }

    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    fileToArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsArrayBuffer(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    fileToText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsText(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }
    // Merge PDF Feature
    setupMergePdfListeners() {
        const mergePdfDropZone = document.getElementById('mergePdfDropZone');
        const mergePdfInput = document.getElementById('mergePdfInput');

        if (!mergePdfDropZone || !mergePdfInput) return;

        // Drag and drop functionality
        mergePdfDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            mergePdfDropZone.classList.add('dragover');
        });

        mergePdfDropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            mergePdfDropZone.classList.remove('dragover');
        });

        mergePdfDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            mergePdfDropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            this.handlePdfFiles(files);
        });

        mergePdfDropZone.addEventListener('click', () => {
            mergePdfInput.click();
        });

        mergePdfInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handlePdfFiles(files);
        });

        // Settings listeners
        const mergeQuality = document.getElementById('mergeQuality');
        const maintainBookmarksSettings = document.getElementById('maintainBookmarksSettings');
        const addTableOfContents = document.getElementById('addTableOfContents');
        const addPageNumbers = document.getElementById('addPageNumbers');

        if (mergeQuality) {
            mergeQuality.addEventListener('change', (e) => {
                this.mergePdfSettings.quality = e.target.value;
            });
        }

        if (maintainBookmarksSettings) {
            maintainBookmarksSettings.addEventListener('change', (e) => {
                this.mergePdfSettings.maintainBookmarks = e.target.checked;
            });
        }

        if (addTableOfContents) {
            addTableOfContents.addEventListener('change', (e) => {
                this.mergePdfSettings.addTableOfContents = e.target.checked;
            });
        }

        if (addPageNumbers) {
            addPageNumbers.addEventListener('change', (e) => {
                this.mergePdfSettings.addPageNumbers = e.target.checked;
            });
        }

        // Action buttons
        const clearAllPdfsBtn = document.getElementById('clearAllPdfs');
        const mergePdfsBtn = document.getElementById('mergePdfsBtn');

        if (clearAllPdfsBtn) {
            clearAllPdfsBtn.addEventListener('click', () => {
                this.clearAllPdfs();
            });
        }

        if (mergePdfsBtn) {
            mergePdfsBtn.addEventListener('click', () => {
                this.mergePdfs();
            });
        }
    }

    handlePdfFiles(files) {
        const validFiles = files.filter(file => {
            return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
        });

        if (validFiles.length === 0) {
            this.showNotification('Please select valid PDF files', 'warning');
            return;
        }

        let addedCount = 0;
        validFiles.forEach(file => {
            if (!this.selectedPdfs.find(pdf => pdf.name === file.name)) {
                const pdfData = {
                    file: file,
                    name: file.name,
                    size: file.size
                };
                this.selectedPdfs.push(pdfData);
                addedCount++;
            }
        });

        this.updatePdfsList();
        this.updatePdfCount();

        if (addedCount > 0) {
            this.showNotification(`Added ${addedCount} PDF file(s). Total: ${this.selectedPdfs.length}`, 'success');
        } else if (validFiles.length > 0) {
            this.showNotification('All selected PDFs are already in the list', 'info');
        }
    }

    updatePdfsList() {
        const container = document.getElementById('selectedPdfsContainer');
        const pdfsList = document.getElementById('pdfsList');

        if (!container || !pdfsList) return;

        if (this.selectedPdfs.length === 0) {
            container.classList.add('hidden');
            return;
        }

        container.classList.remove('hidden');
        pdfsList.innerHTML = '';

        this.selectedPdfs.forEach((pdfData, index) => {
            const pdfItem = document.createElement('div');
            pdfItem.className = 'pdf-item';
            pdfItem.draggable = true;
            pdfItem.dataset.index = index;
            pdfItem.innerHTML = `
                <div class="drag-handle">
                    <i class="fas fa-grip-vertical"></i>
                </div>
                <div class="pdf-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="pdf-info">
                    <div class="pdf-name">${pdfData.name}</div>
                    <div class="pdf-size">${this.formatFileSize(pdfData.size)}</div>
                    <div class="pdf-pages" id="pdf-pages-${index}">Loading...</div>
                </div>
                <div class="pdf-actions">
                    <button class="move-up-btn" onclick="app.movePdfUp(${index})" ${index === 0 ? 'disabled' : ''}>
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button class="move-down-btn" onclick="app.movePdfDown(${index})" ${index === this.selectedPdfs.length - 1 ? 'disabled' : ''}>
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button class="remove-pdf" onclick="app.removePdf(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // Add drag and drop event listeners for reordering
            pdfItem.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', index);
                pdfItem.classList.add('dragging');
            });

            pdfItem.addEventListener('dragend', () => {
                pdfItem.classList.remove('dragging');
            });

            pdfItem.addEventListener('dragover', (e) => {
                e.preventDefault();
                const draggingItem = pdfsList.querySelector('.dragging');
                if (draggingItem !== pdfItem) {
                    const rect = pdfItem.getBoundingClientRect();
                    const midY = rect.top + rect.height / 2;
                    if (e.clientY < midY) {
                        pdfsList.insertBefore(draggingItem, pdfItem);
                    } else {
                        pdfsList.insertBefore(draggingItem, pdfItem.nextSibling);
                    }
                }
            });

            pdfItem.addEventListener('drop', (e) => {
                e.preventDefault();
                const draggedIndex = parseInt(e.dataTransfer.getData('text/plain'));
                const targetIndex = parseInt(pdfItem.dataset.index);
                this.reorderPdfs(draggedIndex, targetIndex);
            });

            pdfsList.appendChild(pdfItem);

            // Load PDF page count asynchronously
            this.loadPdfPageCount(pdfData.file, index);
        });
    }

    removePdf(index) {
        if (this.selectedPdfs[index]) {
            this.selectedPdfs.splice(index, 1);
            this.updatePdfsList();
            this.updatePdfCount();

            // Reset the file input to allow selecting files again
            const mergePdfInput = document.getElementById('mergePdfInput');
            if (mergePdfInput) {
                mergePdfInput.value = '';
            }
        }
    }

    clearAllPdfs() {
        this.selectedPdfs = [];
        this.updatePdfsList();
        this.updatePdfCount();

        // Reset the file input to allow selecting the same files again
        const mergePdfInput = document.getElementById('mergePdfInput');
        if (mergePdfInput) {
            mergePdfInput.value = '';
        }
    }

    updatePdfCount() {
        const countElement = document.getElementById('pdfCount');
        const mergeBtn = document.getElementById('mergePdfsBtn');
        const actionGroup = document.getElementById('mergePdfAction');

        if (countElement) {
            countElement.textContent = this.selectedPdfs.length;
        }

        if (mergeBtn) {
            mergeBtn.disabled = this.selectedPdfs.length < 2;
        }

        // Show/hide the action group based on whether PDFs are selected
        if (actionGroup) {
            if (this.selectedPdfs.length > 0) {
                actionGroup.classList.remove('hidden');
            } else {
                actionGroup.classList.add('hidden');
            }
        }
    }

    async mergePdfs() {
        if (this.selectedPdfs.length < 2) {
            this.showNotification('Please select at least 2 PDF files to merge', 'warning');
            return;
        }

        const mergeBtn = document.getElementById('mergePdfsBtn');
        const originalText = mergeBtn.innerHTML;

        try {
            // Disable merge button and show progress
            mergeBtn.disabled = true;
            mergeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Merging...</span>';

            this.showNotification('Preparing PDF files for merge...', 'info');

            // Show save dialog
            const result = await window.electronAPI.saveFile({
                title: 'Save Merged PDF',
                defaultPath: `merged_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.success && result.filePath) {
                // Validate PDF files first
                this.showNotification('Validating PDF files...', 'info');
                const validationResults = await this.validatePdfFiles();

                if (!validationResults.allValid) {
                    throw new Error(`Invalid PDF files detected: ${validationResults.invalidFiles.join(', ')}`);
                }

                // Convert files to array buffers with progress
                this.showNotification('Processing PDF files...', 'info');
                const pdfBuffers = [];
                for (let i = 0; i < this.selectedPdfs.length; i++) {
                    const pdfData = this.selectedPdfs[i];
                    mergeBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> <span>Processing ${i + 1}/${this.selectedPdfs.length}...</span>`;

                    try {
                        const buffer = await this.fileToArrayBuffer(pdfData.file);
                        pdfBuffers.push(buffer);
                    } catch (error) {
                        throw new Error(`Failed to process ${pdfData.name}: ${error.message}`);
                    }
                }

                // Get merge options from settings
                const mergeOptions = {
                    maintainBookmarks: this.mergePdfSettings.maintainBookmarks,
                    quality: this.mergePdfSettings.quality,
                    addTableOfContents: this.mergePdfSettings.addTableOfContents,
                    addPageNumbers: this.mergePdfSettings.addPageNumbers,
                    sourceFileNames: this.selectedPdfs.map(pdf => pdf.name)
                };

                // Merge PDFs through main process
                mergeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Merging PDFs...</span>';
                this.showNotification('Merging PDF files...', 'info');

                const mergeResult = await window.electronAPI.mergePdfs(result.filePath, pdfBuffers, mergeOptions);

                if (mergeResult.success) {
                    this.showNotification(`Successfully merged ${this.selectedPdfs.length} PDFs into ${result.filePath}`, 'success');

                    // Ask if user wants to open the merged PDF or folder
                    const openResult = await window.electronAPI.showMessageBox({
                        type: 'question',
                        buttons: ['Open PDF', 'Open Folder', 'Close'],
                        defaultId: 0,
                        title: 'PDF Merged Successfully',
                        message: 'PDF files have been merged successfully. Would you like to open the merged PDF?'
                    });

                    if (openResult.response === 0) {
                        // Open the merged PDF file
                        await window.electronAPI.openExternal(result.filePath);
                    } else if (openResult.response === 1) {
                        // Open the folder containing the merged PDF
                        const outputDir = result.filePath.substring(0, result.filePath.lastIndexOf('\\') || result.filePath.lastIndexOf('/'));
                        await window.electronAPI.openExternal(outputDir);
                    }

                    // Clear the selected PDFs after successful merge
                    this.clearAllPdfs();
                } else {
                    throw new Error(mergeResult.error || 'Failed to merge PDFs');
                }
            } else if (result.canceled) {
                this.showNotification('Merge operation canceled', 'info');
            } else {
                throw new Error('Failed to select save location');
            }
        } catch (error) {
            console.error('PDF merge error:', error);
            this.showNotification(`Merge failed: ${error.message}`, 'error');
        } finally {
            // Restore merge button
            mergeBtn.disabled = this.selectedPdfs.length < 2;
            mergeBtn.innerHTML = originalText;
        }
    }

    async validatePdfFiles() {
        const results = {
            allValid: true,
            invalidFiles: []
        };

        for (const pdfData of this.selectedPdfs) {
            try {
                const buffer = await this.fileToArrayBuffer(pdfData.file);
                const isValid = await window.electronAPI.validatePdf(buffer);
                if (!isValid) {
                    results.allValid = false;
                    results.invalidFiles.push(pdfData.name);
                }
            } catch (error) {
                results.allValid = false;
                results.invalidFiles.push(pdfData.name);
            }
        }

        return results;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // PDF reordering methods
    movePdfUp(index) {
        if (index > 0) {
            const temp = this.selectedPdfs[index];
            this.selectedPdfs[index] = this.selectedPdfs[index - 1];
            this.selectedPdfs[index - 1] = temp;
            this.updatePdfsList();
        }
    }

    movePdfDown(index) {
        if (index < this.selectedPdfs.length - 1) {
            const temp = this.selectedPdfs[index];
            this.selectedPdfs[index] = this.selectedPdfs[index + 1];
            this.selectedPdfs[index + 1] = temp;
            this.updatePdfsList();
        }
    }

    reorderPdfs(fromIndex, toIndex) {
        if (fromIndex !== toIndex) {
            const item = this.selectedPdfs.splice(fromIndex, 1)[0];
            this.selectedPdfs.splice(toIndex, 0, item);
            this.updatePdfsList();
        }
    }

    async loadPdfPageCount(file, index) {
        try {
            const arrayBuffer = await this.fileToArrayBuffer(file);
            const pageCount = await window.electronAPI.getPdfPageCount(arrayBuffer);
            const pageElement = document.getElementById(`pdf-pages-${index}`);
            if (pageElement) {
                pageElement.textContent = `${pageCount} pages`;
            }
        } catch (error) {
            const pageElement = document.getElementById(`pdf-pages-${index}`);
            if (pageElement) {
                pageElement.textContent = 'Unknown pages';
            }
        }
    }

    // Split PDF Feature
    setupSplitPdfListeners() {
        const splitPdfDropZone = document.getElementById('splitPdfDropZone');
        const splitPdfInput = document.getElementById('splitPdfInput');

        if (!splitPdfDropZone || !splitPdfInput) return;

        // Drag and drop functionality
        splitPdfDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            splitPdfDropZone.classList.add('dragover');
        });

        splitPdfDropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            splitPdfDropZone.classList.remove('dragover');
        });

        splitPdfDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            splitPdfDropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            this.handleSplitPdfFile(files[0]); // Only take the first file
        });

        splitPdfDropZone.addEventListener('click', () => {
            splitPdfInput.click();
        });

        splitPdfInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleSplitPdfFile(e.target.files[0]);
            }
        });

        // Settings listeners
        const splitMethod = document.getElementById('splitMethod');
        const pageRangesInput = document.getElementById('pageRangesInput');
        const everyNPagesInput = document.getElementById('everyNPagesInput');
        const extractPagesInput = document.getElementById('extractPagesInput');

        if (splitMethod) {
            splitMethod.addEventListener('change', (e) => {
                this.splitPdfSettings.method = e.target.value;
                this.updateSplitMethodVisibility();
            });
        }

        if (pageRangesInput) {
            pageRangesInput.addEventListener('input', (e) => {
                this.splitPdfSettings.pageRanges = e.target.value;
            });
        }

        if (everyNPagesInput) {
            everyNPagesInput.addEventListener('input', (e) => {
                this.splitPdfSettings.everyNPages = parseInt(e.target.value) || 1;
            });
        }

        if (extractPagesInput) {
            extractPagesInput.addEventListener('input', (e) => {
                this.splitPdfSettings.extractPages = e.target.value;
            });
        }

        // Action button
        const splitPdfBtn = document.getElementById('splitPdfBtn');
        if (splitPdfBtn) {
            splitPdfBtn.addEventListener('click', () => {
                this.splitPdf();
            });
        }

        // Initialize split method visibility
        this.updateSplitMethodVisibility();
    }

    handleSplitPdfFile(file) {
        if (!file || (!file.type.includes('pdf') && !file.name.toLowerCase().endsWith('.pdf'))) {
            this.showNotification('Please select a valid PDF file', 'warning');
            return;
        }

        this.currentPdfFile = file;
        this.showNotification(`Selected: ${file.name}`, 'success');

        // Display the selected PDF
        this.displaySelectedSplitPdf(file);

        // Enable split button
        const splitPdfBtn = document.getElementById('splitPdfBtn');
        const splitPdfAction = document.getElementById('splitPdfAction');

        if (splitPdfBtn) {
            splitPdfBtn.disabled = false;
        }

        if (splitPdfAction) {
            splitPdfAction.classList.remove('hidden');
        }
    }

    displaySelectedSplitPdf(file) {
        const container = document.getElementById('selectedSplitPdfContainer');
        const display = document.getElementById('splitPdfDisplay');

        if (!container || !display) return;

        container.classList.remove('hidden');

        display.innerHTML = `
            <div class="pdf-item">
                <div class="pdf-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="pdf-info">
                    <div class="pdf-name">${file.name}</div>
                    <div class="pdf-size">${this.formatFileSize(file.size)}</div>
                    <div class="pdf-pages" id="split-pdf-pages">Loading...</div>
                </div>
                <div class="pdf-actions">
                    <button class="remove-pdf" onclick="app.clearSplitPdf()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        // Load PDF page count asynchronously
        this.loadSplitPdfPageCount(file);
    }

    async loadSplitPdfPageCount(file) {
        try {
            const buffer = await this.fileToArrayBuffer(file);
            const pageCount = await window.electronAPI.getPdfPageCount(buffer);
            const pagesElement = document.getElementById('split-pdf-pages');
            if (pagesElement) {
                pagesElement.textContent = `${pageCount} pages`;
            }
        } catch (error) {
            console.error('Error loading PDF page count:', error);
            const pagesElement = document.getElementById('split-pdf-pages');
            if (pagesElement) {
                pagesElement.textContent = 'Unknown pages';
            }
        }
    }

    clearSplitPdf() {
        this.currentPdfFile = null;

        // Hide the display container
        const container = document.getElementById('selectedSplitPdfContainer');
        if (container) {
            container.classList.add('hidden');
        }

        // Reset file input
        const splitPdfInput = document.getElementById('splitPdfInput');
        if (splitPdfInput) {
            splitPdfInput.value = '';
        }

        // Hide action button
        const splitPdfAction = document.getElementById('splitPdfAction');
        if (splitPdfAction) {
            splitPdfAction.classList.add('hidden');
        }
    }

    updateSplitMethodVisibility() {
        const pageRangesGroup = document.getElementById('pageRangesGroup');
        const everyNPagesGroup = document.getElementById('everyNPagesGroup');
        const extractPagesGroup = document.getElementById('extractPagesGroup');

        // Hide all groups first
        [pageRangesGroup, everyNPagesGroup, extractPagesGroup].forEach(group => {
            if (group) group.classList.add('hidden');
        });

        // Show the relevant group based on method
        switch (this.splitPdfSettings.method) {
            case 'pages':
                if (pageRangesGroup) pageRangesGroup.classList.remove('hidden');
                break;
            case 'every':
                if (everyNPagesGroup) everyNPagesGroup.classList.remove('hidden');
                break;
            case 'extract':
                if (extractPagesGroup) extractPagesGroup.classList.remove('hidden');
                break;
        }
    }

    async splitPdf() {
        if (!this.currentPdfFile) {
            this.showNotification('Please select a PDF file first', 'warning');
            return;
        }

        const splitPdfBtn = document.getElementById('splitPdfBtn');
        const originalText = splitPdfBtn.innerHTML;

        try {
            // Disable button and show progress
            splitPdfBtn.disabled = true;
            splitPdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Splitting...</span>';

            this.showNotification('Processing PDF file...', 'info');

            // Convert file to array buffer
            const pdfBuffer = await this.fileToArrayBuffer(this.currentPdfFile);

            // Validate and prepare split options
            let pageRanges = this.splitPdfSettings.pageRanges;
            let everyNPages = this.splitPdfSettings.everyNPages;
            let extractPages = this.splitPdfSettings.extractPages;

            // Validate based on method
            if (this.splitPdfSettings.method === 'pages' && !pageRanges.trim()) {
                this.showNotification('Please enter page ranges (e.g., 1-5, 8-10)', 'warning');
                return;
            }

            if (this.splitPdfSettings.method === 'every' && (!everyNPages || everyNPages < 1)) {
                everyNPages = 1; // Default to 1 page per file
            }

            if (this.splitPdfSettings.method === 'extract' && !extractPages.trim()) {
                this.showNotification('Please enter pages to extract (e.g., 1, 3, 5-7)', 'warning');
                return;
            }

            const splitOptions = {
                method: this.splitPdfSettings.method,
                pageRanges: pageRanges,
                everyNPages: everyNPages,
                extractPages: extractPages
            };

            // Show save dialog to name the base output file
            const baseName = this.currentPdfFile.name.replace('.pdf', '');
            const result = await window.electronAPI.showSaveDialog({
                title: 'Save Split PDF Files',
                buttonLabel: 'Save',
                defaultPath: `${baseName}_split`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.canceled || !result.filePath) {
                this.showNotification('Split operation canceled', 'info');
                return;
            }

            // Pass the full output path to the main process
            const outputPath = result.filePath;

            // Split PDF through main process
            splitPdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Splitting PDF...</span>';
            this.showNotification('Splitting PDF...', 'info');

            const splitResult = await window.electronAPI.splitPdf(pdfBuffer, outputPath, splitOptions);

            if (splitResult.success) {
                this.showNotification(`Successfully split PDF into ${splitResult.fileCount} files`, 'success');

                // Ask if user wants to open the first split file or the folder
                let openResult;
                if (splitResult.fileCount === 1) {
                    // Single file - offer to open the file
                    openResult = await window.electronAPI.showMessageBox({
                        type: 'question',
                        buttons: ['Open File', 'Open Folder', 'Close'],
                        defaultId: 0,
                        title: 'PDF Split Successfully',
                        message: `PDF has been split into 1 file. Would you like to open the file?`
                    });
                } else {
                    // Multiple files - offer to open folder or first file
                    openResult = await window.electronAPI.showMessageBox({
                        type: 'question',
                        buttons: ['Open First File', 'Open Folder', 'Close'],
                        defaultId: 0,
                        title: 'PDF Split Successfully',
                        message: `PDF has been split into ${splitResult.fileCount} files. Would you like to open the first file or the folder?`
                    });
                }

                if (openResult.response === 0) {
                    // Open the first split file
                    if (splitResult.firstFilePath) {
                        await window.electronAPI.openExternal(splitResult.firstFilePath);
                    }
                } else if (openResult.response === 1) {
                    // Open the directory containing the split files
                    const actualOutputDir = splitResult.outputDir;
                    await window.electronAPI.openExternal(actualOutputDir);
                }

                // Reset the form
                this.resetSplitPdfForm();
            } else {
                throw new Error(splitResult.error || 'Failed to split PDF');
            }
        } catch (error) {
            console.error('PDF split error:', error);
            this.showNotification(`Split failed: ${error.message}`, 'error');
        } finally {
            // Restore button
            splitPdfBtn.disabled = !this.currentPdfFile;
            splitPdfBtn.innerHTML = originalText;
        }
    }

    resetSplitPdfForm() {
        // Clear the PDF display and reset everything
        this.clearSplitPdf();

        // Reset settings inputs
        const pageRangesInput = document.getElementById('pageRangesInput');
        const everyNPagesInput = document.getElementById('everyNPagesInput');
        const extractPagesInput = document.getElementById('extractPagesInput');

        if (pageRangesInput) pageRangesInput.value = '';
        if (everyNPagesInput) everyNPagesInput.value = '1';
        if (extractPagesInput) extractPagesInput.value = '';

        // Reset settings state
        this.splitPdfSettings = {
            method: 'pages',
            pageRanges: '',
            everyNPages: 1,
            extractPages: ''
        };
    }

    setupTextToPdfListeners() {
        const textToPdfInput = document.getElementById('textToPdfInput');

        // Settings listeners
        const textPageSize = document.getElementById('textPageSize');
        const textMargins = document.getElementById('textMargins');
        const lineSpacing = document.getElementById('lineSpacing');
        const fontFamily = document.getElementById('fontFamily');
        const fontSize = document.getElementById('fontSize');

        if (textPageSize) {
            textPageSize.addEventListener('change', (e) => {
                this.textToPdfSettings.pageSize = e.target.value;
            });
        }

        if (textMargins) {
            textMargins.addEventListener('change', (e) => {
                this.textToPdfSettings.margins = e.target.value;
            });
        }

        if (lineSpacing) {
            lineSpacing.addEventListener('change', (e) => {
                this.textToPdfSettings.lineSpacing = e.target.value;
            });
        }

        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                this.textToPdfSettings.fontFamily = e.target.value;
            });
        }

        if (fontSize) {
            fontSize.addEventListener('input', (e) => {
                this.textToPdfSettings.fontSize = parseInt(e.target.value) || 12;
            });
        }

        // Text input listener to show/hide action button
        if (textToPdfInput) {
            textToPdfInput.addEventListener('input', () => {
                this.updateTextToPdfAction();
            });
        }

        // Action button
        const textToPdfBtn = document.getElementById('textToPdfBtn');
        if (textToPdfBtn) {
            textToPdfBtn.addEventListener('click', () => {
                this.convertTextToPdf();
            });
        }

        // Initialize action button state
        this.updateTextToPdfAction();
    }

    updateTextToPdfAction() {
        const textToPdfInput = document.getElementById('textToPdfInput');
        const textToPdfAction = document.getElementById('textToPdfAction');
        const textToPdfBtn = document.getElementById('textToPdfBtn');

        const hasText = textToPdfInput && textToPdfInput.value.trim().length > 0;

        if (textToPdfAction) {
            if (hasText) {
                textToPdfAction.classList.remove('hidden');
            } else {
                textToPdfAction.classList.add('hidden');
            }
        }

        if (textToPdfBtn) {
            textToPdfBtn.disabled = !hasText;
        }
    }

    async convertTextToPdf() {
        const textToPdfInput = document.getElementById('textToPdfInput');

        if (!textToPdfInput || !textToPdfInput.value.trim()) {
            this.showNotification('Please enter some text first', 'warning');
            return;
        }

        const textToPdfBtn = document.getElementById('textToPdfBtn');
        const originalText = textToPdfBtn.innerHTML;

        try {
            // Disable button and show progress
            textToPdfBtn.disabled = true;
            textToPdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Creating PDF...</span>';

            this.showNotification('Converting text to PDF...', 'info');

            // Show save dialog
            const result = await window.electronAPI.saveFile({
                title: 'Save Text PDF',
                defaultPath: `text_document_${new Date().toISOString().split('T')[0]}.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.canceled) {
                this.showNotification('PDF creation canceled', 'info');
                return;
            }

            // Prepare text content and settings
            const textContent = textToPdfInput.value;
            const textOptions = {
                pageSize: this.textToPdfSettings.pageSize,
                margins: this.textToPdfSettings.margins,
                lineSpacing: this.textToPdfSettings.lineSpacing,
                fontFamily: this.textToPdfSettings.fontFamily,
                fontSize: this.textToPdfSettings.fontSize
            };

            // Create PDF through main process
            textToPdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Generating PDF...</span>';

            const pdfResult = await window.electronAPI.createTextPdf(result.filePath, textContent, textOptions);

            if (pdfResult.success) {
                this.showNotification(`Successfully created PDF: ${result.filePath}`, 'success');

                // Ask if user wants to open the PDF or folder
                const openResult = await window.electronAPI.showMessageBox({
                    type: 'question',
                    buttons: ['Open PDF', 'Open Folder', 'Close'],
                    defaultId: 0,
                    title: 'PDF Created Successfully',
                    message: 'Text has been converted to PDF successfully. Would you like to open the PDF?'
                });

                if (openResult.response === 0) {
                    // Open the PDF file
                    await window.electronAPI.openExternal(result.filePath);
                } else if (openResult.response === 1) {
                    // Open the folder containing the PDF
                    const outputDir = result.filePath.substring(0, result.filePath.lastIndexOf('\\') || result.filePath.lastIndexOf('/'));
                    await window.electronAPI.openExternal(outputDir);
                }
            } else {
                throw new Error(pdfResult.error || 'Failed to create PDF');
            }
        } catch (error) {
            console.error('Text to PDF error:', error);
            this.showNotification(`PDF creation failed: ${error.message}`, 'error');
        } finally {
            // Restore button
            textToPdfBtn.disabled = false;
            textToPdfBtn.innerHTML = originalText;
        }
    }

    setupLockPdfListeners() {
        const lockPdfDropZone = document.getElementById('lockPdfDropZone');
        const lockPdfInput = document.getElementById('lockPdfInput');

        if (!lockPdfDropZone || !lockPdfInput) return;

        // Drag and drop functionality
        lockPdfDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            lockPdfDropZone.classList.add('dragover');
        });

        lockPdfDropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            lockPdfDropZone.classList.remove('dragover');
        });

        lockPdfDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            lockPdfDropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            this.handleLockPdfFile(files[0]); // Only take the first file
        });

        lockPdfDropZone.addEventListener('click', () => {
            lockPdfInput.click();
        });

        lockPdfInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleLockPdfFile(e.target.files[0]);
            }
        });

        // Settings listeners
        const userPassword = document.getElementById('lockUserPassword');
        const ownerPassword = document.getElementById('lockOwnerPassword');
        const allowPrinting = document.getElementById('allowPrinting');
        const allowCopying = document.getElementById('allowCopying');
        const allowModifying = document.getElementById('allowModifying');

        if (userPassword) {
            userPassword.addEventListener('input', (e) => {
                this.lockPdfSettings.userPassword = e.target.value;
                this.updateLockPdfAction();
            });
        }

        if (ownerPassword) {
            ownerPassword.addEventListener('input', (e) => {
                this.lockPdfSettings.ownerPassword = e.target.value;
            });
        }

        if (allowPrinting) {
            allowPrinting.addEventListener('change', (e) => {
                this.lockPdfSettings.allowPrinting = e.target.checked;
            });
        }

        if (allowCopying) {
            allowCopying.addEventListener('change', (e) => {
                this.lockPdfSettings.allowCopying = e.target.checked;
            });
        }

        if (allowModifying) {
            allowModifying.addEventListener('change', (e) => {
                this.lockPdfSettings.allowModifying = e.target.checked;
            });
        }

        // Action button
        const lockPdfBtn = document.getElementById('lockPdfBtn');
        if (lockPdfBtn) {
            lockPdfBtn.addEventListener('click', () => {
                this.lockPdf();
            });
        }
    }

    handleLockPdfFile(file) {
        if (!file || (!file.type.includes('pdf') && !file.name.toLowerCase().endsWith('.pdf'))) {
            this.showNotification('Please select a valid PDF file', 'warning');
            return;
        }

        this.currentPdfFile = file;
        this.showNotification(`Selected: ${file.name}`, 'success');

        // Display the selected PDF
        this.displaySelectedLockPdf(file);
        this.updateLockPdfAction();
    }

    displaySelectedLockPdf(file) {
        const container = document.getElementById('selectedLockPdfContainer');
        const display = document.getElementById('lockPdfDisplay');

        if (!container || !display) return;

        container.classList.remove('hidden');

        display.innerHTML = `
            <div class="pdf-item">
                <div class="pdf-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="pdf-info">
                    <div class="pdf-name">${file.name}</div>
                    <div class="pdf-size">${this.formatFileSize(file.size)}</div>
                    <div class="pdf-pages" id="lock-pdf-pages">Loading...</div>
                </div>
                <div class="pdf-actions">
                    <button class="remove-pdf" onclick="app.clearLockPdf()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        // Load PDF page count asynchronously
        this.loadLockPdfPageCount(file);
    }

    async loadLockPdfPageCount(file) {
        try {
            const buffer = await this.fileToArrayBuffer(file);
            const pageCount = await window.electronAPI.getPdfPageCount(buffer);
            const pagesElement = document.getElementById('lock-pdf-pages');
            if (pagesElement) {
                pagesElement.textContent = `${pageCount} pages`;
            }
        } catch (error) {
            console.error('Error loading PDF page count:', error);
            const pagesElement = document.getElementById('lock-pdf-pages');
            if (pagesElement) {
                pagesElement.textContent = 'Unknown pages';
            }
        }
    }

    clearLockPdf() {
        this.currentPdfFile = null;

        // Hide the display container
        const container = document.getElementById('selectedLockPdfContainer');
        if (container) {
            container.classList.add('hidden');
        }

        // Reset file input
        const lockPdfInput = document.getElementById('lockPdfInput');
        if (lockPdfInput) {
            lockPdfInput.value = '';
        }

        // Hide action button
        const lockPdfAction = document.getElementById('lockPdfAction');
        if (lockPdfAction) {
            lockPdfAction.classList.add('hidden');
        }

        // Reset password fields
        const userPassword = document.getElementById('lockUserPassword');
        const ownerPassword = document.getElementById('lockOwnerPassword');
        if (userPassword) userPassword.value = '';
        if (ownerPassword) ownerPassword.value = '';

        this.updateLockPdfAction();
    }

    updateLockPdfAction() {
        const lockPdfAction = document.getElementById('lockPdfAction');
        const lockPdfBtn = document.getElementById('lockPdfBtn');

        const hasFile = this.currentPdfFile !== null;
        const hasPassword = this.lockPdfSettings.userPassword.trim().length > 0;

        if (lockPdfAction) {
            if (hasFile) {
                lockPdfAction.classList.remove('hidden');
            } else {
                lockPdfAction.classList.add('hidden');
            }
        }

        if (lockPdfBtn) {
            lockPdfBtn.disabled = !hasFile || !hasPassword;
        }
    }

    async lockPdf() {
        if (!this.currentPdfFile) {
            this.showNotification('Please select a PDF file first', 'warning');
            return;
        }

        if (!this.lockPdfSettings.userPassword.trim()) {
            this.showNotification('Please enter a user password', 'warning');
            return;
        }

        const lockPdfBtn = document.getElementById('lockPdfBtn');
        const originalText = lockPdfBtn.innerHTML;

        try {
            // Disable button and show progress
            lockPdfBtn.disabled = true;
            lockPdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Locking...</span>';

            this.showNotification('Processing PDF file...', 'info');

            // Convert file to array buffer
            const pdfBuffer = await this.fileToArrayBuffer(this.currentPdfFile);

            // Show save dialog
            const result = await window.electronAPI.saveFile({
                title: 'Save Locked PDF',
                defaultPath: `locked_${this.currentPdfFile.name}`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.canceled) {
                this.showNotification('Lock operation canceled', 'info');
                return;
            }

            // Prepare lock options
            const lockOptions = {
                userPassword: this.lockPdfSettings.userPassword,
                ownerPassword: this.lockPdfSettings.ownerPassword || this.lockPdfSettings.userPassword,
                permissions: {
                    printing: this.lockPdfSettings.allowPrinting,
                    copying: this.lockPdfSettings.allowCopying,
                    modifying: this.lockPdfSettings.allowModifying
                }
            };

            // Lock PDF through main process
            lockPdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Locking PDF...</span>';
            this.showNotification('Locking PDF file...', 'info');

            const lockResult = await window.electronAPI.lockPdf(pdfBuffer, result.filePath, lockOptions);

            if (lockResult.success) {
                this.showNotification(`Successfully locked PDF: ${result.filePath}`, 'success');

                // Ask if user wants to open the locked PDF or folder
                const openResult = await window.electronAPI.showMessageBox({
                    type: 'question',
                    buttons: ['Open PDF', 'Open Folder', 'Close'],
                    defaultId: 0,
                    title: 'PDF Locked Successfully',
                    message: 'PDF has been password protected successfully. Would you like to open the locked PDF?'
                });

                if (openResult.response === 0) {
                    // Open the locked PDF file
                    await window.electronAPI.openExternal(result.filePath);
                } else if (openResult.response === 1) {
                    // Open the folder containing the locked PDF
                    const outputDir = result.filePath.substring(0, result.filePath.lastIndexOf('\\') || result.filePath.lastIndexOf('/'));
                    await window.electronAPI.openExternal(outputDir);
                }

                // Reset the form
                this.resetLockPdfForm();
            } else {
                throw new Error(lockResult.error || 'Failed to lock PDF');
            }
        } catch (error) {
            console.error('PDF lock error:', error);
            this.showNotification(`Lock failed: ${error.message}`, 'error');
        } finally {
            // Restore button
            lockPdfBtn.disabled = !this.currentPdfFile || !this.lockPdfSettings.userPassword.trim();
            lockPdfBtn.innerHTML = originalText;
        }
    }

    resetLockPdfForm() {
        // Clear the PDF display and reset everything
        this.clearLockPdf();

        // Reset settings state
        this.lockPdfSettings = {
            userPassword: '',
            ownerPassword: '',
            allowPrinting: true,
            allowCopying: true,
            allowModifying: false
        };
    }

    setupDeletePagesListeners() {
        const deletePagesDropZone = document.getElementById('deletePagesDropZone');
        const deletePagesInput = document.getElementById('deletePagesInput');

        if (!deletePagesDropZone || !deletePagesInput) return;

        // Drag and drop functionality
        deletePagesDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            deletePagesDropZone.classList.add('dragover');
        });

        deletePagesDropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            deletePagesDropZone.classList.remove('dragover');
        });

        deletePagesDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            deletePagesDropZone.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            this.handleDeletePagesPdfFile(files[0]); // Only take the first file
        });

        deletePagesDropZone.addEventListener('click', () => {
            deletePagesInput.click();
        });

        deletePagesInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleDeletePagesPdfFile(e.target.files[0]);
            }
        });

        // Settings listeners
        const pagesToDelete = document.getElementById('deletePagesToDelete');

        if (pagesToDelete) {
            pagesToDelete.addEventListener('input', (e) => {
                this.deletePagesSettings.pagesToDelete = e.target.value;
                this.updateDeletePagesAction();
            });
        }

        // Action button
        const deletePagesBtn = document.getElementById('deletePagesBtn');
        if (deletePagesBtn) {
            deletePagesBtn.addEventListener('click', () => {
                this.deletePages();
            });
        }
    }

    handleDeletePagesPdfFile(file) {
        if (!file || (!file.type.includes('pdf') && !file.name.toLowerCase().endsWith('.pdf'))) {
            this.showNotification('Please select a valid PDF file', 'warning');
            return;
        }

        this.currentPdfFile = file;
        this.showNotification(`Selected: ${file.name}`, 'success');

        // Display the selected PDF
        this.displaySelectedDeletePdf(file);
        this.updateDeletePagesAction();
    }

    displaySelectedDeletePdf(file) {
        const container = document.getElementById('selectedDeletePdfContainer');
        const display = document.getElementById('deletePdfDisplay');

        if (!container || !display) return;

        container.classList.remove('hidden');

        display.innerHTML = `
            <div class="pdf-item">
                <div class="pdf-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="pdf-info">
                    <div class="pdf-name">${file.name}</div>
                    <div class="pdf-size">${this.formatFileSize(file.size)}</div>
                    <div class="pdf-pages" id="delete-pdf-pages">Loading...</div>
                </div>
                <div class="pdf-actions">
                    <button class="remove-pdf" onclick="app.clearDeletePdf()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        // Load PDF page count asynchronously
        this.loadDeletePdfPageCount(file);
    }

    async loadDeletePdfPageCount(file) {
        try {
            const buffer = await this.fileToArrayBuffer(file);
            const pageCount = await window.electronAPI.getPdfPageCount(buffer);
            const pagesElement = document.getElementById('delete-pdf-pages');
            if (pagesElement) {
                pagesElement.textContent = `${pageCount} pages`;
            }
        } catch (error) {
            console.error('Error loading PDF page count:', error);
            const pagesElement = document.getElementById('delete-pdf-pages');
            if (pagesElement) {
                pagesElement.textContent = 'Unknown pages';
            }
        }
    }

    clearDeletePdf() {
        this.currentPdfFile = null;

        // Hide the display container
        const container = document.getElementById('selectedDeletePdfContainer');
        if (container) {
            container.classList.add('hidden');
        }

        // Reset file input
        const deletePagesInput = document.getElementById('deletePagesInput');
        if (deletePagesInput) {
            deletePagesInput.value = '';
        }

        // Hide action button
        const deletePagesAction = document.getElementById('deletePagesAction');
        if (deletePagesAction) {
            deletePagesAction.classList.add('hidden');
        }

        // Reset pages input
        const pagesToDelete = document.getElementById('deletePagesToDelete');
        if (pagesToDelete) {
            pagesToDelete.value = '';
        }

        this.updateDeletePagesAction();
    }

    updateDeletePagesAction() {
        const deletePagesAction = document.getElementById('deletePagesAction');
        const deletePagesBtn = document.getElementById('deletePagesBtn');

        const hasFile = this.currentPdfFile !== null;
        const hasPages = this.deletePagesSettings.pagesToDelete.trim().length > 0;

        if (deletePagesAction) {
            if (hasFile) {
                deletePagesAction.classList.remove('hidden');
            } else {
                deletePagesAction.classList.add('hidden');
            }
        }

        if (deletePagesBtn) {
            deletePagesBtn.disabled = !hasFile || !hasPages;
        }
    }

    async deletePages() {
        if (!this.currentPdfFile) {
            this.showNotification('Please select a PDF file first', 'warning');
            return;
        }

        if (!this.deletePagesSettings.pagesToDelete.trim()) {
            this.showNotification('Please specify pages to delete', 'warning');
            return;
        }

        // Show confirmation dialog
        const confirmResult = await window.electronAPI.showMessageBox({
            type: 'warning',
            buttons: ['Delete Pages', 'Cancel'],
            defaultId: 1,
            title: 'Confirm Page Deletion',
            message: `Are you sure you want to delete the specified pages from "${this.currentPdfFile.name}"?`,
            detail: 'This action cannot be undone. The original file will not be modified - a new file will be created.'
        });

        if (confirmResult.response !== 0) {
            return;
        }

        const deletePagesBtn = document.getElementById('deletePagesBtn');
        const originalText = deletePagesBtn.innerHTML;

        try {
            // Disable button and show progress
            deletePagesBtn.disabled = true;
            deletePagesBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Deleting...</span>';

            this.showNotification('Processing PDF file...', 'info');

            // Convert file to array buffer
            const pdfBuffer = await this.fileToArrayBuffer(this.currentPdfFile);

            // Show save dialog
            const result = await window.electronAPI.saveFile({
                title: 'Save PDF with Deleted Pages',
                defaultPath: `${this.currentPdfFile.name.replace('.pdf', '')}_pages_deleted.pdf`,
                filters: [
                    { name: 'PDF Files', extensions: ['pdf'] }
                ]
            });

            if (result.canceled) {
                this.showNotification('Delete operation canceled', 'info');
                return;
            }

            // Delete pages through main process
            deletePagesBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Deleting Pages...</span>';
            this.showNotification('Deleting specified pages...', 'info');

            const deleteResult = await window.electronAPI.deletePdfPages(pdfBuffer, result.filePath, this.deletePagesSettings.pagesToDelete);

            if (deleteResult.success) {
                this.showNotification(`Successfully deleted ${deleteResult.deletedPages} pages. ${deleteResult.remainingPages} pages remaining.`, 'success');

                // Ask if user wants to open the new PDF or folder
                const openResult = await window.electronAPI.showMessageBox({
                    type: 'question',
                    buttons: ['Open PDF', 'Open Folder', 'Close'],
                    defaultId: 0,
                    title: 'Pages Deleted Successfully',
                    message: `Pages have been deleted successfully. Would you like to open the new PDF?`
                });

                if (openResult.response === 0) {
                    // Open the new PDF file
                    await window.electronAPI.openExternal(result.filePath);
                } else if (openResult.response === 1) {
                    // Open the folder containing the new PDF
                    const outputDir = result.filePath.substring(0, result.filePath.lastIndexOf('\\') || result.filePath.lastIndexOf('/'));
                    await window.electronAPI.openExternal(outputDir);
                }

                // Reset the form
                this.resetDeletePagesForm();
            } else {
                throw new Error(deleteResult.error || 'Failed to delete pages');
            }
        } catch (error) {
            console.error('Delete pages error:', error);
            this.showNotification(`Delete failed: ${error.message}`, 'error');
        } finally {
            // Restore button
            deletePagesBtn.disabled = !this.currentPdfFile || !this.deletePagesSettings.pagesToDelete.trim();
            deletePagesBtn.innerHTML = originalText;
        }
    }

    resetDeletePagesForm() {
        // Clear the PDF display and reset everything
        this.clearDeletePdf();

        // Reset settings state
        this.deletePagesSettings = {
            pagesToDelete: ''
        };
    }
    // Setup Global AI Settings
    setupGlobalAiSettings() {
        const globalAiBtn = document.getElementById('globalAiSettingsBtn');
        const globalAiSidebar = document.getElementById('globalAiSidebar');
        const closeAiSidebar = document.getElementById('closeAiSidebar');

        if (!globalAiBtn || !globalAiSidebar || !closeAiSidebar) {
            console.error('Global AI Settings elements not found!');
            return;
        }

        // Global AI Settings button click
        globalAiBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleGlobalAiSidebar();
        });

        // Close sidebar button click
        closeAiSidebar.addEventListener('click', () => {
            this.closeGlobalAiSidebar();
        });

        // Close sidebar when clicking outside
        document.addEventListener('click', (e) => {
            if (this.isGlobalAiSidebarOpen &&
                !globalAiSidebar.contains(e.target) &&
                !globalAiBtn.contains(e.target)) {
                this.closeGlobalAiSidebar();
            }
        });

        // Setup global AI settings functionality
        this.setupGlobalAiSettingsHandlers();
    }

    toggleGlobalAiSidebar() {
        const sidebar = document.getElementById('globalAiSidebar');

        if (!sidebar) {
            console.error('Sidebar not found!');
            return;
        }

        this.isGlobalAiSidebarOpen = !this.isGlobalAiSidebarOpen;

        if (this.isGlobalAiSidebarOpen) {
            sidebar.classList.add('active');
            this.loadGlobalAiSettings();
        } else {
            sidebar.classList.remove('active');
        }
    }

    closeGlobalAiSidebar() {
        const sidebar = document.getElementById('globalAiSidebar');
        this.isGlobalAiSidebarOpen = false;
        sidebar.classList.remove('active');
    }

    async loadGlobalAiSettings() {
        // Load settings from backend instead of syncing with removed MCQ/TF sidebar
        const globalFileQuestionsCount = document.getElementById('globalFileQuestionsCount');
        const globalImageQuestionsCount = document.getElementById('globalImageQuestionsCount');
        const globalModelSelect = document.getElementById('globalModelSelect');

        try {
            // Load saved settings from backend
            const result = await window.electronAPI.getSettings();
            if (result.success && result.settings) {
                if (globalFileQuestionsCount) {
                    globalFileQuestionsCount.value = result.settings.questionsPerPage || 2;
                }
                if (globalImageQuestionsCount) {
                    globalImageQuestionsCount.value = result.settings.imageQuestionsCount || 5;
                }
            }
        } catch (error) {
            console.error('Error loading global AI settings:', error);
            // Set default values if loading fails
            if (globalFileQuestionsCount) globalFileQuestionsCount.value = 2;
            if (globalImageQuestionsCount) globalImageQuestionsCount.value = 5;
        }

        // Load available models
        this.loadAvailableModelsGlobal();

        // Load saved model preference
        this.loadGlobalModelPreference();
    }

    setupGlobalAiSettingsHandlers() {
        // Question count settings
        const globalFileQuestionsCount = document.getElementById('globalFileQuestionsCount');
        const globalImageQuestionsCount = document.getElementById('globalImageQuestionsCount');
        const globalModelSelect = document.getElementById('globalModelSelect');

        // Save settings directly to backend instead of syncing with removed elements
        if (globalFileQuestionsCount) {
            globalFileQuestionsCount.addEventListener('change', (e) => {
                this.saveQuestionCountSetting('questionsPerPage', parseInt(e.target.value));
            });
        }

        if (globalImageQuestionsCount) {
            globalImageQuestionsCount.addEventListener('change', (e) => {
                this.saveQuestionCountSetting('imageQuestionsCount', parseInt(e.target.value));
            });
        }

        if (globalModelSelect) {
            globalModelSelect.addEventListener('change', (e) => {
                this.saveModelPreference(e.target.value);
                this.updateGlobalModelStatus();
            });
        }

        // Model management buttons
        document.getElementById('globalAddModelBtn').addEventListener('click', () => {
            this.showAddModelDialog();
        });

        document.getElementById('globalRemoveModelBtn').addEventListener('click', () => {
            this.showRemoveModelDialog();
        });

        document.getElementById('globalTestModelsBtn').addEventListener('click', () => {
            this.showTestModelsDialog();
        });

        document.getElementById('globalManageApiKeyBtn').addEventListener('click', () => {
            this.showApiKeyDialog();
        });
    }

    async loadAvailableModelsGlobal() {
        try {
            const result = await window.electronAPI.getAllModels();
            const globalModelSelect = document.getElementById('globalModelSelect');

            if (result.success && result.models) {
                // Clear existing options except auto
                const autoOption = globalModelSelect.querySelector('option[value="auto"]');
                globalModelSelect.innerHTML = '';
                globalModelSelect.appendChild(autoOption);

                // Add available models
                result.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name || model.id;
                    globalModelSelect.appendChild(option);
                });

                console.log('Loaded', result.models.length, 'models into global settings');
                this.updateGlobalModelStatus();
            } else {
                console.error('Failed to get models:', result);
            }
        } catch (error) {
            console.error('Error loading models for global settings:', error);
        }
    }

    updateGlobalModelStatus() {
        const globalModelSelect = document.getElementById('globalModelSelect');
        const globalModelStatus = document.getElementById('globalModelStatus');

        if (!globalModelSelect || !globalModelStatus) return;

        const selectedModel = globalModelSelect.value;
        const statusIndicator = globalModelStatus.querySelector('.status-indicator');
        const statusText = globalModelStatus.querySelector('.status-text');
        const statusDescription = globalModelStatus.querySelector('.status-description');

        if (selectedModel === 'auto') {
            statusIndicator.className = 'status-indicator status-auto';
            statusText.textContent = 'Auto Selection';
            statusDescription.textContent = 'Best available model will be selected automatically';
        } else {
            statusIndicator.className = 'status-indicator status-checking';
            statusText.textContent = 'Checking...';
            statusDescription.textContent = 'Verifying model availability';

            // Check model status
            this.checkGlobalModelStatus(selectedModel);
        }
    }

    async checkGlobalModelStatus(modelName) {
        try {
            const isAvailable = await window.electronAPI.checkModelAvailability(modelName);
            const globalModelStatus = document.getElementById('globalModelStatus');
            const statusIndicator = globalModelStatus.querySelector('.status-indicator');
            const statusText = globalModelStatus.querySelector('.status-text');
            const statusDescription = globalModelStatus.querySelector('.status-description');

            if (isAvailable) {
                statusIndicator.className = 'status-indicator status-available';
                statusText.textContent = 'Available';
                statusDescription.textContent = `${modelName} is ready to use`;
            } else {
                statusIndicator.className = 'status-indicator status-unavailable';
                statusText.textContent = 'Unavailable';
                statusDescription.textContent = `${modelName} is not accessible`;
            }
        } catch (error) {
            console.error('Error checking model status:', error);
            const globalModelStatus = document.getElementById('globalModelStatus');
            const statusIndicator = globalModelStatus.querySelector('.status-indicator');
            const statusText = globalModelStatus.querySelector('.status-text');
            const statusDescription = globalModelStatus.querySelector('.status-description');

            statusIndicator.className = 'status-indicator status-error';
            statusText.textContent = 'Error';
            statusDescription.textContent = 'Unable to check model status';
        }
    }






}


// Initialize the application when DOM is loaded
console.log('🚀 Setting up DOMContentLoaded listener...');
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOMContentLoaded event fired - initializing app...');
    window.app = new QuestionGeneratorApp();
    console.log('🚀 App initialized successfully!');
});
