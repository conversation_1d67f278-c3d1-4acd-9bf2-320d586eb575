const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

/**
 * Extracts text from a document using Python extraction service
 * @param {string} filePath - Path to the document file
 * @returns {Promise<{text: string, pageCount: number, questionCount: number}>} - Extracted text and metadata
 */
async function extractWithPython(filePath) {
    return new Promise((resolve, reject) => {
        try {
            // Validate file exists
            if (!fs.existsSync(filePath)) {
                return reject(new Error(`File not found: ${filePath}`));
            }

            // Path to the Python extraction script
            const pythonScriptPath = path.join(__dirname, '../../simple_extraction_service_fixed.py');
            
            // Check if Python script exists
            if (!fs.existsSync(pythonScriptPath)) {
                return reject(new Error(`Python extraction script not found: ${pythonScriptPath}`));
            }

            // Spawn Python process
            const pythonProcess = spawn('python', [pythonScriptPath, filePath], {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: path.dirname(pythonScriptPath)
            });

            let stdout = '';
            let stderr = '';

            // Collect stdout data
            pythonProcess.stdout.on('data', (data) => {
                stdout += data.toString();
            });

            // Collect stderr data
            pythonProcess.stderr.on('data', (data) => {
                stderr += data.toString();
            });

            // Handle process completion
            pythonProcess.on('close', (code) => {
                if (code !== 0) {
                    console.error('Python extraction failed:', stderr);
                    return reject(new Error(`Python extraction failed with code ${code}: ${stderr}`));
                }

                try {
                    // Extract JSON from the output (it might have other text before/after)
                    const lines = stdout.trim().split('\n');
                    let jsonLine = '';

                    // Find the line that looks like JSON (starts with { and ends with })
                    for (const line of lines) {
                        const trimmedLine = line.trim();
                        if (trimmedLine.startsWith('{') && trimmedLine.endsWith('}')) {
                            jsonLine = trimmedLine;
                            break;
                        }
                    }

                    if (!jsonLine) {
                        return reject(new Error('No JSON output found in Python script response'));
                    }

                    // Parse the JSON output from Python script
                    const result = JSON.parse(jsonLine);

                    // Validate the result structure
                    if (!result || typeof result !== 'object') {
                        return reject(new Error('Invalid JSON response from Python script'));
                    }

                    if (!result.text || typeof result.text !== 'string') {
                        return reject(new Error('No text extracted from document'));
                    }

                    // Return the result with default values for missing fields
                    resolve({
                        text: result.text,
                        pageCount: result.pageCount || result.page_count || 1,
                        questionCount: result.questionCount || result.question_count || 15,
                        metadata: result.metadata || {}
                    });

                } catch (parseError) {
                    console.error('Failed to parse Python output:', stdout);
                    reject(new Error(`Failed to parse extraction result: ${parseError.message}`));
                }
            });

            // Handle process errors
            pythonProcess.on('error', (error) => {
                reject(new Error(`Failed to start Python process: ${error.message}`));
            });

            // Set a timeout for the extraction process (10 minutes)
            const timeout = setTimeout(() => {
                pythonProcess.kill('SIGTERM');
                reject(new Error('Python extraction timed out after 10 minutes'));
            }, 10 * 60 * 1000);

            // Clear timeout when process completes
            pythonProcess.on('close', () => {
                clearTimeout(timeout);
            });

        } catch (error) {
            reject(new Error(`Python extraction setup failed: ${error.message}`));
        }
    });
}

module.exports = {
    extractWithPython
};
