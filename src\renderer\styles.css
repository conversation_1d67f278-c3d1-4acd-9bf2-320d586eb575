/* CSS Variables - Dark Theme Only */
:root {
    /* Dark Theme Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --bg-accent: #2a2a3a;
    --bg-hover: #3a3a4a;

    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --text-inverse: #1a1a1a;

    --border-color: #404040;
    --border-hover: #667eea;
    --border-focus: #667eea;

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    --shadow-light: 0 2px 10px rgba(0,0,0,0.3);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.3);
    --shadow-heavy: 0 8px 25px rgba(102, 126, 234, 0.25);

    --success-color: #4caf50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196f3;
    --hover-transform: translateY(-1px);

    --success-bg: #1b4332;
    --error-bg: #5c1a1a;
    --warning-bg: #5c3317;
    --info-bg: #1a365d;

    --card-bg: #2d2d2d;
    --primary-color: #667eea;
    --primary-hover: #5a67d8;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}



body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
    transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

/* Header Styles */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 100;
    position: relative;
    flex-shrink: 0; /* Prevent header from shrinking */
}

/* Hide header on home screen */
.screen.active#homeScreen ~ .app-header,
#homeScreen.active ~ .app-header {
    display: none;
}

/* Show header only when not on home screen */
body:not(.home-active) .app-header {
    display: block;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 1.5rem;
}

.logo h1 {
    font-size: 1.2rem;
    font-weight: 300;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

/* Language Toggle Styles */
#languageToggle {
    min-width: 60px;
    padding: 8px 12px;
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

#languageToggle .language-text {
    font-family: 'Arial', sans-serif;
    font-size: 0.85rem;
    font-weight: 600;
}

#languageToggle:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
}

/* Arabic Language Support - Layout Preserved */
[dir="rtl"] {
    /* Only improve font rendering for Arabic, keep layout the same */
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* Arabic font improvements - no layout changes */
[dir="rtl"] * {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

[dir="rtl"] .language-text {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-icon {
    padding: 0.5rem;
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Main Content */
.main-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    z-index: 1;
    margin-top: 0; /* Ensure no negative margin */
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    z-index: 1;
    box-sizing: border-box;
}

.screen.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Welcome Screen */
#welcomeScreen {
    overflow: hidden;
}

.welcome-layout {
    display: flex;
    height: 100vh;
    overflow: hidden;
    max-height: 100vh;
}

.welcome-main-content {
    flex: 1;
    padding: 1rem;
    overflow: hidden;
    text-align: center;
    margin-right: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

.welcome-sidebar {
    width: 500px;
    background: var(--card-bg);
    border-left: 1px solid var(--border-color);
    padding: 0.75rem;
    overflow-y: hidden;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    height: 100vh;
    position: fixed;
    right: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}



/* Sidebar Cards */
.sidebar-card {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    margin-bottom: 0.75rem;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow:
        0 2px 12px rgba(0, 0, 0, 0.08),
        0 1px 4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    flex-shrink: 0;
}

.sidebar-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
}

.sidebar-card:hover {
    transform: translateY(-3px);
    box-shadow:
        0 8px 30px rgba(0, 0, 0, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.12);
}

.sidebar-card .card-header {
    padding: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
}

.sidebar-card .header-icon {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a67d8 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.sidebar-card .header-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: inherit;
}

.sidebar-card .card-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.sidebar-card .card-subtitle {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0.2rem 0 0 0;
    opacity: 0.9;
}

.sidebar-card .card-body {
    padding: 0 0.75rem 0.75rem 0.75rem;
    box-sizing: border-box;
}

/* Sidebar Settings Grid */
.settings-grid-sidebar {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.settings-grid-sidebar .setting-item {
    background: var(--bg-secondary);
    border: none;
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    transition: all 0.3s ease;
    min-height: 60px;
}

.settings-grid-sidebar .setting-item:hover {
    background: var(--card-bg);
    transform: translateY(-1px);
}

.settings-grid-sidebar .setting-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.settings-grid-sidebar .setting-icon {
    width: 24px;
    height: 24px;
    background: var(--primary-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.settings-grid-sidebar .setting-text {
    flex: 1;
}

.settings-grid-sidebar .setting-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.2rem 0;
    display: block;
}

.settings-grid-sidebar .setting-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.3;
}

.settings-grid-sidebar .setting-control {
    flex-shrink: 0;
}

.settings-grid-sidebar .number-input {
    width: 60px;
    padding: 0.5rem 0.6rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.9rem;
    color: var(--text-primary);
    text-align: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.settings-grid-sidebar .number-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.settings-grid-sidebar .number-input::-webkit-outer-spin-button,
.settings-grid-sidebar .number-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.settings-grid-sidebar .number-input[type=number] {
    -moz-appearance: textfield;
}

/* Sidebar Management Grid */
.management-grid-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    box-sizing: border-box;
    padding: 0.25rem;
    flex: 1;
    min-height: 0;
}

.management-grid-sidebar .management-section {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 0.75rem;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    backdrop-filter: blur(10px);
    flex: 1;
    min-height: 0;
}

.management-grid-sidebar .management-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 16px 16px 0 0;
}

.management-grid-sidebar .section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.management-grid-sidebar .section-title i {
    color: var(--primary-color);
    font-size: 1rem;
    width: 18px;
    text-align: center;
}

.action-buttons-grid-sidebar {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    width: 100%;
    box-sizing: border-box;
    padding: 0.25rem;
    margin: 0;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Sidebar Buttons */
.sidebar-btn {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    padding: 0.7rem;
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    min-height: 55px;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
}

.sidebar-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.sidebar-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 4px 10px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.sidebar-btn.btn-add {
    border-color: rgba(16, 185, 129, 0.3);
    background: linear-gradient(145deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
}

.sidebar-btn.btn-add:hover {
    background: linear-gradient(145deg, rgba(16, 185, 129, 0.15) 0%, rgba(16, 185, 129, 0.08) 100%);
    border-color: rgba(16, 185, 129, 0.5);
    box-shadow:
        0 8px 25px rgba(16, 185, 129, 0.2),
        0 4px 10px rgba(16, 185, 129, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sidebar-btn.btn-remove {
    border-color: rgba(239, 68, 68, 0.3);
    background: linear-gradient(145deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%);
}

.sidebar-btn.btn-remove:hover {
    background: linear-gradient(145deg, rgba(239, 68, 68, 0.15) 0%, rgba(239, 68, 68, 0.08) 100%);
    border-color: rgba(239, 68, 68, 0.5);
    box-shadow:
        0 8px 25px rgba(239, 68, 68, 0.2),
        0 4px 10px rgba(239, 68, 68, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sidebar-btn.btn-test {
    border-color: rgba(245, 158, 11, 0.3);
    background: linear-gradient(145deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
}

.sidebar-btn.btn-test:hover {
    background: linear-gradient(145deg, rgba(245, 158, 11, 0.15) 0%, rgba(245, 158, 11, 0.08) 100%);
    border-color: rgba(245, 158, 11, 0.5);
    box-shadow:
        0 8px 25px rgba(245, 158, 11, 0.2),
        0 4px 10px rgba(245, 158, 11, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sidebar-btn.btn-api {
    border-color: rgba(139, 92, 246, 0.3);
    background: linear-gradient(145deg, rgba(139, 92, 246, 0.05) 0%, rgba(139, 92, 246, 0.02) 100%);
}

.sidebar-btn.btn-api:hover {
    background: linear-gradient(145deg, rgba(139, 92, 246, 0.15) 0%, rgba(139, 92, 246, 0.08) 100%);
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow:
        0 8px 25px rgba(139, 92, 246, 0.2),
        0 4px 10px rgba(139, 92, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sidebar-btn .btn-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.sidebar-btn .btn-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: inherit;
}

.sidebar-btn.btn-add .btn-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.sidebar-btn.btn-remove .btn-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.sidebar-btn.btn-test .btn-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.sidebar-btn.btn-api .btn-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.sidebar-btn .btn-content {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    text-align: left;
    gap: 0.25rem;
}

.sidebar-btn .btn-title {
    display: block;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.sidebar-btn .btn-subtitle {
    display: block;
    font-size: 0.7rem;
    color: var(--text-secondary);
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    opacity: 0.8;
}



/* Responsive Design for Sidebar */
@media (max-width: 1200px) {
    .welcome-sidebar {
        width: 450px;
    }

    .welcome-main-content {
        margin-right: 450px;
    }

    .action-buttons-grid-sidebar {
        gap: 0.6rem;
        padding: 0.4rem;
    }

    .sidebar-btn {
        min-height: 65px;
        padding: 0.8rem;
    }
}

@media (max-width: 1024px) {
    .welcome-layout {
        flex-direction: column;
    }

    .welcome-main-content {
        margin-right: 0;
        padding: 1.5rem;
        height: auto;
    }

    .welcome-sidebar {
        width: 100%;
        height: auto;
        position: relative;
        right: auto;
        top: auto;
        border-left: none;
        border-top: 1px solid var(--border-color);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        max-height: 400px;
    }
}

@media (max-width: 768px) {
    .welcome-main-content {
        padding: 1rem;
    }

    .welcome-sidebar {
        padding: 1rem;
        max-height: 350px;
    }

    .sidebar-card .card-header {
        padding: 0.75rem 1rem;
    }

    .sidebar-card .card-body {
        padding: 1rem;
    }

    .sidebar-card .card-title {
        font-size: 0.9rem;
    }

    .sidebar-card .card-subtitle {
        font-size: 0.75rem;
    }

    .action-buttons-grid-sidebar {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .sidebar-btn {
        min-height: 65px;
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .sidebar-btn .btn-icon {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .sidebar-btn .btn-title {
        font-size: 0.8rem;
    }

    .sidebar-btn .btn-subtitle {
        font-size: 0.65rem;
    }

    .management-grid-sidebar .management-section {
        padding: 1rem;
    }

    .management-grid-sidebar .section-title {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
}

/* Sidebar Model Components */
.sidebar-card .model-selector-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.sidebar-card .selector-wrapper {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.sidebar-card .selector-label {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.sidebar-card .selector-label i {
    color: var(--primary-color);
}

.sidebar-card .custom-select-wrapper {
    position: relative;
}

.sidebar-card .modern-select {
    width: 100%;
    padding: 0.5rem 2rem 0.5rem 0.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.8rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.sidebar-card .modern-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.sidebar-card .select-arrow {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
    font-size: 0.6rem;
}

.sidebar-card .model-status-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

.sidebar-card .status-indicator-wrapper {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    margin-bottom: 0.3rem;
}

.sidebar-card .status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    flex-shrink: 0;
}

.sidebar-card .status-indicator.status-unknown {
    background: #6b7280;
}

.sidebar-card .status-indicator.status-available {
    background: #10b981;
}

.sidebar-card .status-indicator.status-unavailable {
    background: #ef4444;
}

.sidebar-card .status-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

.sidebar-card .status-details {
    margin-top: 0.3rem;
}

.sidebar-card .status-description {
    font-size: 0.7rem;
    color: var(--text-secondary);
    line-height: 1.3;
}

/* Home Screen Styles */
.home-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.home-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    backdrop-filter: blur(10px);
}

.app-branding {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.app-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a67d8 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.app-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.app-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0.25rem 0 0 0;
    opacity: 0.9;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.home-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Home Language Toggle */
#homeLanguageToggle {
    min-width: 60px;
    padding: 8px 12px;
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

#homeLanguageToggle .language-text {
    font-family: 'Arial', sans-serif;
    font-size: 0.85rem;
    font-weight: 600;
}

#homeLanguageToggle:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    flex: 1;
}

.service-card {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    border-radius: 16px 16px 0 0;
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.15);
}

.service-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    position: relative;
    overflow: hidden;
}

.quiz-service .service-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.pdf-service .service-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
}

.doc-service .service-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.text-service .service-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);
}

.study-service .service-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.ai-service .service-icon {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    box-shadow: 0 4px 16px rgba(6, 182, 212, 0.3);
}

.service-content {
    flex: 1;
}

.service-title {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.service-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0 0 1rem 0;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature-tag {
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.service-action {
    margin-top: auto;
}

.service-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a67d8 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.service-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.service-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    cursor: not-allowed;
}

/* PDF Editor Styles */
#pdfEditorScreen {
    overflow: hidden;
}

.pdf-editor-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 1.5rem;
    min-height: 100vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: hidden;
}

.pdf-editor-container .screen-header {
    flex-shrink: 0;
    margin-bottom: 0;
    padding: 0.75rem 0;
    text-align: center;
}

.pdf-editor-container .screen-header h2 {
    font-size: 1.8rem;
    margin: 0 0 0.5rem 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.screen-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

/* PDF Editor Navigation */
.pdf-editor-nav {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    flex-shrink: 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.pdf-editor-nav::-webkit-scrollbar {
    display: none;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: fit-content;
}

.nav-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.nav-btn.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.nav-btn i {
    font-size: 1rem;
}

/* PDF Editor Content */
.pdf-editor-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 1.5rem;
    flex: 1;
    min-height: 0;
    overflow: visible;
    padding: 0.5rem 0;
}

.feature-content {
    display: none;
    min-height: 0;
    overflow: visible;
    padding: 0.5rem 0;
}

.feature-content.active {
    display: block;
}

.feature-layout {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: hidden;
}

.upload-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 0;
    overflow: hidden;
    height: 100%;
}

/* Modern Upload Zone */
.upload-zone {
    border: 2px dashed rgba(102, 126, 234, 0.4);
    border-radius: 24px;
    padding: 3.5rem 2rem;
    text-align: center;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.08) 0%,
        rgba(102, 126, 234, 0.03) 50%,
        rgba(139, 92, 246, 0.05) 100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    height: 320px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1.2rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.upload-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(102, 126, 234, 0.05) 100%);
    border-radius: 24px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-zone:hover {
    border-color: rgba(102, 126, 234, 0.7);
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.12) 0%,
        rgba(102, 126, 234, 0.06) 50%,
        rgba(139, 92, 246, 0.08) 100%);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.upload-zone:hover::before {
    opacity: 1;
}

.upload-zone.dragover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.15) 0%,
        rgba(102, 126, 234, 0.08) 50%,
        rgba(139, 92, 246, 0.1) 100%);
    transform: scale(1.01);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
}

.upload-icon {
    width: 64px;
    height: 64px;
    background: transparent;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    transition: all 0.3s ease;
    margin: 0;
    flex-shrink: 0;
}

.upload-zone:hover .upload-icon {
    transform: scale(1.05);
    color: #667eea;
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
}

.upload-zone h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.02em;
}

.upload-zone p {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
    max-width: 300px;
}

.supported-formats {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
    margin-top: 0.5rem;
}

.supported-formats span {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.format-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.format-tag {
    padding: 0.4rem 0.8rem;
    background: rgba(102, 126, 234, 0.15);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.format-tag:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateY(-1px);
}

.selected-images {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    flex: 1;
    min-height: 0;
    max-height: 300px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.section-header h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.images-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
    max-height: 220px;
    padding-right: 0.5rem;
}

.image-item {
    background: var(--bg-secondary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.4rem;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    height: fit-content;
}

.image-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.image-preview {
    width: 100%;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 0.2rem;
}
.image-info {
    margin-bottom: 0.3rem;
}

.image-size {
    font-size: 0.6rem;
    color: var(--text-muted);
    font-style: italic;
}

.image-name {
    font-size: 0.65rem;
    color: var(--text-secondary);
    word-break: break-all;
    margin-bottom: 0.1rem;
    line-height: 1.1;
    max-height: 2.2em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.remove-image {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 20px;
    height: 20px;
    background: rgba(239, 68, 68, 0.9);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 0.7rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-image:hover {
    background: #ef4444;
    transform: scale(1.1);
}

/* PDF Feature Specific Styles */

/* Text Editor for Text to PDF */
.text-input-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: 100%;
    overflow: hidden;
}

.text-editor {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.editor-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1rem;
}

.editor-tools {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.font-select, .font-size {
    padding: 0.25rem 0.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.85rem;
}

.font-size {
    width: 60px;
}

#textToPdfInput {
    flex: 1;
    padding: 1rem;
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: none;
    outline: none;
}

#textToPdfInput::placeholder {
    color: var(--text-muted);
}

/* PDF List Styles */
.selected-pdfs, .selected-images {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.pdfs-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

.pdf-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: grab;
    position: relative;
}

.pdf-item:hover {
    background: var(--bg-hover);
    transform: translateX(4px);
}

.pdf-item.dragging {
    opacity: 0.5;
    cursor: grabbing;
}

.pdf-item .drag-handle {
    width: 20px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    cursor: grab;
    transition: color 0.3s ease;
}

.pdf-item .drag-handle:hover {
    color: var(--text-secondary);
}

.pdf-item .pdf-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.pdf-item .pdf-info {
    flex: 1;
    min-width: 0;
}

.pdf-item .pdf-name {
    font-size: 0.85rem;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    word-break: break-all;
    line-height: 1.2;
}

.pdf-item .pdf-size {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.pdf-item .pdf-pages {
    font-size: 0.7rem;
    color: var(--text-muted);
    font-style: italic;
}

.pdf-item .pdf-actions {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.pdf-item .move-up-btn,
.pdf-item .move-down-btn,
.pdf-item .remove-pdf {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 0.7rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.pdf-item .move-up-btn,
.pdf-item .move-down-btn {
    background: rgba(102, 126, 234, 0.8);
}

.pdf-item .move-up-btn:hover,
.pdf-item .move-down-btn:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

.pdf-item .move-up-btn:disabled,
.pdf-item .move-down-btn:disabled {
    background: rgba(102, 126, 234, 0.3);
    cursor: not-allowed;
    transform: none;
}

.pdf-item .remove-pdf {
    background: rgba(239, 68, 68, 0.9);
}

.pdf-item .remove-pdf:hover {
    background: #ef4444;
    transform: scale(1.1);
}

/* Merge options */
.merge-options {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Split PDF Options */
.split-options {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.split-methods {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.method-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.method-option:hover {
    background: var(--bg-hover);
}

.method-option input[type="radio"] {
    accent-color: var(--primary-color);
}

.method-option label {
    flex: 1;
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
}

.page-input {
    padding: 0.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.85rem;
    width: 120px;
}

.page-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Password Settings */
.password-settings {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.password-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.password-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.password-field label {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

.password-field input {
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.password-field input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.permissions {
    margin-top: 0.5rem;
}

.permissions h5 {
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    color: var(--text-primary);
    font-size: 0.85rem;
}

.checkbox-label input[type="checkbox"] {
    accent-color: var(--primary-color);
}

/* Page Selection */
.page-selection {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.selection-tools {
    display: flex;
    gap: 0.5rem;
}

.selection-tools .btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
}

.pages-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.5rem;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
    max-height: 200px;
    padding-right: 0.5rem;
}

.page-thumbnail {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.page-thumbnail:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.page-thumbnail.selected {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.page-thumbnail .page-number {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.page-range-input {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.page-range-input label {
    color: var(--text-primary);
    font-size: 0.85rem;
}

/* PDF Settings Sidebar */
.pdf-settings-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    overflow-y: auto;
    max-height: 100%;
    padding-right: 0.5rem;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.pdf-settings-sidebar::-webkit-scrollbar {
    width: 6px;
}

.pdf-settings-sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.pdf-settings-sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.pdf-settings-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

.settings-card, .actions-card {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    position: relative;
}

.settings-card::before, .actions-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group:last-child {
    margin-bottom: 0;
}

/* Settings Groups for Different Features */
.settings-group {
    display: none;
}

.settings-group.active {
    display: block;
}

/* Action Groups */
.action-group {
    display: none;
    flex-direction: column;
    gap: 1rem;
}

.action-group.active {
    display: flex;
}

.setting-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.setting-label i {
    color: var(--primary-color);
    width: 16px;
}

.setting-select {
    width: 100%;
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.setting-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-large {
    width: 100%;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 12px;
    margin-bottom: 1rem;
}

/* Generate PDF Button Glow Effect */
#convertToPdfBtn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    box-shadow:
        0 4px 15px rgba(102, 126, 234, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: pulse-glow 2s infinite;
}

#convertToPdfBtn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.6),
        0 4px 12px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);
}

#convertToPdfBtn:not(:disabled):active {
    transform: translateY(0);
    box-shadow:
        0 4px 15px rgba(102, 126, 234, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.2);
}

#convertToPdfBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#convertToPdfBtn:not(:disabled):hover::before {
    left: 100%;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow:
            0 4px 15px rgba(102, 126, 234, 0.4),
            0 2px 8px rgba(0, 0, 0, 0.2);
    }
    50% {
        box-shadow:
            0 4px 20px rgba(102, 126, 234, 0.6),
            0 2px 10px rgba(0, 0, 0, 0.3);
    }
}

.conversion-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.info-item span:last-child {
    font-weight: 600;
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .pdf-editor-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pdf-settings-sidebar {
        order: -1;
        max-height: none;
        overflow-y: visible;
    }

    .settings-card, .actions-card {
        margin-bottom: 0;
    }

    .pdf-editor-nav {
        overflow-x: auto;
        scrollbar-width: thin;
    }

    .nav-btn {
        min-width: 120px;
    }
}

@media (max-width: 768px) {
    .pdf-editor-container {
        padding: 0.75rem;
        min-height: 100vh;
        max-height: 100vh;
    }

    .pdf-editor-content {
        gap: 1rem;
    }

    .settings-card, .actions-card {
        padding: 1rem;
    }

    .pdf-editor-nav {
        gap: 0.25rem;
        padding: 0.25rem;
    }

    .nav-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        min-width: 100px;
    }

    .nav-btn span {
        display: none;
    }

    .nav-btn i {
        font-size: 1.1rem;
    }

    .upload-zone {
        height: 220px;
        padding: 2rem 1rem;
    }

    .pages-preview {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    }
}

    .upload-zone {
        padding: 2rem 1.5rem;
        height: 260px;
        gap: 1rem;
    }

    .upload-icon {
        width: 56px;
        height: 56px;
        font-size: 1.6rem;
        border-radius: 16px;
    }

    .upload-zone h3 {
        font-size: 1.2rem;
    }

    .upload-zone p {
        font-size: 0.9rem;
    }

    .format-tags {
        gap: 0.3rem;
    }

    .format-tag {
        padding: 0.3rem 0.6rem;
        font-size: 0.75rem;
    }

    .images-list {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.75rem;
    }
}

/* Document Converter Styles */
#documentConverterScreen {
    overflow: hidden;
}

.doc-converter-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 1.5rem;
    height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: hidden;
}

.doc-converter-container .screen-header {
    flex-shrink: 0;
    margin-bottom: 0;
    padding: 0.75rem 0;
}

.doc-converter-container .screen-header h2 {
    font-size: 1.5rem;
    margin: 0;
}

.doc-converter-layout {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 1.5rem;
    flex: 1;
    min-height: 0;
}

.doc-settings-sidebar {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow-y: auto;
    padding-right: 0.5rem;
}

.selected-docs {
    margin-top: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
}

.docs-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
}

.doc-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.doc-item:hover {
    border-color: var(--border-hover);
    transform: translateY(-1px);
}

.doc-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.doc-info {
    flex: 1;
    min-width: 0;
}

.doc-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.doc-size {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.remove-doc {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.remove-doc:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-label {
    background-color: #667eea;
}

input:checked + .toggle-label:before {
    transform: translateX(26px);
}

/* Responsive Design for Document Converter */
@media (max-width: 1024px) {
    .doc-converter-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .doc-settings-sidebar {
        order: -1;
        max-height: 400px;
    }
}

@media (max-width: 768px) {
    .doc-converter-container {
        padding: 1rem;
    }

    .docs-list {
        max-height: 200px;
    }

    .doc-item {
        padding: 0.5rem;
        gap: 0.75rem;
    }

    .doc-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: auto;
}

.stat-card {
    background: linear-gradient(145deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    backdrop-filter: blur(10px);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a67d8 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    display: block;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    display: block;
}

.welcome-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.welcome-header {
    margin-bottom: 1.5rem;
    text-align: center;
    flex-shrink: 0;
}

.welcome-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.welcome-header h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.welcome-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.question-type-selection {
    margin-bottom: 1.5rem;
    text-align: center;
    flex-shrink: 0;
}

.question-type-selection h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.type-buttons {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.type-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    width: 250px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.type-btn:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.type-btn.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.type-btn i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.type-btn span {
    font-size: 1.1rem;
    font-weight: 600;
}

.type-btn small {
    opacity: 0.8;
    font-size: 0.9rem;
}

.input-methods {
    margin-bottom: 1rem;
    text-align: center;
    flex-shrink: 0;
}

.input-methods h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.input-options {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.input-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    width: 150px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.input-btn:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.input-btn i {
    font-size: 1.5rem;
    color: #667eea;
}

.input-btn span {
    font-weight: 500;
    color: var(--text-primary);
}

/* Modern Card Styles */
.modern-card {
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    margin-bottom: 2rem;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.modern-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.card-header {
    padding: 18px 18px 14px 18px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    min-height: 60px;
}

.header-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    flex-shrink: 0;
}

.header-content {
    flex: 1;
    min-width: 0;
    padding-top: 2px;
}

.card-title {
    margin: 0 0 4px 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.3;
    max-width: 100%;
}

.card-subtitle {
    margin: 0;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.3;
    max-width: 100%;
    word-wrap: break-word;
}

.card-body {
    padding: 24px;
}

/* Model Selection Card Styles */
.model-selector-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.selector-wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.selector-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

.selector-label i {
    color: #667eea;
}

.custom-select-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.modern-select {
    width: 100%;
    padding: 16px 50px 16px 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 500;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.modern-select:hover {
    border-color: rgba(102, 126, 234, 0.5);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
}

.modern-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.modern-select option {
    background: #1a1a2e;
    color: #ffffff;
    padding: 12px;
}

.select-arrow {
    position: absolute;
    right: 16px;
    pointer-events: none;
    color: rgba(255, 255, 255, 0.6);
    transition: transform 0.3s ease;
}

.custom-select-wrapper:hover .select-arrow {
    color: #667eea;
    transform: translateY(-1px);
}

.model-status-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: blur(10px);
}

.status-indicator-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.status-unknown {
    background: #fbbf24;
}

.status-unknown::before {
    background: #fbbf24;
}

.status-available {
    background: #10b981;
}

.status-available::before {
    background: #10b981;
}

.status-error {
    background: #ef4444;
}

.status-error::before {
    background: #ef4444;
}

.status-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 0.95rem;
}

.status-details {
    margin-top: 4px;
}

.status-description {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
    line-height: 1.4;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
}

/* Model Management Card Styles */
.management-grid {
    display: grid;
    gap: 24px;
}

.management-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.section-title i {
    color: #667eea;
    font-size: 1rem;
}

.action-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.modern-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    color: #ffffff;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.btn-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
    flex: 1;
}

.btn-title {
    font-weight: 600;
    font-size: 0.95rem;
    color: #ffffff;
}

.btn-subtitle {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.2;
}

/* Button Color Variants */
.btn-add .btn-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-add:hover .btn-icon {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: scale(1.1);
}

.btn-remove .btn-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-remove:hover .btn-icon {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: scale(1.1);
}

.btn-view .btn-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-view:hover .btn-icon {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: scale(1.1);
}

.btn-test .btn-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
}

.btn-test:hover .btn-icon {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    transform: scale(1.1);
}

.btn-api .btn-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.btn-api:hover .btn-icon {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: scale(1.1);
}

/* Question Settings Card Styles */
.settings-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.setting-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.setting-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.setting-card:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.setting-card:hover::before {
    opacity: 1;
}

.setting-icon-wrapper {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
}

.setting-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.setting-content {
    text-align: center;
}

.setting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.setting-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.setting-value-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    min-width: 40px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.setting-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0 0 20px 0;
}

.setting-input-wrapper {
    margin-top: 16px;
}

.modern-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modern-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.modern-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6);
}

.modern-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.modern-slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6);
}

.modern-slider::-webkit-slider-track {
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0.3;
}

.modern-slider::-moz-range-track {
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0.3;
    border: none;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    padding: 0 10px;
}

.slider-labels span {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 500;
}

.setting-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.setting-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-select:hover {
    border-color: var(--text-secondary);
}

.setting-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%9ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

.model-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-available {
    background: #10b981;
    box-shadow: 0 0 6px rgba(16, 185, 129, 0.4);
}

.status-rate-limited {
    background: #f59e0b;
    box-shadow: 0 0 6px rgba(245, 158, 11, 0.4);
}

.status-unavailable {
    background: #ef4444;
    box-shadow: 0 0 6px rgba(239, 68, 68, 0.4);
}

.status-unknown {
    background: #6b7280;
    animation: pulse 2s infinite;
}

.status-text {
    color: var(--text-secondary);
}

/* Legacy styles removed - using modern card styles instead */

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.setting-item:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-light);
}

.setting-info {
    flex: 1;
}

.setting-info label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    cursor: pointer;
}

.setting-info label i {
    color: #667eea;
    font-size: 1.1rem;
}

.setting-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.3;
}

.setting-control {
    margin-left: 1rem;
}

.setting-input {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.setting-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-input:hover {
    border-color: var(--border-hover);
}

/* Content Screen */
.content-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
    position: relative;
    z-index: 2;
    padding-top: 0; /* Remove extra padding since screen has it */
}

.screen-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
    position: relative;
    z-index: 10;
    background: var(--bg-primary);
    margin-top: 0;
    padding-top: 0;
}

.screen-header h2 {
    flex: 1;
    color: var(--text-primary);
    min-width: 200px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}

/* Compact header layout for better space usage */
.screen-header .real-time-indicators {
    order: 1;
}

.screen-header .btn {
    white-space: nowrap;
}

.screen-header .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.header-nav {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.quiz-nav {
    display: flex;
    align-items: center;
}

.input-area {
    display: none;
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.input-area.active {
    display: block;
}

/* Text Input */
#textContent {
    width: 100%;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 200px;
    transition: border-color 0.3s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

#textContent:focus {
    outline: none;
    border-color: var(--border-focus);
}

/* Upload Zones */
.upload-zone {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-tertiary);
}

.upload-zone:hover {
    border-color: var(--border-hover);
    background: var(--bg-hover);
}

.upload-zone.dragover {
    border-color: var(--border-hover);
    background: var(--bg-accent);
    transform: scale(1.01);
}

.upload-zone i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
    display: block;
}

.upload-zone p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.upload-zone small {
    color: var(--text-muted);
}

/* File Info */
.file-info {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.file-details {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-details i {
    color: #667eea;
}

/* Image Preview */
.image-preview {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.image-preview img {
    max-width: 300px;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.image-preview .btn {
    position: absolute;
    top: -10px;
    right: -10px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Input Actions */
.input-actions {
    margin-top: 2rem;
    text-align: center;
}

/* ===== AI PROCESSING SCREEN ===== */
.ai-processing-container {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

/* Animated Background */
.processing-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: 1;
}

.neural-network {
    position: relative;
    width: 100%;
    height: 100%;
}

.node {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: pulse 2s infinite ease-in-out;
}

.node-1 { top: 20%; left: 15%; animation-delay: 0s; }
.node-2 { top: 30%; right: 20%; animation-delay: 0.5s; }
.node-3 { bottom: 25%; left: 25%; animation-delay: 1s; }
.node-4 { bottom: 35%; right: 15%; animation-delay: 1.5s; }
.node-5 { top: 50%; left: 50%; animation-delay: 0.8s; }

.connection {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: flow 3s infinite linear;
}

.conn-1 { top: 22%; left: 16%; width: 200px; transform: rotate(15deg); }
.conn-2 { top: 32%; right: 22%; width: 150px; transform: rotate(-25deg); }
.conn-3 { bottom: 27%; left: 27%; width: 180px; transform: rotate(45deg); }
.conn-4 { bottom: 37%; right: 17%; width: 160px; transform: rotate(-35deg); }

/* Main Content */
.processing-content {
    position: relative;
    z-index: 10;
    text-align: center;
    color: white;
    max-width: 600px;
    padding: 2rem;
}

/* AI Brain Animation */
.ai-brain-container {
    margin-bottom: 3rem;
    position: relative;
}

.brain-core {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 120px;
}

.brain-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: brainPulse 2s infinite ease-in-out;
}

.brain-pulse::before,
.brain-pulse::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: brainPulse 2s infinite ease-in-out;
}

.brain-pulse::before {
    width: 140px;
    height: 140px;
    animation-delay: 0.3s;
}

.brain-pulse::after {
    width: 160px;
    height: 160px;
    animation-delay: 0.6s;
}

.brain-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
    color: white;
    animation: float 3s ease-in-out infinite;
}

.thinking-dots {
    position: absolute;
    top: -20px;
    right: -20px;
    display: flex;
    gap: 4px;
}

.dot {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: thinking 1.5s infinite ease-in-out;
}

.dot-1 { animation-delay: 0s; }
.dot-2 { animation-delay: 0.2s; }
.dot-3 { animation-delay: 0.4s; }

/* Title and Status */
.processing-info {
    margin-bottom: 3rem;
}

.processing-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.title-gradient {
    background: linear-gradient(45deg, #ffffff, #e0e7ff, #ffffff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.processing-status {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
    animation: fadeInOut 2s ease-in-out infinite;
}

/* Advanced Progress Bar */
.progress-container {
    margin-bottom: 3rem;
}

.progress-track {
    position: relative;
    width: 100%;
    max-width: 400px;
    height: 12px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    margin: 0 auto 1rem;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00f5ff, #0080ff, #0040ff);
    background-size: 200% 100%;
    border-radius: 20px;
    width: 0%;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    animation: progressGlow 2s ease-in-out infinite;
}

.progress-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    border-radius: 20px;
    animation: progressSweep 2s linear infinite;
}

.progress-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: particleFloat 3s linear infinite;
}

.particle-1 { left: 20%; animation-delay: 0s; }
.particle-2 { left: 50%; animation-delay: 1s; }
.particle-3 { left: 80%; animation-delay: 2s; }

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 400px;
    margin: 0 auto;
}

.progress-percentage {
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.progress-eta {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Processing Steps */
.processing-steps {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.5;
    transition: all 0.5s ease;
}

.step.active {
    opacity: 1;
    transform: scale(1.1);
}

.step.completed {
    opacity: 0.8;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.5s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.step.active .step-icon {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    animation: stepPulse 1s ease-in-out infinite;
}

.step.completed .step-icon {
    background: rgba(0, 255, 0, 0.3);
    color: #00ff00;
}

.step-text {
    font-size: 0.9rem;
    text-align: center;
    max-width: 80px;
}

/* Error Dialogs */
.error-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.dialog-content {
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    animation: dialogSlideIn 0.3s ease-out;
}

.dialog-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #ff6b6b;
}

.rate-limit-dialog .dialog-icon {
    color: #ffa726;
}

.dialog-content h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.5rem;
}

.dialog-content p {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.dialog-content ul {
    text-align: left;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.dialog-content li {
    margin-bottom: 0.5rem;
}

.dialog-options {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.dialog-options .btn {
    min-width: 120px;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.dialog-options .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.dialog-options .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.dialog-options .btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.dialog-options .btn-secondary:hover {
    background: var(--border-color);
    transform: translateY(-2px);
}

.retry-countdown {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
}

.retry-countdown span {
    font-weight: 700;
    color: #ffa726;
    font-size: 1.1rem;
}



/* Model Selection Dialog */
.model-selection-dialog .dialog-icon {
    color: #667eea;
}

.model-selection-container {
    margin: 1.5rem 0;
    text-align: left;
}

.model-selection-container label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 600;
}

.model-selection-container .setting-select {
    width: 100%;
    margin-bottom: 0;
}

@keyframes dialogSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Mobile responsive for dialogs */
@media (max-width: 768px) {
    .dialog-content {
        padding: 1.5rem;
        margin: 1rem;
    }

    .dialog-options {
        flex-direction: column;
    }

    .dialog-options .btn {
        width: 100%;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

/* Legacy model management styles removed - using modern card styles instead */
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
    padding: 20px;
    box-sizing: border-box;
    overflow: hidden;
}

.modal-content {
    background: var(--card-bg);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    border: 1px solid var(--border-color);
    position: relative;
    margin: 0 auto;
    box-sizing: border-box;
}

.modal-large {
    max-width: min(700px, calc(100vw - 40px));
    width: 100%;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--primary-gradient);
    border-radius: 16px 16px 0 0;
}

.modal-header h3 {
    color: white;
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.modal-body {
    padding: 24px;
    overflow-x: hidden;
    word-wrap: break-word;
}

.modal-body * {
    max-width: 100%;
    box-sizing: border-box;
}

.modal-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 0 0 16px 16px;
    flex-wrap: wrap;
}

/* Modal Responsive Styles */
@media (max-width: 768px) {
    .modal-overlay {
        padding: 10px;
    }

    .modal-content {
        max-width: 100%;
        width: 100%;
        max-height: calc(100vh - 20px);
        margin: 0;
    }

    .modal-large {
        max-width: 100%;
        width: 100%;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .modal-overlay {
        padding: 5px;
    }

    .modal-content {
        max-height: calc(100vh - 10px);
        border-radius: 12px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 12px;
    }
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-input, .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
    max-width: 100%;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-help {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

/* Model Info Styles */
.model-info {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    margin-top: 12px;
}

.info-item {
    margin-bottom: 8px;
    font-size: 14px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-item strong {
    color: var(--text-primary);
}

/* Models List Styles */
.models-list {
    max-height: 400px;
    overflow-y: auto;
}

.model-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 12px;
    background: var(--bg-secondary);
    transition: background 0.2s;
}

.model-item:hover {
    background: var(--hover-bg);
}

.model-item:last-child {
    margin-bottom: 0;
}

.model-details {
    flex: 1;
}

.model-id {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 4px;
}

.model-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.model-description {
    font-size: 12px;
    color: var(--text-secondary);
}

.model-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-available {
    background: #10b981;
}

.status-rate-limited {
    background: #f59e0b;
}

.status-unavailable {
    background: #ef4444;
}

.status-unknown {
    background: #6b7280;
}

/* Model Testing Styles */
.test-controls {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.test-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

.test-results {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
}

.test-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.test-placeholder i {
    font-size: 2rem;
    margin-bottom: 12px;
    opacity: 0.5;
}

.test-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    transition: background 0.2s;
}

.test-item:hover {
    background: var(--hover-bg);
}

.test-item:last-child {
    border-bottom: none;
}

.test-model-info {
    flex: 1;
}

.test-model-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.test-model-id {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--text-secondary);
}

.test-status {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: flex-end;
}

.test-status-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
}

.test-status-testing {
    background: #3b82f6;
    animation: pulse 1.5s infinite;
}

.test-status-success {
    background: #10b981;
}

.test-status-failed {
    background: #ef4444;
}

.test-status-pending {
    background: #6b7280;
}

.test-timing {
    font-size: 11px;
    color: var(--text-secondary);
    margin-left: 8px;
}

.test-error {
    font-size: 11px;
    color: #ef4444;
    margin-top: 4px;
    max-width: 200px;
    word-wrap: break-word;
}

.test-success-info {
    font-size: 11px;
    color: #10b981;
    margin-top: 4px;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Notification Container */
.notification-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1001;
    max-width: 400px;
}

.notification {
    background: white;
    border-left: 4px solid #667eea;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-width: 350px;
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    word-wrap: break-word;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

/* ===== ANIMATIONS ===== */
@keyframes pulse {
    0%, 100% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes flow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes brainPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.6;
    }
}

@keyframes float {
    0%, 100% { transform: translate(-50%, -50%) translateY(0px); }
    50% { transform: translate(-50%, -50%) translateY(-10px); }
}

@keyframes thinking {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    40% {
        opacity: 1;
        transform: scale(1.3);
    }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes progressGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 245, 255, 0.8),
                    0 0 30px rgba(0, 128, 255, 0.6);
    }
}

@keyframes progressSweep {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(400%); }
}

@keyframes particleFloat {
    0% {
        transform: translateY(0px) translateX(0px);
        opacity: 0;
    }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% {
        transform: translateY(-20px) translateX(10px);
        opacity: 0;
    }
}

@keyframes stepPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes slideIn {
    from {
        transform: translateY(100%) translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0) translateX(0);
        opacity: 1;
    }
}

/* Questions Display */
.questions-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.question-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.question-actions .btn {
    min-width: 180px;
}

.questions-display {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.question-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 0;
}

.question-item:last-child {
    border-bottom: none;
}

.question-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.question-number {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.question-text {
    flex: 1;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
}

.question-options {
    margin-left: 3rem;
    margin-bottom: 1rem;
}

.option-item {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.option-letter {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    color: var(--text-primary);
}

.option-item.correct .option-letter {
    background: #d4edda;
    border-color: #28a745;
    color: #28a745;
}

.question-answer {
    margin-left: 3rem;
    padding: 1rem;
    background: var(--success-bg);
    border-radius: 8px;
    border-left: 4px solid var(--success-color);
    color: var(--text-primary);
}

.answer-label {
    font-weight: 600;
    color: #28a745;
    margin-bottom: 0.5rem;
}

.question-explanation {
    margin-left: 3rem;
    margin-top: 1rem;
    padding: 1rem;
    background: var(--warning-bg);
    border-radius: 8px;
    border-left: 4px solid var(--warning-color);
    color: var(--text-primary);
}

.explanation-label {
    font-weight: 600;
    color: #856404;
    margin-bottom: 0.5rem;
}

/* Quiz Screen */
.quiz-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    gap: 1rem;
}

.quiz-progress {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.quiz-score {
    font-size: 1.1rem;
    color: #667eea;
}

.quiz-content {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.quiz-question {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.quiz-options {
    margin-bottom: 0.5rem;
}

.quiz-option {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    width: 100%;
}

.quiz-option:hover {
    border-color: var(--border-hover);
    background: var(--bg-secondary);
    transform: var(--hover-transform);
}

.quiz-option.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.quiz-option.correct {
    border-color: var(--success-color);
    background: var(--success-bg);
    color: var(--text-primary);
}

.quiz-option.incorrect {
    border-color: var(--error-color);
    background: var(--error-bg);
    color: var(--text-primary);
}

.option-indicator {
    background: var(--bg-secondary);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    flex-shrink: 0;
}

.quiz-option.selected .option-indicator {
    background: white;
    color: #667eea;
    border: 1px solid #667eea;
}

.quiz-feedback {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.feedback-correct {
    border-left: 4px solid var(--success-color);
    background: var(--success-bg);
    color: var(--text-primary);
}

.feedback-incorrect {
    border-left: 4px solid var(--error-color);
    background: var(--error-bg);
    color: var(--text-primary);
}

.feedback-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.feedback-correct .feedback-title {
    color: var(--text-primary);
}

.feedback-incorrect .feedback-title {
    color: var(--text-primary);
}

/* Ensure all feedback text is visible */
.quiz-feedback p,
.quiz-feedback span,
.quiz-feedback div {
    color: var(--text-primary) !important;
}

.feedback-correct p,
.feedback-correct span,
.feedback-correct div {
    color: var(--text-primary) !important;
}

.feedback-incorrect p,
.feedback-incorrect span,
.feedback-incorrect div {
    color: var(--text-primary) !important;
}

.quiz-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 0.5rem;
}

/* Results Screen */
.results-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.results-header {
    margin-bottom: 3rem;
}

.results-icon {
    font-size: 4rem;
    color: #ffc107;
    margin-bottom: 1rem;
}

.results-display {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
}

.score-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.score-item {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
}

.score-value {
    font-size: 2rem;
    font-weight: 600;
    color: #667eea;
    display: block;
}

.score-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* History Screen */
.history-content {
    padding: 2rem 0;
}

.saved-quizzes-section,
.quiz-history-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.section-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-header h3 i {
    color: #667eea;
}

.section-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Clean Save Quiz Dialog Styles */
#saveQuizNameDialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
    padding: 1rem;
    box-sizing: border-box;
}

#saveQuizNameDialog .modal-dialog {
    background: var(--card-bg);
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    width: 100%;
    max-width: 450px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideInUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    margin: 0 auto;
}

#saveQuizNameDialog .dialog-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1.25rem 1.5rem;
    position: relative;
}

#saveQuizNameDialog .dialog-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

#saveQuizNameDialog .dialog-header h3 i {
    font-size: 1.2rem;
    opacity: 0.9;
}

#saveQuizNameDialog .close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

#saveQuizNameDialog .close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

#saveQuizNameDialog .dialog-content {
    padding: 1.5rem;
}

#saveQuizNameDialog .form-group {
    margin-bottom: 1.5rem;
}

#saveQuizNameDialog .form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

#saveQuizNameDialog #customQuizName {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
    font-family: inherit;
}

#saveQuizNameDialog #customQuizName:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.quiz-info-preview {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.quiz-info-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.quiz-summary-text {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.6;
    margin: 0;
}

.quiz-summary-highlight {
    color: #667eea;
    font-weight: 700;
}

#saveQuizNameDialog .dialog-footer {
    padding: 1.25rem 1.5rem;
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

#saveQuizNameDialog .dialog-footer .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 100px;
    justify-content: center;
}

#saveQuizNameDialog .dialog-footer .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

#saveQuizNameDialog .dialog-footer .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

#saveQuizNameDialog .dialog-footer .btn-secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

#saveQuizNameDialog .dialog-footer .btn-secondary:hover {
    background: var(--accent-bg);
    color: var(--text-primary);
    border-color: #667eea;
}

.form-text {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: block;
    opacity: 0.8;
}

#saveQuizNameDialog #customQuizName::placeholder {
    color: var(--text-secondary);
    opacity: 0.6;
}

#saveQuizNameDialog #customQuizName:hover {
    border-color: #667eea;
}

/* RTL Support for Save Quiz Dialog */
[dir="rtl"] #saveQuizNameDialog .dialog-header h3 {
    flex-direction: row-reverse;
}

[dir="rtl"] #saveQuizNameDialog .close-btn {
    right: auto;
    left: 1rem;
}

[dir="rtl"] #saveQuizNameDialog .dialog-footer {
    justify-content: flex-start;
}

[dir="rtl"] #saveQuizNameDialog .dialog-footer .btn {
    flex-direction: row-reverse;
}

[dir="rtl"] #saveQuizNameDialog #customQuizName {
    text-align: right;
    direction: rtl;
}

[dir="rtl"] #saveQuizNameDialog .form-group label {
    text-align: right;
}

[dir="rtl"] .quiz-summary-text {
    text-align: right;
}

/* Inline Save Quiz Form */
.inline-save-form {
    margin-top: 2rem;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.inline-save-form.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.save-form-container {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.03),
        rgba(118, 75, 162, 0.03)
    );
    border: 1px solid rgba(102, 126, 234, 0.15);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.save-form-container {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.08),
        rgba(118, 75, 162, 0.08)
    );
    border-color: rgba(102, 126, 234, 0.25);
}

.save-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.save-form-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.save-form-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    position: relative;
}

.save-form-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 18px;
    z-index: -1;
    opacity: 0.3;
    filter: blur(8px);
}

.save-form-title h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.save-form-title p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.95rem;
    opacity: 0.8;
}

.save-form-content {
    display: grid;
    gap: 2rem;
}

.quiz-preview-card {
    background: rgba(102, 126, 234, 0.05);
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    position: relative;
}

.quiz-preview-card {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.2);
}

.preview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
}

.stat-item {
    background: rgba(255, 255, 255, 0.08);
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.stat-item i {
    color: #667eea;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.stat-value {
    color: #667eea;
    font-weight: 700;
    font-size: 0.95rem;
    margin-left: auto;
}

.name-input-group {
    display: grid;
    gap: 0.75rem;
}

.name-input-group label {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
}

.input-wrapper {
    position: relative;
}

.quiz-name-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 16px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
}

.quiz-name-input:focus {
    outline: none;
    border-color: #667eea;
    background: var(--input-bg);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.quiz-name-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.6;
}

.quiz-name-input {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(102, 126, 234, 0.3);
}

.quiz-name-input:focus {
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
}

.input-decoration {
    position: absolute;
    inset: 0;
    border-radius: 16px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.quiz-name-input:focus + .input-decoration {
    opacity: 1;
}

.input-decoration {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
}

.input-hint {
    color: var(--text-secondary);
    font-size: 0.85rem;
    opacity: 0.7;
    font-style: italic;
}

.setting-hint {
    color: var(--text-secondary);
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 4px;
    display: block;
    line-height: 1.3;
}

.setting-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.setting-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-primary);
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.warning-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
    padding: 8px 12px;
    margin-top: 8px;
}

.warning-message small {
    color: #ef4444;
    font-size: 0.8rem;
    line-height: 1.3;
}

.save-form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1rem;
}

.save-form-actions .btn {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.save-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.cancel-btn {
    background: var(--surface-color);
    border: 2px solid rgba(102, 126, 234, 0.2);
    color: var(--text-secondary);
}

.cancel-btn:hover {
    background: rgba(102, 126, 234, 0.05);
    border-color: #667eea;
    color: var(--text-primary);
    transform: translateY(-1px);
}

.cancel-btn {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(102, 126, 234, 0.3);
}

.cancel-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
}

/* RTL Support for Inline Save Form */
[dir="rtl"] .save-form-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .save-form-title {
    text-align: right;
}

[dir="rtl"] .stat-item {
    flex-direction: row-reverse;
}

[dir="rtl"] .stat-value {
    margin-left: 0;
    margin-right: auto;
}

[dir="rtl"] .name-input-group label {
    text-align: right;
}

[dir="rtl"] .quiz-name-input {
    text-align: right;
    direction: rtl;
}

[dir="rtl"] .save-form-actions {
    justify-content: flex-start;
}

[dir="rtl"] .save-form-actions .btn {
    flex-direction: row-reverse;
}



/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.saved-quizzes-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* When saved quizzes list contains only empty state, make it full width */
.saved-quizzes-list:has(.empty-state) {
    display: block;
}

/* Fallback for browsers that don't support :has() */
.saved-quizzes-list .empty-state:only-child {
    grid-column: 1 / -1;
}

.saved-quiz-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.saved-quiz-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: #667eea;
}

.saved-quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.saved-quiz-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
    line-height: 1.3;
    flex: 1;
    margin-right: 1rem;
}

.saved-quiz-type {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.saved-quiz-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.saved-quiz-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.saved-quiz-questions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.saved-quiz-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* Empty state styling for saved quizzes - more specific selector */
.saved-quizzes-section .saved-quizzes-list .empty-state {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    padding: 3rem 2rem !important;
    min-height: 200px !important;
    background: var(--bg-secondary) !important;
    border: 2px dashed var(--border-color) !important;
    border-radius: 12px !important;
    color: var(--text-secondary) !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.saved-quizzes-section .saved-quizzes-list .empty-state i {
    font-size: 3rem !important;
    color: #667eea !important;
    margin-bottom: 1rem !important;
    opacity: 0.7 !important;
}

.saved-quizzes-section .saved-quizzes-list .empty-state h3 {
    margin: 0 0 0.5rem 0 !important;
    color: var(--text-primary) !important;
    font-size: 1.2rem !important;
}

.saved-quizzes-section .saved-quizzes-list .empty-state p {
    margin: 0 !important;
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
    max-width: 300px !important;
}



/* History Filters Compact */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.history-filters-compact {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.filter-select-compact {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.85rem;
    min-width: 120px;
    transition: all 0.3s ease;
}

.filter-select-compact:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.filter-select-compact:hover {
    border-color: #667eea;
}

.history-stats-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.history-list {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.history-item {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.history-item:hover {
    background: var(--bg-primary);
    transform: translateX(5px);
}

.history-item:last-child {
    border-bottom: none;
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.history-item-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

.history-item-title i {
    color: #667eea;
    font-size: 1.2rem;
}

.history-item-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.history-item-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.history-stat {
    text-align: center;
    padding: 0.75rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.history-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.history-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.score-excellent { color: #10b981; }
.score-good { color: #3b82f6; }
.score-average { color: #f59e0b; }
.score-poor { color: #ef4444; }

/* Statistics Screen */
.statistics-content {
    padding: 2rem 0;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;
}

.stats-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    padding: 2rem;
}

.stats-section h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 0 1.5rem 0;
    color: var(--text-primary);
    font-size: 1.3rem;
}

.stats-section h3 i {
    color: #667eea;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    border-color: #667eea;
}

.stat-card.large {
    padding: 2rem;
    min-height: 140px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-card.large .stat-icon {
    width: 80px;
    height: 80px;
}

.stat-icon i {
    color: white;
    font-size: 1.5rem;
}

.stat-card.large .stat-icon i {
    font-size: 2rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stat-card.large .stat-number {
    font-size: 3rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-sublabel {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    opacity: 0.8;
}

.stat-progress {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.8s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
    min-width: 35px;
}



/* Loading States */
.loading-placeholder {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.loading-placeholder i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #667eea;
}

.loading-placeholder p {
    margin: 0;
    font-size: 1.1rem;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--border-color);
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.empty-state p {
    margin: 0;
    line-height: 1.5;
}

/* Floating Back Button */
.floating-back-btn {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transform: scale(0);
    opacity: 0;
}

.floating-back-btn[style*="flex"] {
    transform: scale(1);
    opacity: 1;
    animation: bounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.floating-back-btn:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.floating-back-btn:active {
    transform: translateY(-1px) scale(1.05);
}

@keyframes bounceIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Global AI Settings Button */
.global-ai-btn {
    position: fixed;
    top: 20px;
    left: 180px;
    z-index: 1000;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.global-ai-btn:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.global-ai-btn i {
    animation: aiPulse 2s infinite;
}

@keyframes aiPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Global AI Settings Sidebar */
.global-ai-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 380px;
    height: 100vh;
    background: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    z-index: 1001;
    transition: right 0.3s ease;
    overflow-y: auto;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
}

.global-ai-sidebar.active {
    right: 0;
}

.ai-sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.ai-sidebar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-sidebar-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-sidebar-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.ai-sidebar-content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .type-buttons {
        flex-direction: column;
        align-items: center;
    }

    .input-options {
        flex-direction: column;
        align-items: center;
    }

    .quiz-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .question-actions {
        flex-direction: column;
    }

    .results-actions {
        flex-direction: column;
        align-items: center;
    }

    /* History & Statistics Mobile */
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .history-filters-compact {
        width: 100%;
        justify-content: space-between;
    }

    .filter-select-compact {
        flex: 1;
        min-width: auto;
    }

    .history-stats-summary {
        grid-template-columns: 1fr;
    }

    .history-item-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .stats-overview {
        grid-template-columns: 1fr;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .stat-card.large {
        min-height: auto;
    }

    .stat-progress {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    /* Floating button mobile adjustments */
    .floating-back-btn {
        bottom: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    /* Notification container mobile adjustments */
    .notification-container {
        bottom: 10px;
        right: 10px;
        max-width: calc(100vw - 100px);
    }

    .notification {
        max-width: 100%;
        font-size: 0.9rem;
    }

    /* AI Processing Screen Mobile */
    .processing-content {
        padding: 1rem;
        max-width: 90%;
    }

    .processing-title {
        font-size: 2rem;
    }

    .brain-core {
        width: 80px;
        height: 80px;
    }

    .brain-pulse {
        width: 80px;
        height: 80px;
    }

    .brain-pulse::before {
        width: 100px;
        height: 100px;
    }

    .brain-pulse::after {
        width: 120px;
        height: 120px;
    }

    .brain-icon {
        font-size: 2rem;
    }

    .progress-track {
        max-width: 300px;
        height: 10px;
    }

    .progress-percentage {
        font-size: 1.2rem;
    }

    .processing-steps {
        gap: 1rem;
    }

    .step-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .step-text {
        font-size: 0.8rem;
        max-width: 60px;
    }

    .neural-network {
        display: none; /* Hide complex animations on mobile for performance */
    }
}

/* API Key Management Dialog Styles */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.api-key-dialog {
    width: 90%;
    max-width: 700px;
    max-height: 80vh;
    overflow-y: auto;
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    animation: dialogSlideIn 0.3s ease-out;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 16px 16px 0 0;
}

.dialog-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dialog-header h3 i {
    color: #667eea;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.dialog-body {
    padding: 2rem;
}

.dialog-footer {
    padding: 1rem 2rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: 0 0 16px 16px;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.api-key-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background: var(--bg-secondary);
}

.api-key-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.api-key-section h4 i {
    color: #667eea;
}

.api-key-info {
    background: var(--bg-primary);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.api-key-info-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.api-key-info-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.api-key-info-value {
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    word-break: break-all;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.input-with-toggle {
    position: relative;
    display: flex;
    align-items: center;
}

.api-key-input {
    flex: 1;
    padding: 12px 45px 12px 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color 0.2s;
}

.api-key-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.api-key-input.valid {
    border-color: #48bb78;
}

.api-key-input.invalid {
    border-color: #f56565;
}

.toggle-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: color 0.2s;
}

.toggle-btn:hover {
    color: var(--text-primary);
    background: var(--border-color);
}

.help-text {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    line-height: 1.4;
}

.button-group {
    display: flex;
    gap: 0.75rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.instructions-list {
    margin: 0;
    padding-left: 1.25rem;
    color: var(--text-primary);
}

.instructions-list li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.instructions-list a {
    color: #667eea;
    text-decoration: none;
}

.instructions-list a:hover {
    text-decoration: underline;
}

.api-key-alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
    font-size: 0.9rem;
}

.api-key-alert.success {
    background: var(--success-bg);
    color: var(--text-primary);
    border: 1px solid #9ae6b4;
}

.api-key-alert.error {
    background: var(--error-bg);
    color: var(--text-primary);
    border: 1px solid #fc8181;
}

.api-key-alert.warning {
    background: var(--warning-bg);
    color: var(--text-primary);
    border: 1px solid #f6e05e;
}

.api-key-alert.info {
    background: var(--info-bg);
    color: var(--text-primary);
    border: 1px solid #90cdf4;
}

/* Mobile responsive for API key dialog */
@media (max-width: 768px) {
    .api-key-dialog {
        width: 95%;
        max-height: 90vh;
    }

    .dialog-header,
    .dialog-body,
    .dialog-footer {
        padding: 1rem;
    }

    .api-key-info-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .button-group {
        flex-direction: column;
    }

    .button-group .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Real-Time Indicators */
.refresh-indicator {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    opacity: 0.9;
    white-space: nowrap;
    margin-right: 0.5rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.refresh-indicator i {
    font-size: 0.7rem;
}



/* Text Color Utilities */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--error-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-primary {
    color: #667eea !important;
}

/* Specific fixes for history and statistics screens */
#historyScreen,
#statisticsScreen {
    padding-top: 0;
}

#historyScreen .content-container,
#statisticsScreen .content-container {
    margin-top: 120px; /* Push content below main header */
    padding-top: 1rem;
}

#historyScreen .screen-header,
#statisticsScreen .screen-header {
    margin-top: -120px; /* Pull header back up */
    padding-top: 120px; /* Add padding to push content down */
    background: var(--bg-primary);
    position: relative;
    z-index: 10;
}

/* Mobile responsive for real-time features */
@media (max-width: 768px) {
    .refresh-indicator {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        margin-right: 0.25rem;
    }



    #historyScreen .content-container,
    #statisticsScreen .content-container {
        margin-top: 100px; /* Less margin on mobile */
        padding-top: 0.5rem;
    }

    #historyScreen .screen-header,
    #statisticsScreen .screen-header {
        margin-top: -100px; /* Pull header back up */
        padding-top: 100px; /* Add padding to push content down */
    }

    .pagination-container {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 15px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.pagination-info {
    font-size: 14px;
    color: var(--text-secondary);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    padding: 5px 10px;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    min-width: 30px;
    text-align: center;
}

.page-number:hover {
    background: var(--bg-secondary);
    border-color: #667eea;
}

.page-number.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-controls .btn {
    padding: 5px 10px;
    font-size: 12px;
}

.pagination-controls .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Text Tools Screen Styles */
.text-tools-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    height: 100vh;
    overflow-y: auto;
}

.text-tools-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.text-tools-nav .nav-btn {
    padding: 12px 20px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.text-tools-nav .nav-btn:hover {
    background: var(--bg-hover);
    border-color: var(--border-hover);
    color: var(--text-primary);
}

.text-tools-nav .nav-btn.active {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
}

/* Mind Map Layout */
.mind-map-layout {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    height: calc(100vh - 200px);
}

.mind-map-input-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border-color);
    overflow-y: auto;
}

.mind-map-display-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.mind-map-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.mind-map-header h3 {
    color: var(--text-primary);
    font-size: 18px;
    margin: 0;
}

.mind-map-controls {
    display: flex;
    gap: 8px;
}

.mind-map-controls .btn {
    padding: 8px 12px;
    font-size: 12px;
    min-width: auto;
}

/* Mind Map Input Areas */
#textToolsScreen .input-area {
    display: none;
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

#textToolsScreen .input-area.active {
    display: block;
}

#mindMapTextContent {
    width: 100%;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 150px;
    transition: border-color 0.3s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
}

#mindMapTextContent:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Mind Map Container */
.mind-map-container {
    flex: 1;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: auto;
    min-height: 400px;
}

.mind-map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-muted);
    text-align: center;
}

.mind-map-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.mind-map-placeholder p {
    font-size: 16px;
    max-width: 300px;
}

/* Mind Map Visualization */
.mind-map-visualization {
    padding: 30px;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.mind-map-title {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 15px;
    padding: 15px 25px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-medium);
}

.mind-map-description {
    font-size: 14px;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 30px;
    max-width: 600px;
    line-height: 1.5;
}

.mind-map-nodes {
    display: flex;
    flex-direction: column;
    gap: 25px;
    width: 100%;
    max-width: 1000px;
}

/* Mind Map Node Styles */
.mind-map-node {
    position: relative;
    margin: 10px 0;
}

.mind-map-node.level-1 {
    margin-left: 0;
}

.mind-map-node.level-2 {
    margin-left: 40px;
}

.mind-map-node.level-3 {
    margin-left: 80px;
}

.node-content {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: 10px;
    padding: 15px 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.mind-map-node.level-1 .node-content {
    background: linear-gradient(135deg, #667eea20, #764ba220);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-light);
}

.mind-map-node.level-2 .node-content {
    background: var(--bg-tertiary);
    border-color: #4caf50;
}

.mind-map-node.level-3 .node-content {
    background: var(--bg-secondary);
    border-color: #ff9800;
}

.node-content:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.node-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.mind-map-node.level-1 .node-label {
    font-size: 18px;
    color: var(--primary-color);
}

.mind-map-node.level-2 .node-label {
    font-size: 16px;
    color: #4caf50;
}

.mind-map-node.level-3 .node-label {
    font-size: 14px;
    color: #ff9800;
}

.node-description {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.node-children {
    margin-top: 15px;
    padding-left: 20px;
    border-left: 2px solid var(--border-color);
    position: relative;
}

.node-children::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), transparent);
}

/* Connection Lines */
.mind-map-node.level-2::before,
.mind-map-node.level-3::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 50%;
    width: 20px;
    height: 2px;
    background: var(--border-color);
    transform: translateY(-50%);
}

.mind-map-node.level-2::before {
    background: #4caf50;
}

.mind-map-node.level-3::before {
    background: #ff9800;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .mind-map-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .mind-map-input-section {
        order: 2;
    }

    .mind-map-display-section {
        order: 1;
        min-height: 500px;
    }
}

@media (max-width: 768px) {
    .text-tools-container {
        padding: 15px;
    }

    .mind-map-layout {
        gap: 15px;
    }

    .mind-map-input-section,
    .mind-map-display-section {
        padding: 15px;
    }

    .mind-map-title {
        font-size: 20px;
        padding: 12px 20px;
    }

    .mind-map-node.level-2 {
        margin-left: 20px;
    }

    .mind-map-node.level-3 {
        margin-left: 40px;
    }
}
