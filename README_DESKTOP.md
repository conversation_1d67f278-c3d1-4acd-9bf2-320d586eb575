# MCQ & TF Question Generator - Desktop Application

A powerful desktop application for generating multiple-choice and true/false questions from educational content. This application has been converted from a Telegram bot to a standalone desktop application using Electron.

## 🚀 Features

### Core Functionality
- **Question Generation**: Create MCQ and True/False questions from various content types
- **Multiple Input Methods**: 
  - Direct text input
  - PDF document upload
  - Word document upload (DOCX, DOC)
  - Image upload with OCR processing
- **Interactive Quiz System**: Take quizzes with immediate feedback
- **Progress Tracking**: Monitor your learning progress and statistics
- **Export Options**: Save questions and results in various formats

### AI-Powered
- Uses multiple AI models for question generation
- Automatic content analysis and question creation
- Intelligent answer explanations
- Content optimization for better questions

### Desktop Features
- **Offline Capable**: Works without internet (after initial setup)
- **Local Data Storage**: All your data stays on your computer
- **Fast Performance**: No network delays for local operations
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Keyboard Shortcuts**: Efficient navigation and controls

## 📋 Requirements

- **Node.js**: Version 16 or higher
- **Python**: Version 3.7+ (for document processing)
- **Operating System**: Windows 10+, macOS 10.14+, or Linux
- **RAM**: Minimum 4GB, recommended 8GB
- **Storage**: At least 2GB free space

## 🛠️ Installation

### Quick Start
1. **Clone or download** this repository
2. **Open terminal** in the project directory
3. **Run the startup script**:
   ```bash
   node start-desktop.js
   ```

### Manual Installation
1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   - Copy `env.example` to `.env`
   - Add your OpenRouter API key:
     ```
     API_KEY=your_openrouter_api_key_here
     ```

4. **Start the application**:
   ```bash
   npm start
   ```

## 🎯 Usage

### Getting Started
1. **Launch the application**
2. **Choose question type**: MCQ or True/False
3. **Add your content**:
   - Type text directly
   - Upload a PDF or Word document
   - Upload an image with text
4. **Generate questions** and review them
5. **Take an interactive quiz** or export the questions

### Keyboard Shortcuts
- `Ctrl/Cmd + N`: New quiz
- `Ctrl/Cmd + O`: Open file
- `Ctrl/Cmd + M`: Generate MCQ questions
- `Ctrl/Cmd + T`: Generate True/False questions
- `Ctrl/Cmd + I`: Start interactive quiz
- `Ctrl/Cmd + ,`: Open settings

### File Support
- **Documents**: PDF, DOCX, DOC, TXT
- **Images**: JPG, JPEG, PNG, BMP, TIFF
- **Maximum file size**: 1GB
- **OCR Support**: Automatic text extraction from images

## ⚙️ Configuration

### Environment Variables
Create a `.env` file with these settings:

```env
# API Configuration
API_KEY=your_openrouter_api_key

# Question Settings
DEFAULT_QUESTION_COUNT=15
QUESTIONS_PER_PAGE=5
IMAGE_QUESTIONS_COUNT=5

# Performance Settings
MAX_CONCURRENT_REQUESTS=8
REQUEST_TIMEOUT=60000
CACHE_MAX_AGE_HOURS=72

# File Settings
MAX_FILE_SIZE_MB=1000
FILE_UPLOADS_PER_DAY=50

# Debug Settings
DEBUG_MODE=false
LOG_LEVEL=info
```

### Application Settings
Access settings through the application menu or `Ctrl/Cmd + ,`:
- Question generation preferences
- UI theme and language
- Export formats
- Performance options

## 📊 Data Storage

### Local Database
- **Location**: `data/desktop.db` (SQLite)
- **Contains**: Quiz sessions, statistics, user preferences
- **Backup**: Automatic daily backups in `data/backups/`

### Cache System
- **Question Cache**: Stores generated questions for faster access
- **File Cache**: Caches processed document content
- **Automatic Cleanup**: Removes old cache entries

## 🔧 Development

### Project Structure
```
src/
├── main.js              # Main Electron process
├── preload.js           # Preload script for security
├── ipcHandlers.js       # IPC communication handlers
├── config/
│   └── desktop.js       # Desktop-specific configuration
├── renderer/
│   ├── index.html       # Main UI
│   ├── styles.css       # Application styles
│   └── app.js           # Frontend logic
├── services/            # Backend services (from original bot)
├── handlers/            # Request handlers
├── database/            # Database management
└── utils/               # Utility functions
```

### Building for Distribution
```bash
# Build for current platform
npm run build

# Build for specific platforms
npm run build-win      # Windows
npm run build-mac      # macOS
npm run build-linux    # Linux

# Create installer packages
npm run dist
```

### Development Mode
```bash
# Start in development mode with hot reload
npm run dev

# Enable debug mode
DEBUG_MODE=true npm start
```

## 🐛 Troubleshooting

### Common Issues

**Application won't start**
- Check Node.js version: `node --version`
- Reinstall dependencies: `rm -rf node_modules && npm install`
- Check Python installation: `python --version`

**Questions not generating**
- Verify API key in `.env` file
- Check internet connection
- Review logs in `logs/` directory

**File processing errors**
- Ensure Python dependencies are installed: `pip install -r requirements.txt`
- Check file format is supported (PDF, DOCX, DOC, TXT, JPG, PNG, BMP, TIFF)
- Verify file isn't corrupted
- Try using the file selection dialog instead of drag & drop
- Check that file path doesn't contain special characters

**Performance issues**
- Reduce concurrent requests in settings
- Clear cache: Delete `data/cache/` folder
- Check available RAM and storage

### Log Files
- **Application logs**: `logs/info.log`
- **Error logs**: `logs/error.log`
- **Extraction logs**: `logs/extraction.log`

## 🔄 Migration from Telegram Bot

This desktop application includes all features from the original Telegram bot:
- ✅ Question generation (MCQ & TF)
- ✅ File processing (PDF, DOCX, images)
- ✅ Interactive quizzes
- ✅ Statistics and progress tracking
- ✅ Admin features (now user settings)
- ✅ Feedback system
- ✅ Caching and performance optimization

### Key Differences
- **Single User**: Designed for individual use
- **Local Storage**: No cloud dependencies
- **Enhanced UI**: Rich desktop interface
- **Offline Mode**: Works without internet
- **Better Performance**: No network latency

## 📝 License

This project is licensed under the ISC License - see the original LICENSE file for details.

## 🤝 Support

For issues, questions, or contributions:
1. Check the troubleshooting section above
2. Review log files for error details
3. Create an issue with detailed information
4. Include system information and error logs

## 🎉 Acknowledgments

- Original Telegram bot codebase
- Electron framework
- OpenRouter API for AI models
- All open-source libraries used in this project
