import os
import io
import sys
import subprocess
import argparse
import logging
import datetime
import json
import traceback
import re
import fitz  # PyMuPDF
import pytesseract
from PIL import Image, ImageFilter, ImageEnhance
import docx2txt
import tempfile
import multiprocessing
from concurrent.futures import <PERSON><PERSON>ool<PERSON>xecutor, ThreadPoolExecutor, as_completed
import pickle
# Add threading for better concurrency
import threading

# Global lock for thread safety
global_lock = threading.Lock()

# Thread-local storage
thread_local = threading.local()

# Get environment variables with defaults
EXTRACTION_PARALLEL_WORKERS = int(os.environ.get('EXTRACTION_PARALLEL_WORKERS', '6'))
EXTRACTION_TIMEOUT_MS = int(os.environ.get('EXTRACTION_TIMEOUT_MS', '1200000'))
OPTIMIZE_EXTRACTION = os.environ.get('OPTIMIZE_EXTRACTION', '1') == '1'

# Function to load environment variables from .env file
def load_env_from_file():
    try:
        # Get the directory where this script is located
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Look for .env file in parent directory
        env_path = os.path.join(os.path.dirname(script_dir), '.env')
        
        if os.path.exists(env_path):
            log_debug(f"Loading environment variables from {env_path}")
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
        else:
            log_debug(f".env file not found at {env_path}")
            return False
    except Exception as e:
        log_error(f"Error loading .env file: {str(e)}")
        return False

# Configure logging to file and suppress console output completely
log_dir = os.path.join(tempfile.gettempdir(), "extraction_logs")
if not os.path.exists(log_dir):
    os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, "extraction_service.log")

# Configure logging to file and suppress console output completely
file_handler = logging.FileHandler(log_file, mode='a')
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# Configure the logger to only log to file
logger = logging.getLogger("")
logger.setLevel(logging.DEBUG)
logger.addHandler(file_handler)
# No console handler to prevent any output to stdout/stderr

# Replace print with controlled output functions
def log_debug(message):
    # Debug logs only go to file, never to console
    try:
        logger.debug(message)
    except:
        pass

def log_info(message):
    # Info logs only go to file, never to console
    try:
        logger.info(message)
    except:
        pass

def log_error(message):
    # Error logs only go to file, never to console
    try:
        logger.debug(f"ERROR: {message}")
    except:
        pass

# Special function for essential output that JS needs to parse
def output_essential(message):
    # Only output absolutely essential information needed for parsing
    print(message)

# Load environment variables from .env file
load_env_from_file()
log_info("Environment variables loaded")

# Set Tesseract path - use environment variable if available
tesseract_path = os.environ.get('TESSERACT_PATH', r'C:\Program Files\Tesseract-OCR\tesseract.exe')
pytesseract.pytesseract.tesseract_cmd = tesseract_path
log_info(f"Using Tesseract path: {tesseract_path}")

# First check local tessdata directory
current_dir = os.path.dirname(os.path.abspath(__file__))
local_tessdata = os.path.join(current_dir, 'tessdata')

if os.path.exists(local_tessdata):
    os.environ['TESSDATA_PREFIX'] = local_tessdata
    log_debug(f"Using local tessdata directory: {local_tessdata}")
else:
    # Check if TESSDATA_PREFIX is already set in environment (e.g. from Docker)
    if 'TESSDATA_PREFIX' in os.environ and os.path.exists(os.environ['TESSDATA_PREFIX']):
        log_debug(f"Using TESSDATA_PREFIX from environment: {os.environ['TESSDATA_PREFIX']}")
    else:
        # Try to find tessdata directory
        try:
            # Try the 'where' command first (Windows)
            try:
                tesseract_base_dir = os.path.dirname(os.popen('where tesseract').read().strip())
            except:
                # Try 'which' command (Linux/Mac)
                tesseract_base_dir = os.path.dirname(os.popen('which tesseract').read().strip())
                
            if tesseract_base_dir and os.path.exists(tesseract_base_dir):
                tessdata_dir = os.path.join(tesseract_base_dir, 'tessdata')
                if os.path.exists(tessdata_dir):
                    os.environ['TESSDATA_PREFIX'] = tessdata_dir
                    log_debug(f"Set TESSDATA_PREFIX to: {tessdata_dir}")
                else:
                    # Try common installation paths
                    common_paths = [
                        # Docker paths
                        '/usr/share/tesseract-ocr/4.00/tessdata',
                        '/usr/share/tesseract-ocr/tessdata',
                        # Windows paths
                        r'C:\Program Files\Tesseract-OCR\tessdata',
                        r'C:\Program Files (x86)\Tesseract-OCR\tessdata',
                        os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'Tesseract-OCR', 'tessdata'),
                        r'C:\Users\<USER>\scoop\apps\tesseract\current\tessdata'
                    ]
                    
                    for path in common_paths:
                        if os.path.exists(path):
                            os.environ['TESSDATA_PREFIX'] = path
                            log_debug(f"Set TESSDATA_PREFIX to: {path}")
                            break
            else:
                log_error("Warning: Could not determine Tesseract installation directory")
        except Exception as e:
            log_error(f"Warning: Error finding Tesseract path: {str(e)}")
            # Try common paths anyway
            common_paths = [
                # Docker paths
                '/usr/share/tesseract-ocr/4.00/tessdata',
                '/usr/share/tesseract-ocr/tessdata',
                # Windows paths
                r'C:\Program Files\Tesseract-OCR\tessdata',
                r'C:\Program Files (x86)\Tesseract-OCR\tessdata',
                os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'Tesseract-OCR', 'tessdata'),
                r'C:\Users\<USER>\scoop\apps\tesseract\current\tessdata'
            ]
            
            for path in common_paths:
                if os.path.exists(path):
                    os.environ['TESSDATA_PREFIX'] = path
                    log_debug(f"Set TESSDATA_PREFIX to: {path}")
                    break

# Check available languages
try:
    langs = pytesseract.get_languages(config='')
    log_debug(f"Available Tesseract languages: {langs}")
except Exception as e:
    log_error(f"Warning: Could not get Tesseract languages: {str(e)}")
    langs = ['eng']  # Default to English if we can't get languages

# Helper functions for document type and question count
def get_document_type_description(page_count):
    if page_count == 1:
        return "Single-page document"
    elif page_count == 2:
        return "Two-page document"
    else:
        return "Multi-page document"

def calculate_question_count(page_count, is_image=False):
    """Calculate suggested question count based on document type and length"""
    if is_image:
        # For images, use the dedicated image question count from environment
        questions_count = int(os.environ.get('IMAGE_QUESTIONS_COUNT', '5'))
        log_info(f"Image document: using {questions_count} questions")
    else:
        # For regular documents, multiply pages by questions_per_page
        questions_per_page = int(os.environ.get('QUESTIONS_PER_PAGE', '3'))
        question_count = page_count * questions_per_page
        log_info(f"Regular document: {question_count} questions")
    
    # Log info only (don't output to console)
    if is_image:
        log_info(f"Generating {questions_count} questions")
        return questions_count
    else:
        log_info(f"Generating {question_count} questions")
        return question_count

# Enhanced image optimization for OCR
def optimize_image_for_ocr(img):
    """Optimize image for better OCR results"""
    try:
        # Convert to grayscale if it's not already
        if img.mode != 'L':
            img = img.convert('L')
        
        # Increase contrast
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.5)
        
        # Apply sharpening filter
        img = img.filter(ImageFilter.SHARPEN)
        
        # Resize if image is too small
        if img.width < 1000 or img.height < 1000:
            scale_factor = max(1000 / img.width, 1000 / img.height)
            new_width = int(img.width * scale_factor)
            new_height = int(img.height * scale_factor)
            img = img.resize((new_width, new_height), Image.LANCZOS)
        
        # Apply adaptive thresholding for better text extraction
        # This is simulated with a crude threshold
        if OPTIMIZE_EXTRACTION:
            # Convert to numpy array for more advanced processing
            try:
                import numpy as np
                from scipy import ndimage
                
                # Convert to numpy array
                img_array = np.array(img)
                
                # Apply median filter to reduce noise
                img_array = ndimage.median_filter(img_array, size=3)
                
                # Apply adaptive thresholding
                from skimage.filters import threshold_local
                block_size = 35
                threshold = threshold_local(img_array, block_size, offset=10)
                binary_img = img_array > threshold
                
                # Convert back to PIL Image
                from PIL import Image
                img = Image.fromarray((binary_img * 255).astype(np.uint8))
            except ImportError:
                # Fall back to simple thresholding if advanced libraries aren't available
                img = img.point(lambda p: p > 128 and 255)
        
        return img
    except Exception as e:
        log_error(f"Error optimizing image: {str(e)}")
        return img  # Return original image if optimization fails

# Enhanced text extraction from PDF with parallel processing
def extract_text_from_pdf(file_path):
    """Extract text from PDF using PyMuPDF with parallel processing for speed"""
    try:
        log_info(f"Opening PDF document: {file_path}")
        doc = fitz.open(file_path)
        page_count = len(doc)
        log_info(f"PDF document has {page_count} pages")
        
        # Determine optimal batch size based on number of pages
        batch_size = 1
        if page_count > 100:
            batch_size = 10
        elif page_count > 50:
            batch_size = 5
        elif page_count > 10:
            batch_size = 3
        
        # Create batches of pages
        batches = []
        for i in range(0, page_count, batch_size):
            batches.append(range(i, min(i + batch_size, page_count)))
        
        # Function to process a batch of pages
        def process_batch(batch_range):
            batch_text = []
            for page_num in batch_range:
                batch_text.append(extract_page_text(page_num))
            return batch_text
        
        # Function to extract text from a single page
        def extract_page_text(page_num):
            page = doc[page_num]
            text = page.get_text()
            
            # If page has minimal text, try OCR
            if len(text.strip()) < 50:
                try:
                    # Convert page to image
                    pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
                    img = Image.open(io.BytesIO(pix.tobytes("png")))
                    
                    # Optimize image for OCR
                    img = optimize_image_for_ocr(img)
                    
                    # Perform OCR
                    ocr_text = pytesseract.image_to_string(img, lang='eng')
                    
                    # Use OCR text if it's longer than the extracted text
                    if len(ocr_text.strip()) > len(text.strip()):
                        text = ocr_text
                except Exception as e:
                    log_error(f"Error performing OCR on PDF page {page_num}: {str(e)}")
            
            return text
        
        # Use ThreadPoolExecutor for parallel processing
        all_text = []
        with ThreadPoolExecutor(max_workers=EXTRACTION_PARALLEL_WORKERS) as executor:
            futures = [executor.submit(process_batch, batch) for batch in batches]
            for future in as_completed(futures):
                all_text.extend(future.result())
        
        # Combine all text
        combined_text = "\n\n".join(all_text)
        
        # Clean up the text
        combined_text = re.sub(r'\n{3,}', '\n\n', combined_text)  # Remove excessive newlines
        
        return combined_text, page_count
        
    except Exception as e:
        log_error(f"Error extracting text from PDF: {str(e)}")
        traceback.print_exc()
        raise

def extract_from_image(image_path):
    """Extract text from an image using OCR"""
    log_info(f"Processing image: {image_path}")
    
    try:
        # Open the image
        with Image.open(image_path) as img:
            # Optimize the image for OCR
            optimized_img = optimize_image_for_ocr(img)
            
            # Use simpler OCR approach for speed
            try:
                # Try English first (faster)
                text = pytesseract.image_to_string(optimized_img, lang='eng', config='--psm 6')
                if not text or len(text.strip()) < 50:
                    # If not enough text, try Arabic+English
                    text = pytesseract.image_to_string(optimized_img, lang='ara+eng', config='--psm 6')
            except Exception:
                # Fallback to default
                text = pytesseract.image_to_string(optimized_img)
            
            # Always 1 page for images
            page_count = 1
            
            # Save the extracted text to a file (log only, don't output)
            output_file = f"{image_path}_extracted.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(text)

            log_info(f"Saved extracted text to: {output_file}")
        
        return text, page_count
    except Exception as e:
        log_info(f"Image extraction note: {str(e)}")
        return "Image content for processing.", 1

def extract_from_docx(file_path):
    """Extract text from a DOCX file"""
    log_info(f"Extracting text from DOCX file: {file_path}")
    
    try:
        # Use docx2txt for text extraction - faster and simpler than python-docx
        text = docx2txt.process(file_path)
        if text and len(text.strip()) > 0:
            # Count newlines to estimate pages (rough estimate)
            page_count = max(1, text.count('\n') // 40)
            log_info(f"Estimated {page_count} pages from DOCX")
            return text, page_count
        else:
            log_error("Extracted empty text from DOCX file")
            return "Document appears to be empty or contains only images.", 1
    except Exception as e:
        log_error(f"Error extracting text from DOCX: {str(e)}")
        return f"Error extracting text: {str(e)}", 1

def extract_from_text(text_path):
    """Extract text from a plain text file"""
    log_info(f"Extracting text from text file: {text_path}")

    try:
        with open(text_path, 'r', encoding='utf-8', errors='replace') as file:
            text = file.read()

        # Count newlines to estimate pages
        page_count = max(1, text.count('\n') // 40)
        log_info(f"Estimated {page_count} pages from text file")

        return text, page_count
    except Exception as e:
        log_error(f"Error extracting text from text file: {str(e)}")
        return f"Error reading text file: {str(e)}", 1

def extract_from_excel(file_path):
    """Extract text from an Excel file (XLSX/XLS)"""
    log_info(f"Extracting text from Excel file: {file_path}")

    try:
        # Try to use openpyxl for XLSX files
        try:
            import openpyxl
            log_info("Using openpyxl for Excel extraction")

            workbook = openpyxl.load_workbook(file_path, data_only=True)
            text_parts = []

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_text = f"Sheet: {sheet_name}\n"

                # Extract text from all cells
                for row in sheet.iter_rows():
                    row_text = []
                    for cell in row:
                        if cell.value is not None:
                            row_text.append(str(cell.value))
                    if row_text:
                        sheet_text += "\t".join(row_text) + "\n"

                text_parts.append(sheet_text)

            # Join all text parts
            extracted_text = "\n\n".join(text_parts)

            # Estimate page count based on content length
            page_count = max(1, len(extracted_text) // 2000)
            log_info(f"Extracted text from {len(workbook.sheetnames)} sheets, estimated {page_count} pages")

            return extracted_text, page_count

        except ImportError:
            # Final fallback - try to read as CSV-like format
            log_info("openpyxl not available, trying xlrd for XLS files")
            try:
                # For XLS files, try using xlrd
                import xlrd

                workbook = xlrd.open_workbook(file_path)
                text_parts = []

                for sheet_idx in range(workbook.nsheets):
                    sheet = workbook.sheet_by_index(sheet_idx)
                    sheet_text = f"Sheet: {sheet.name}\n"

                    for row_idx in range(sheet.nrows):
                        row_values = []
                        for col_idx in range(sheet.ncols):
                            cell_value = sheet.cell_value(row_idx, col_idx)
                            if cell_value:
                                row_values.append(str(cell_value))
                        if row_values:
                            sheet_text += "\t".join(row_values) + "\n"

                    text_parts.append(sheet_text)

                extracted_text = "\n\n".join(text_parts)
                page_count = max(1, len(extracted_text) // 2000)

                return extracted_text, page_count

            except ImportError:
                log_error("No Excel libraries available (openpyxl, xlrd)")
                return "Excel file detected but no suitable library available for extraction. Please install openpyxl.", 1

    except Exception as e:
        log_error(f"Error extracting text from Excel: {str(e)}")
        return f"Error extracting Excel content: {str(e)}", 1

def extract_from_pptx(file_path):
    """Extract text from a PowerPoint file (PPTX/PPT)"""
    log_info(f"Extracting text from PowerPoint file: {file_path}")
    
    try:
        # Try to use python-pptx first
        try:
            from pptx import Presentation
            log_info("Using python-pptx for extraction")
            
            prs = Presentation(file_path)
            
            # Extract text from all slides
            text_parts = []
            for i, slide in enumerate(prs.slides):
                slide_text = f"Slide {i+1}:\n"
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        slide_text += shape.text + "\n"
                text_parts.append(slide_text)
            
            # Join all text parts
            extracted_text = "\n\n".join(text_parts)
            
            # Get page count from number of slides
            page_count = len(prs.slides)
            log_info(f"Extracted text from {page_count} slides")
            
            return extracted_text, page_count
            
        except (ImportError, Exception) as e:
            # Fallback to plain text extraction if python-pptx fails
            log_error(f"Error using python-pptx: {str(e)}")
            log_info("Falling back to alternative extraction method")
            
            # Create a temporary directory
            with tempfile.TemporaryDirectory() as temp_dir:
                # Try using textract if available
                try:
                    import textract
                    log_info("Using textract for PowerPoint extraction")
                    extracted_text = textract.process(file_path).decode('utf-8')
                    
                    # Estimate page count
                    page_count = max(1, len(extracted_text) // 2000)
                    return extracted_text, page_count
                    
                except ImportError:
                    log_info("Textract not available, trying alternative methods")
                    
                    # Try to convert PPTX to text using LibreOffice or other tools if available
                    try:
                        # For Linux/macOS with LibreOffice
                        if os.name != 'nt':  # Not Windows
                            output_file = os.path.join(temp_dir, "output.txt")
                            subprocess.run(['soffice', '--headless', '--convert-to', 'txt', '--outdir', temp_dir, file_path], 
                                           check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                            txt_file = os.path.join(temp_dir, os.path.splitext(os.path.basename(file_path))[0] + '.txt')
                            
                            if os.path.exists(txt_file):
                                with open(txt_file, 'r', encoding='utf-8', errors='replace') as f:
                                    extracted_text = f.read()
                                page_count = max(1, extracted_text.count('\n') // 40)
                                return extracted_text, page_count
                        else:
                            # For Windows
                            # Extract with PowerShell if available (Windows)
                            ps_script = f'''
                            $ppt = New-Object -ComObject PowerPoint.Application
                            $ppt.Visible = $false
                            $presentation = $ppt.Presentations.Open("{file_path}")
                            $text = ""
                            foreach ($slide in $presentation.Slides) {{
                                $text += "Slide $($slide.SlideIndex):`n"
                                foreach ($shape in $slide.Shapes) {{
                                    if ($shape.HasTextFrame) {{
                                        $text += $shape.TextFrame.TextRange.Text + "`n"
                                    }}
                                }}
                                $text += "`n"
                            }}
                            $presentation.Close()
                            $ppt.Quit()
                            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($presentation) | Out-Null
                            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($ppt) | Out-Null
                            Write-Output $text
                            '''
                            
                            try:
                                log_info("Trying PowerShell extraction for PowerPoint")
                                ps_output = subprocess.run(['powershell', '-Command', ps_script], 
                                                        check=True, capture_output=True, text=True)
                                extracted_text = ps_output.stdout
                                slide_count = extracted_text.count('Slide ')
                                log_info(f"Extracted text from PowerShell with {slide_count} slides detected")
                                return extracted_text, max(1, slide_count)
                            except Exception as ps_err:
                                log_error(f"PowerShell extraction failed: {str(ps_err)}")
                        
                    except Exception as convert_err:
                        log_error(f"Conversion failed: {str(convert_err)}")
            
            # Last resort: return a simple error message
            log_error("All PowerPoint extraction methods failed")
            return "Could not extract text from PowerPoint presentation. The file may be corrupted or contain only images.", 1
    except Exception as e:
        log_error(f"Error extracting text from PowerPoint: {str(e)}")
        return f"Error extracting text: {str(e)}", 1

# Main file processing function with enhanced error handling and OCR fallback
def process_file(file_path):
    """Process a file and extract text with appropriate method"""
    try:
        start_time = datetime.datetime.now()
        log_info(f"Processing file: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Determine file type and extract text accordingly
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # Extract text based on file type
        if file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']:
            log_info(f"Processing image file with OCR: {file_path}")
            extracted_text, page_count = extract_from_image(file_path)
            is_image = True
        elif file_ext == '.pdf':
            log_info(f"Processing PDF file: {file_path}")
            extracted_text, page_count = extract_text_from_pdf(file_path)
            is_image = False
        elif file_ext in ['.docx', '.doc']:
            log_info(f"Processing Word document: {file_path}")
            extracted_text, page_count = extract_from_docx(file_path)
            is_image = False
        elif file_ext in ['.xlsx', '.xls']:
            log_info(f"Processing Excel spreadsheet: {file_path}")
            extracted_text, page_count = extract_from_excel(file_path)
            is_image = False
        elif file_ext in ['.pptx', '.ppt']:
            log_info(f"Processing PowerPoint presentation: {file_path}")
            extracted_text, page_count = extract_from_pptx(file_path)
            is_image = False
        elif file_ext in ['.txt', '.csv', '.md', '.json', '.xml', '.html', '.htm']:
            log_info(f"Processing text file: {file_path}")
            extracted_text, page_count = extract_from_text(file_path)
            is_image = False
        else:
            # Try to extract as text for unknown file types
            log_info(f"Unknown file type, trying as text: {file_path}")
            try:
                extracted_text, page_count = extract_from_text(file_path)
                is_image = False
            except:
                raise ValueError(f"Unsupported file type: {file_ext}")
        
        # Calculate suggested question count
        question_count = calculate_question_count(page_count, is_image)
        
        # Record processing time
        processing_time = (datetime.datetime.now() - start_time).total_seconds()
        
        # Create result object with all extracted text
        result = {
            "text": extracted_text,
            "pageCount": page_count,
            "questionCount": question_count,
            "processingTime": processing_time,
            "fileType": file_ext[1:] if file_ext.startswith('.') else file_ext,
            "isImage": is_image,
            "documentType": get_document_type_description(page_count)
        }
        
        # Output the result as JSON
        output_essential(json.dumps(result))
        
        return 0
    except Exception as e:
        log_error(f"Error processing file: {str(e)}")
        traceback.print_exc()
        
        # Still output a JSON object with the error for proper handling
        error_result = {
            "error": str(e),
            "stack": traceback.format_exc()
        }
        output_essential(json.dumps(error_result))
        
        return 1

# Main function
if __name__ == "__main__":
    if len(sys.argv) < 2:
        log_error("No file path provided")
        print(json.dumps({"error": "No file path provided"}))
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    try:
        sys.exit(process_file(file_path))
    except Exception as e:
        log_error(f"Unhandled exception: {str(e)}")
        print(json.dumps({"error": str(e), "type": "unhandled_exception"}))
        sys.exit(1) 