/tf const { ipcMain, dialog, shell, Notification } = require('electron');
const path = require('path');
const fs = require('fs');

// Import existing services (adapted for desktop)
const apiService = require('./services/apiService');
const fileService = require('./services/fileService');
const database = require('./database/database');
const logger = require('./utils/logger');

class IPCHandlers {
    constructor() {
        this.setupHandlers();
    }

    setupHandlers() {
        // File operations
        ipcMain.handle('select-file', async (event, options = {}) => {
            try {
                const defaultFilters = [
                    { name: 'All Supported', extensions: ['pdf', 'docx', 'doc', 'txt', 'jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'Documents', extensions: ['pdf', 'docx', 'doc', 'txt'] },
                    { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'PDF Files', extensions: ['pdf'] },
                    { name: 'Word Documents', extensions: ['docx', 'doc'] },
                    { name: 'Text Files', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ];

                const result = await dialog.showOpenDialog({
                    properties: ['openFile'],
                    filters: options.filters || defaultFilters,
                    title: options.title || 'Select a file to process'
                });

                if (!result.canceled && result.filePaths.length > 0) {
                    const filePath = result.filePaths[0];
                    logger.info(`File selected: ${filePath}`);
                    return { success: true, filePath: filePath };
                }

                return { success: false, error: 'No file selected' };
            } catch (error) {
                logger.error('Error in select-file:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('process-file', async (event, fileData) => {
            try {
                // Handle different types of file data
                let filePath;

                if (typeof fileData === 'string') {
                    // Direct file path
                    filePath = fileData;
                } else if (fileData && fileData.path) {
                    // File object with path property
                    filePath = fileData.path;
                } else if (fileData && fileData.filePath) {
                    // File object with filePath property
                    filePath = fileData.filePath;
                } else {
                    throw new Error('Invalid file data provided');
                }

                logger.info(`Processing file: ${filePath}`);

                if (!filePath || filePath === 'undefined') {
                    throw new Error('File path is undefined or invalid');
                }

                // Check if file exists
                if (!fs.existsSync(filePath)) {
                    throw new Error(`File not found: ${filePath}`);
                }

                // Use existing file service to extract text
                const result = await fileService.extractTextFromDocument(filePath);

                if (result && result.text) {
                    return {
                        success: true,
                        text: result.text,
                        pageCount: result.pageCount || 1,
                        questionCount: result.questionCount || 5
                    };
                } else {
                    throw new Error('Failed to extract text from file');
                }
            } catch (error) {
                logger.error('Error processing file:', error.message);
                return { success: false, error: error.message };
            }
        });

        // Question generation
        ipcMain.handle('generate-questions', async (event, content, type, count, preferredModel = 'auto') => {
            try {
                logger.info(`🎯 IPC Handler: Generating ${count} ${type} questions`);
                logger.info(`📊 IPC Handler: Content length: ${content?.length || 0} characters`);
                logger.info(`🤖 IPC Handler: Received preferred model: "${preferredModel}" (type: ${typeof preferredModel})`);

                // DEBUG: Log the exact count received
                logger.info(`🔢 IPC Handler: QUESTION COUNT DEBUG - Received count: ${count} (type: ${typeof count})`);

                // If frontend sent 'auto', check if we have a saved preference in settings
                if (preferredModel === 'auto') {
                    try {
                        const settingsPath = path.join(__dirname, 'data', 'settings.json');
                        if (fs.existsSync(settingsPath)) {
                            const settingsData = fs.readFileSync(settingsPath, 'utf8');
                            const savedSettings = JSON.parse(settingsData);
                            if (savedSettings.preferredModel && savedSettings.preferredModel !== 'auto') {
                                preferredModel = savedSettings.preferredModel;
                                logger.info(`IPC Handler: Overriding with saved preference: "${preferredModel}"`);
                            }
                        }
                    } catch (settingsError) {
                        logger.warn(`Could not load settings in IPC handler: ${settingsError.message}`);
                    }
                }

                // Create a mock context object similar to Telegram context
                const mockContext = {
                    from: { id: 'desktop-user' },
                    chat: { id: 'desktop-chat' },
                    reply: (message) => {
                        logger.info('Bot reply:', message);
                        return Promise.resolve();
                    },
                    telegram: {
                        editMessageText: () => Promise.resolve()
                    }
                };

                // Use existing API service to generate questions
                const questions = await apiService.generateQuestionsFromAPI(
                    content,
                    type,
                    count,
                    2, // retries
                    false, // isScanned
                    'desktop-user',
                    'text', // contentType
                    preferredModel // pass the preferred model
                );

                if (questions && questions.length > 0) {
                    logger.success(`✅ IPC Handler: Generated ${questions.length} questions (requested: ${count})`);
                    if (questions.length !== count) {
                        logger.warn(`⚠️  IPC Handler: Question count mismatch! Requested: ${count}, Generated: ${questions.length}`);
                    }
                    return questions;
                } else {
                    throw new Error('No questions were generated');
                }
            } catch (error) {
                logger.error('Error generating questions:', error);
                throw error;
            }
        });

        // Write file content (for text files like JSON)
        ipcMain.handle('write-file', async (event, filePath, content) => {
            try {
                const fs = require('fs').promises;
                await fs.writeFile(filePath, content, 'utf8');
                logger.success(`Text file written to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error writing file:', error);
                return { success: false, error: error.message };
            }
        });

        // Mind Map generation
        ipcMain.handle('generate-mind-map', async (event, content, isScanned = false) => {
            try {
                logger.info(`🧠 IPC Handler: Generating mind map`);
                logger.info(`📊 IPC Handler: Content length: ${content?.length || 0} characters`);

                if (!content || content.trim().length === 0) {
                    throw new Error('Content is required for mind map generation');
                }

                if (content.length < 50) {
                    throw new Error('Content is too short for mind map generation (minimum 50 characters)');
                }

                // Use existing API service to generate mind map
                const mindMapData = await apiService.generateMindMapFromAPI(
                    content,
                    isScanned,
                    'desktop-user'
                );

                if (mindMapData && mindMapData.title) {
                    logger.success(`✅ IPC Handler: Generated mind map with title: "${mindMapData.title}"`);
                    return mindMapData;
                } else {
                    throw new Error('No mind map data was generated');
                }
            } catch (error) {
                logger.error('Error generating mind map:', error);
                throw error;
            }
        });

        // Quiz operations
        ipcMain.handle('start-quiz', async (event, questions) => {
            try {
                // Initialize quiz session in database if needed
                const quizSession = {
                    id: Date.now().toString(),
                    questions: questions,
                    startTime: new Date().toISOString(),
                    status: 'active'
                };

                // Store session (you might want to implement this in database)
                logger.info('Quiz session started');
                return { success: true, sessionId: quizSession.id };
            } catch (error) {
                logger.error('Error starting quiz:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('submit-answer', async (event, questionIndex, answer) => {
            try {
                // Process answer submission
                logger.info(`Answer submitted for question ${questionIndex}: ${answer}`);
                return { success: true };
            } catch (error) {
                logger.error('Error submitting answer:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-results', async (event) => {
            try {
                // Get quiz results from database
                return { success: true, results: {} };
            } catch (error) {
                logger.error('Error getting quiz results:', error);
                return { success: false, error: error.message };
            }
        });

        // Database operations
        ipcMain.handle('save-quiz-session', async (event, session) => {
            try {
                logger.info('Saving quiz session:', JSON.stringify(session, null, 2));

                // Save quiz session to database
                const sessionData = {
                    timestamp: session.timestamp,
                    question_type: session.question_type || session.questionType, // Handle both formats
                    score_correct: session.score.correct,
                    score_total: session.score.total,
                    duration: session.duration,
                    answers: JSON.stringify(session.answers),
                    questions: JSON.stringify(session.questions)
                };

                logger.info('Processed session data:', JSON.stringify(sessionData, null, 2));

                // Use existing database methods with sqlite3 API
                const db = database.db();

                // Ensure table exists first
                await new Promise((resolve, reject) => {
                    db.exec(`
                        CREATE TABLE IF NOT EXISTS quiz_sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            timestamp TEXT NOT NULL,
                            question_type TEXT NOT NULL,
                            score_correct INTEGER NOT NULL,
                            score_total INTEGER NOT NULL,
                            duration INTEGER NOT NULL,
                            answers TEXT NOT NULL,
                            questions TEXT NOT NULL
                        )
                    `, (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                // Insert session data using sqlite3 API
                await new Promise((resolve, reject) => {
                    db.run(`
                        INSERT INTO quiz_sessions
                        (timestamp, question_type, score_correct, score_total, duration, answers, questions)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    `, [
                        sessionData.timestamp,
                        sessionData.question_type,
                        sessionData.score_correct,
                        sessionData.score_total,
                        sessionData.duration,
                        sessionData.answers,
                        sessionData.questions
                    ], function(err) {
                        if (err) {
                            logger.error('Database insert error:', err);
                            reject(err);
                        } else {
                            logger.info(`Quiz session saved with ID: ${this.lastID}`);
                            resolve();
                        }
                    });
                });

                logger.success('Quiz session saved to database');
                return { success: true };
            } catch (error) {
                logger.error('Error saving quiz session:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-history', async (event) => {
            try {
                const db = database.db();

                // Ensure table exists first
                await new Promise((resolve, reject) => {
                    db.exec(`
                        CREATE TABLE IF NOT EXISTS quiz_sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            timestamp TEXT NOT NULL,
                            question_type TEXT NOT NULL,
                            score_correct INTEGER NOT NULL,
                            score_total INTEGER NOT NULL,
                            duration INTEGER NOT NULL,
                            answers TEXT NOT NULL,
                            questions TEXT NOT NULL
                        )
                    `, (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                // Get sessions using sqlite3 API
                const sessions = await new Promise((resolve, reject) => {
                    db.all(`
                        SELECT * FROM quiz_sessions
                        ORDER BY timestamp DESC
                        LIMIT 50
                    `, [], (err, rows) => {
                        if (err) {
                            logger.error('Database query error:', err);
                            reject(err);
                        } else {
                            resolve(rows || []);
                        }
                    });
                });

                // Parse JSON fields with null safety
                const parsedSessions = sessions.map(session => {
                    if (!session) return null;

                    try {
                        return {
                            ...session,
                            answers: session.answers ? JSON.parse(session.answers) : [],
                            questions: session.questions ? JSON.parse(session.questions) : []
                        };
                    } catch (parseError) {
                        logger.error('Error parsing session data:', parseError);
                        return {
                            ...session,
                            answers: [],
                            questions: []
                        };
                    }
                }).filter(session => session !== null);

                logger.info(`Retrieved ${parsedSessions.length} quiz sessions`);
                return { success: true, sessions: parsedSessions };
            } catch (error) {
                logger.error('Error getting quiz history:', error);
                return { success: false, error: error.message, sessions: [] };
            }
        });

        ipcMain.handle('get-statistics', async (event) => {
            try {
                const db = database.db();

                // Ensure table exists first
                await new Promise((resolve, reject) => {
                    db.exec(`
                        CREATE TABLE IF NOT EXISTS quiz_sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            timestamp TEXT NOT NULL,
                            question_type TEXT NOT NULL,
                            score_correct INTEGER NOT NULL,
                            score_total INTEGER NOT NULL,
                            duration INTEGER NOT NULL,
                            answers TEXT NOT NULL,
                            questions TEXT NOT NULL
                        )
                    `, (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                // Get basic statistics using sqlite3 API
                const totalQuizzes = await new Promise((resolve, reject) => {
                    db.get('SELECT COUNT(*) as count FROM quiz_sessions', [], (err, row) => {
                        if (err) reject(err);
                        else resolve(row || { count: 0 });
                    });
                });

                const avgScore = await new Promise((resolve, reject) => {
                    db.get('SELECT AVG(CAST(score_correct AS FLOAT) / CAST(score_total AS FLOAT) * 100) as avg FROM quiz_sessions WHERE score_total > 0', [], (err, row) => {
                        if (err) reject(err);
                        else resolve(row || { avg: 0 });
                    });
                });

                const totalQuestions = await new Promise((resolve, reject) => {
                    db.get('SELECT SUM(score_total) as total FROM quiz_sessions', [], (err, row) => {
                        if (err) reject(err);
                        else resolve(row || { total: 0 });
                    });
                });

                const stats = {
                    totalQuizzes: totalQuizzes.count || 0,
                    averageScore: Math.round(avgScore.avg || 0),
                    totalQuestions: totalQuestions.total || 0,
                    lastQuizDate: null
                };

                // Get last quiz date
                const lastQuiz = await new Promise((resolve, reject) => {
                    db.get('SELECT timestamp FROM quiz_sessions ORDER BY timestamp DESC LIMIT 1', [], (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    });
                });

                if (lastQuiz) {
                    stats.lastQuizDate = lastQuiz.timestamp;
                }

                logger.info(`Retrieved statistics: ${JSON.stringify(stats)}`);
                return { success: true, statistics: stats };
            } catch (error) {
                logger.error('Error getting statistics:', error);
                return { success: false, error: error.message, statistics: null };
            }
        });

        // Clear all quiz history
        ipcMain.handle('clear-quiz-history', async (event) => {
            try {
                const db = database.db();

                // Delete all records from quiz_sessions table
                await new Promise((resolve, reject) => {
                    db.run('DELETE FROM quiz_sessions', [], function(err) {
                        if (err) {
                            logger.error('Database delete error:', err);
                            reject(err);
                        } else {
                            logger.info(`Cleared ${this.changes} quiz sessions from history`);
                            resolve();
                        }
                    });
                });

                return { success: true, message: 'Quiz history cleared successfully' };
            } catch (error) {
                logger.error('Error clearing quiz history:', error);
                return { success: false, error: error.message };
            }
        });

        // Saved Quiz operations - Using file-based storage
        ipcMain.handle('save-saved-quiz', async (event, savedQuiz) => {
            try {
                const savedQuizzesService = require('./services/savedQuizzesService');

                logger.info(`Saving quiz: ${savedQuiz.title} (ID: ${savedQuiz.id})`);
                console.log('Saved quiz data:', {
                    id: savedQuiz.id,
                    title: savedQuiz.title,
                    questionType: savedQuiz.questionType,
                    questionsCount: savedQuiz.questions?.length || 0,
                    createdAt: savedQuiz.createdAt,
                    source: savedQuiz.source
                });

                const result = savedQuizzesService.addSavedQuiz(savedQuiz);

                if (result.success) {
                    logger.success(`Successfully saved quiz: ${savedQuiz.title}`);
                } else {
                    logger.error(`Failed to save quiz: ${result.error}`);
                }

                return result;
            } catch (error) {
                logger.error('Error saving quiz for later:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-saved-quizzes', async (event) => {
            try {
                const savedQuizzesService = require('./services/savedQuizzesService');

                logger.info('Getting saved quizzes from file...');
                const result = savedQuizzesService.getSavedQuizzes();

                console.log(`Found ${result.savedQuizzes?.length || 0} saved quizzes in file`);

                if (result.savedQuizzes && result.savedQuizzes.length > 0) {
                    console.log('First saved quiz:', result.savedQuizzes[0]);
                }

                console.log(`Returning ${result.savedQuizzes?.length || 0} saved quizzes`);
                return result;
            } catch (error) {
                logger.error('Error getting saved quizzes:', error);
                return { success: false, error: error.message, savedQuizzes: [] };
            }
        });

        ipcMain.handle('delete-saved-quiz', async (event, quizId) => {
            try {
                const savedQuizzesService = require('./services/savedQuizzesService');

                logger.info(`Deleting saved quiz: ${quizId}`);
                const result = savedQuizzesService.deleteSavedQuiz(quizId);

                if (result.success) {
                    logger.success(`Successfully deleted saved quiz: ${quizId}`);
                } else {
                    logger.error(`Failed to delete saved quiz: ${result.error}`);
                }

                return result;
            } catch (error) {
                logger.error('Error deleting saved quiz:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('clear-all-saved-quizzes', async (event) => {
            try {
                const savedQuizzesService = require('./services/savedQuizzesService');

                logger.info('Clearing all saved quizzes...');
                const result = savedQuizzesService.clearAllSavedQuizzes();

                if (result.success) {
                    logger.success('Successfully cleared all saved quizzes');
                } else {
                    logger.error(`Failed to clear saved quizzes: ${result.error}`);
                }

                return result;
            } catch (error) {
                logger.error('Error clearing all saved quizzes:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('fix-islamic-dates', async (event) => {
            try {
                const savedQuizzesService = require('./services/savedQuizzesService');

                logger.info('Fixing Islamic dates in saved quiz titles...');
                const result = savedQuizzesService.fixIslamicDatesInTitles();

                if (result.success) {
                    logger.success(`Successfully fixed ${result.updatedCount} quiz titles`);
                } else {
                    logger.error(`Failed to fix Islamic dates: ${result.error}`);
                }

                return result;
            } catch (error) {
                logger.error('Error fixing Islamic dates:', error);
                return { success: false, error: error.message };
            }
        });

        // API Key Management
        ipcMain.handle('get-api-key-info', async (event) => {
            try {
                const currentKey = process.env.API_KEY;

                if (!currentKey) {
                    return {
                        success: false,
                        error: 'No API key configured',
                        hasKey: false
                    };
                }

                // Mask the key for security (show first 15 and last 4 characters)
                const maskedKey = currentKey.length > 20
                    ? `${currentKey.substring(0, 15)}...${currentKey.substring(currentKey.length - 4)}`
                    : '***masked***';

                return {
                    success: true,
                    hasKey: true,
                    maskedKey: maskedKey,
                    keyLength: currentKey.length,
                    isValidFormat: currentKey.startsWith('sk-or-v1-') && currentKey.length === 73,
                    provider: 'OpenRouter.ai'
                };
            } catch (error) {
                logger.error('Error getting API key info:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('update-api-key', async (event, newApiKey) => {
            try {
                // Validate the new API key format
                if (!newApiKey || typeof newApiKey !== 'string') {
                    return { success: false, error: 'Invalid API key format' };
                }

                if (!newApiKey.startsWith('sk-or-v1-')) {
                    return { success: false, error: 'API key must start with "sk-or-v1-"' };
                }

                if (newApiKey.length !== 73) {
                    return { success: false, error: 'API key must be exactly 73 characters long' };
                }

                logger.info('Updating API key in both database and environment...');

                // Step 1: Update the database (primary source)
                const apiService = require('./services/apiService');

                try {
                    await apiService.addApiKey(newApiKey);
                    logger.info('✅ API key updated in database successfully');
                } catch (dbError) {
                    logger.error('❌ Failed to update database:', dbError.message);
                    return { success: false, error: `Database update failed: ${dbError.message}` };
                }

                // Step 2: Update the .env file (backup/sync)
                const envPath = path.join(__dirname, '..', '.env');
                let envContent = '';

                try {
                    if (fs.existsSync(envPath)) {
                        envContent = fs.readFileSync(envPath, 'utf8');
                    }
                } catch (readError) {
                    logger.warn('Could not read .env file, creating new one');
                }

                // Update or add the API_KEY line
                const lines = envContent.split('\n');
                let keyUpdated = false;

                for (let i = 0; i < lines.length; i++) {
                    if (lines[i].startsWith('API_KEY=')) {
                        lines[i] = `API_KEY=${newApiKey}`;
                        keyUpdated = true;
                        break;
                    }
                }

                if (!keyUpdated) {
                    lines.push(`API_KEY=${newApiKey}`);
                }

                // Write back to .env file
                try {
                    fs.writeFileSync(envPath, lines.join('\n'), 'utf8');
                    logger.info('✅ API key updated in .env file successfully');
                } catch (writeError) {
                    logger.warn('⚠️  Could not update .env file:', writeError.message);
                }

                // Step 3: Update the environment variable for current session
                process.env.API_KEY = newApiKey;

                logger.info('🎉 API key updated successfully in all locations');
                return {
                    success: true,
                    message: 'API key updated successfully! Database and .env file are now synchronized.'
                };

            } catch (error) {
                logger.error('Error updating API key:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('test-api-key', async (event, testKey = null) => {
            try {
                const keyToTest = testKey || process.env.API_KEY;

                if (!keyToTest) {
                    return { success: false, error: 'No API key to test' };
                }

                // Test the API key with a simple request
                const apiService = require('./services/apiService');

                logger.info('Testing API key with simple request...');

                const testResult = await apiService.generateQuestionsFromAPI(
                    "Test content for API key validation.",
                    "TF",
                    1,
                    0, // retries
                    false, // isScanned
                    'api-key-test',
                    'text',
                    null // Use any available model
                );

                if (testResult && Array.isArray(testResult) && testResult.length > 0) {
                    logger.info('API key test successful');
                    return {
                        success: true,
                        message: 'API key is working correctly',
                        questionsGenerated: testResult.length
                    };
                } else {
                    return {
                        success: false,
                        error: 'API key test failed - no questions generated'
                    };
                }

            } catch (error) {
                logger.error('API key test failed:', error);
                return {
                    success: false,
                    error: `API key test failed: ${error.message}`
                };
            }
        });

        // Settings
        ipcMain.handle('get-settings', async (event) => {
            try {
                const settingsPath = path.join(__dirname, 'data', 'settings.json');

                // Default settings
                const defaultSettings = {
                    questionsPerPage: 5,  // Questions per page for multi-page documents
                    imageQuestionsCount: 5,  // Total questions for images (changed from 15 to 5)
                    questionType: 'MCQ',
                    autoSave: true,
                    notifications: true
                };

                try {
                    // Try to read existing settings
                    const fs = require('fs');
                    if (fs.existsSync(settingsPath)) {
                        const settingsData = fs.readFileSync(settingsPath, 'utf8');
                        const savedSettings = JSON.parse(settingsData);

                        // Merge with defaults to ensure all properties exist
                        const settings = { ...defaultSettings, ...savedSettings };
                        logger.info('Loaded settings from file:', settings);
                        return { success: true, settings };
                    }
                } catch (readError) {
                    logger.warn('Could not read settings file, using defaults:', readError.message);
                }

                // Return defaults if file doesn't exist or can't be read
                logger.info('Using default settings');
                return { success: true, settings: defaultSettings };
            } catch (error) {
                logger.error('Error getting settings:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('save-settings', async (event, newSettings) => {
            try {
                const settingsPath = path.join(__dirname, 'data', 'settings.json');
                const fs = require('fs');

                // Ensure data directory exists
                const dataDir = path.dirname(settingsPath);
                if (!fs.existsSync(dataDir)) {
                    fs.mkdirSync(dataDir, { recursive: true });
                }

                let currentSettings = {};

                // Load existing settings if they exist
                if (fs.existsSync(settingsPath)) {
                    try {
                        const settingsData = fs.readFileSync(settingsPath, 'utf8');
                        currentSettings = JSON.parse(settingsData);
                    } catch (readError) {
                        logger.warn('Could not read existing settings, starting fresh:', readError.message);
                    }
                }

                // Merge new settings with existing ones
                const updatedSettings = { ...currentSettings, ...newSettings };

                // Save to file
                fs.writeFileSync(settingsPath, JSON.stringify(updatedSettings, null, 2), 'utf8');

                logger.info('Settings saved to file:', JSON.stringify(updatedSettings, null, 2));
                return { success: true };
            } catch (error) {
                logger.error('Error saving settings:', error);
                return { success: false, error: error.message };
            }
        });

        // Feedback
        ipcMain.handle('submit-feedback', async (event, feedback) => {
            try {
                // Save feedback using existing feedback service
                const feedbackService = require('./services/feedbackService');
                const feedbackData = {
                    userId: 'desktop-user',
                    username: 'Desktop User',
                    rating: feedback.rating,
                    suggestion: feedback.suggestion,
                    quizType: feedback.quizType || 'Unknown',
                    score: feedback.score || 0
                };

                const saved = await feedbackService.saveFeedback(feedbackData);

                if (saved) {
                    logger.success('Feedback saved successfully');
                    return { success: true };
                } else {
                    throw new Error('Failed to save feedback');
                }
            } catch (error) {
                logger.error('Error submitting feedback:', error);
                return { success: false, error: error.message };
            }
        });

        // Utility functions
        ipcMain.handle('show-notification', async (event, title, body) => {
            try {
                if (Notification.isSupported()) {
                    new Notification({ title, body }).show();
                }
                return { success: true };
            } catch (error) {
                logger.error('Error showing notification:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('open-external', async (event, url) => {
            try {
                await shell.openExternal(url);
                return { success: true };
            } catch (error) {
                logger.error('Error opening external URL:', error);
                return { success: false, error: error.message };
            }
        });
        ipcMain.handle('show-message-box', async (event, options) => {
            try {
                const { dialog, BrowserWindow } = require('electron');
                const focusedWindow = BrowserWindow.getFocusedWindow();
                const result = await dialog.showMessageBox(focusedWindow, options);
                return result;
            } catch (error) {
                logger.error('Error showing message box:', error);
                return { success: false, error: error.message };
            }
        });

        // Dialog functions
        ipcMain.handle('show-save-dialog', async (event, options) => {
            try {
                const { dialog, BrowserWindow } = require('electron');
                const focusedWindow = BrowserWindow.getFocusedWindow();
                const result = await dialog.showSaveDialog(focusedWindow, options);
                return result;
            } catch (error) {
                logger.error('Error showing save dialog:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('show-open-dialog', async (event, options) => {
            try {
                const { dialog, BrowserWindow } = require('electron');
                const focusedWindow = BrowserWindow.getFocusedWindow();
                const result = await dialog.showOpenDialog(focusedWindow, options);
                return result;
            } catch (error) {
                logger.error('Error showing open dialog:', error);
                return { success: false, error: error.message };
            }
        });

        // File save dialog
        ipcMain.handle('save-file', async (event, options) => {
            try {
                const { dialog } = require('electron');
                const result = await dialog.showSaveDialog(options);

                if (result.canceled) {
                    return { success: false, canceled: true };
                } else {
                    return { success: true, filePath: result.filePath };
                }
            } catch (error) {
                logger.error('Error showing save dialog:', error);
                return { success: false, error: error.message };
            }
        });

        // PDF generation and save
        ipcMain.handle('save-pdf', async (event, filePath, htmlContent) => {
            try {
                const fs = require('fs').promises;
                const { BrowserWindow } = require('electron');

                // Create a hidden browser window for PDF generation
                const pdfWindow = new BrowserWindow({
                    show: false,
                    webPreferences: {
                        nodeIntegration: false,
                        contextIsolation: true
                    }
                });

                // Load the HTML content
                await pdfWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

                // Generate PDF
                const pdfBuffer = await pdfWindow.webContents.printToPDF({
                    format: 'A4',
                    margin: {
                        top: 20,
                        right: 15,
                        bottom: 20,
                        left: 15
                    },
                    printBackground: true,
                    landscape: false
                });

                // Close the window
                pdfWindow.close();

                // Save PDF to file
                await fs.writeFile(filePath, pdfBuffer);

                logger.success(`PDF saved to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error generating PDF:', error);
                return { success: false, error: error.message };
            }
        });

        // Save file content
        ipcMain.handle('save-file-content', async (event, filePath, content) => {
            try {
                const fs = require('fs').promises;

                // Convert ArrayBuffer to Buffer if needed
                let buffer;
                if (content instanceof ArrayBuffer) {
                    buffer = Buffer.from(content);
                } else if (Buffer.isBuffer(content)) {
                    buffer = content;
                } else {
                    buffer = Buffer.from(content);
                }

                await fs.writeFile(filePath, buffer);
                logger.success(`File content saved to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error saving file content:', error);
                return { success: false, error: error.message };
            }
        });





        // Model Management
        ipcMain.handle('add-model', async (event, modelData) => {
            try {
                logger.info(`Adding new model: ${modelData.id}`);

                // Read current models configuration
                const modelsPath = path.join(__dirname, 'config', 'models.json');
                let models = [];

                // Ensure config directory exists
                const configDir = path.dirname(modelsPath);
                if (!fs.existsSync(configDir)) {
                    fs.mkdirSync(configDir, { recursive: true });
                }

                if (fs.existsSync(modelsPath)) {
                    const modelsData = fs.readFileSync(modelsPath, 'utf8');
                    models = JSON.parse(modelsData);
                }

                // Check if model already exists
                const existingModel = models.find(m => m.id === modelData.id);
                if (existingModel) {
                    return { success: false, error: 'Model already exists' };
                }

                // Add new model
                const newModel = {
                    id: modelData.id,
                    name: modelData.name,
                    description: modelData.description || '',
                    added: new Date().toISOString(),
                    custom: true
                };

                models.push(newModel);

                // Save updated models
                fs.writeFileSync(modelsPath, JSON.stringify(models, null, 2));

                logger.info(`Model ${modelData.id} added successfully`);
                return { success: true };

            } catch (error) {
                logger.error('Error adding model:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('remove-model', async (event, modelId) => {
            try {
                logger.info(`Removing model: ${modelId}`);

                // No restrictions - allow removal of ANY model

                // Read current removed models list
                const removedModelsPath = path.join(__dirname, 'config', 'removed-models.json');
                let removedModels = [];

                // Ensure config directory exists
                const configDir = path.dirname(removedModelsPath);
                if (!fs.existsSync(configDir)) {
                    fs.mkdirSync(configDir, { recursive: true });
                }

                if (fs.existsSync(removedModelsPath)) {
                    const removedData = fs.readFileSync(removedModelsPath, 'utf8');
                    removedModels = JSON.parse(removedData);
                }

                // Add model to removed list if not already there
                if (!removedModels.includes(modelId)) {
                    removedModels.push(modelId);
                    fs.writeFileSync(removedModelsPath, JSON.stringify(removedModels, null, 2));
                }

                // Also remove from custom models if it exists there
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                if (fs.existsSync(customModelsPath)) {
                    const customModelsData = fs.readFileSync(customModelsPath, 'utf8');
                    let customModels = JSON.parse(customModelsData);

                    const modelIndex = customModels.findIndex(m => m.id === modelId);
                    if (modelIndex !== -1) {
                        customModels.splice(modelIndex, 1);
                        fs.writeFileSync(customModelsPath, JSON.stringify(customModels, null, 2));
                    }
                }

                logger.info(`Model ${modelId} removed successfully`);
                return { success: true };

            } catch (error) {
                logger.error('Error removing model:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-all-models', async (event) => {
            try {
                // Read custom models first
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                let customModels = [];

                if (fs.existsSync(customModelsPath)) {
                    const modelsData = fs.readFileSync(customModelsPath, 'utf8');
                    customModels = JSON.parse(modelsData);
                }

                // If custom models exist, use ONLY custom models (user preference)
                if (customModels.length > 0) {
                    logger.info(`Using ${customModels.length} custom models only`);
                    return { success: true, models: customModels };
                }

                // No fallback models - user must add models through the UI
                logger.info('No custom models found - user must add models through the UI');
                return { success: true, models: [] };

            } catch (error) {
                logger.error('Error getting models:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('check-model-availability', async (event, modelId) => {
            try {
                // Simple availability check - just return success for now
                // In a real implementation, you might ping the model API
                return {
                    success: true,
                    available: true,
                    status: 'available',
                    message: 'Model is available'
                };
            } catch (error) {
                logger.error('Error checking model availability:', error);
                return {
                    success: false,
                    available: false,
                    status: 'error',
                    message: error.message
                };
            }
        });

        // Model Testing
        ipcMain.handle('test-model', async (event, modelId, content, questionType, questionCount) => {
            try {
                logger.info(`Testing model: ${modelId}`);

                // Use the existing question generation logic with correct parameter order
                const questions = await apiService.generateQuestionsFromAPI(
                    content,           // text
                    questionType,      // type
                    questionCount,     // count
                    0,                 // retries
                    false,             // isScanned
                    'desktop-user',    // userId
                    'text',            // contentType
                    modelId            // preferredModel - force specific model
                );

                if (questions && questions.length > 0) {
                    logger.info(`Model ${modelId} test successful: ${questions.length} questions generated`);
                    return {
                        success: true,
                        questions: questions,
                        model: modelId
                    };
                } else {
                    logger.warn(`Model ${modelId} test failed: No questions generated`);
                    return {
                        success: false,
                        error: 'No questions generated',
                        model: modelId
                    };
                }

            } catch (error) {
                logger.error(`Error testing model ${modelId}:`, error);
                return {
                    success: false,
                    error: error.message,
                    model: modelId
                };
            }
        });

        // Run Model Simulation
        ipcMain.handle('run-model-simulation', async (event, testParams) => {
            try {
                logger.info('Starting automated model simulation...');

                const { content, questionType, questionCount } = testParams;

                // Get all available models from custom models only
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                let availableModels = [];

                if (fs.existsSync(customModelsPath)) {
                    const customModelsData = fs.readFileSync(customModelsPath, 'utf8');
                    const customModels = JSON.parse(customModelsData);
                    availableModels = customModels;
                }

                logger.info(`Testing ${availableModels.length} models...`);

                const results = [];

                // Test each model
                for (let i = 0; i < availableModels.length; i++) {
                    const model = availableModels[i];
                    logger.info(`Testing model ${i + 1}/${availableModels.length}: ${model.id}`);

                    const startTime = Date.now();

                    try {
                        // Test the model with correct parameter order
                        const questions = await apiService.generateQuestionsFromAPI(
                            content,           // text
                            questionType,      // type
                            questionCount,     // count
                            0,                 // retries
                            false,             // isScanned
                            'desktop-user',    // userId
                            'text',            // contentType
                            model.id           // preferredModel - force specific model
                        );

                        const endTime = Date.now();
                        const duration = endTime - startTime;

                        if (questions && questions.length > 0) {
                            logger.info(`✓ Model ${model.id} SUCCESS: ${questions.length} questions in ${duration}ms`);
                            results.push({
                                model: model,
                                success: true,
                                duration: duration,
                                questionsGenerated: questions.length,
                                questions: questions
                            });
                        } else {
                            logger.warn(`✗ Model ${model.id} FAILED: No questions generated`);
                            results.push({
                                model: model,
                                success: false,
                                duration: duration,
                                error: 'No questions generated'
                            });
                        }

                    } catch (error) {
                        const endTime = Date.now();
                        const duration = endTime - startTime;

                        logger.error(`✗ Model ${model.id} ERROR: ${error.message}`);
                        results.push({
                            model: model,
                            success: false,
                            duration: duration,
                            error: error.message
                        });
                    }

                    // Small delay between tests
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // Generate summary
                const successCount = results.filter(r => r.success).length;
                const totalCount = results.length;
                const avgDuration = Math.round(
                    results.reduce((sum, r) => sum + r.duration, 0) / results.length
                );

                logger.info(`Simulation complete: ${successCount}/${totalCount} models working, avg ${avgDuration}ms`);

                return {
                    success: true,
                    results: results,
                    summary: {
                        totalModels: totalCount,
                        successfulModels: successCount,
                        failedModels: totalCount - successCount,
                        averageDuration: avgDuration
                    }
                };

            } catch (error) {
                logger.error('Error running model simulation:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        });

        // True/False Logic Testing
        ipcMain.handle('test-tf-logic', async (event, testQuestions = null) => {
            try {
                logger.info('Testing True/False logic validation...');
                const apiService = require('./services/apiService');
                const result = apiService.testTrueFalseLogic(testQuestions);
                return { success: true, result };
            } catch (error) {
                logger.error('Error testing TF logic:', error);
                return { success: false, error: error.message };
            }
        });

        // Rate limit management
        ipcMain.handle('get-rate-limited-models', async (event) => {
            try {
                const rateLimitedModels = apiService.getRateLimitedModels();
                return { success: true, models: rateLimitedModels };
            } catch (error) {
                logger.error('Error getting rate-limited models:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('clear-model-rate-limit', async (event, modelId) => {
            try {
                apiService.clearModelRateLimit(modelId);
                logger.info(`Cleared rate limit for model: ${modelId}`);
                return { success: true };
            } catch (error) {
                logger.error('Error clearing model rate limit:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('clear-all-rate-limits', async (event) => {
            try {
                const clearedModels = apiService.clearAllRateLimits();
                logger.info(`Cleared rate limits for ${clearedModels.length} models`);
                return { success: true, clearedModels: clearedModels };
            } catch (error) {
                logger.error('Error clearing all rate limits:', error);
                return { success: false, error: error.message };
            }
        });

        // PDF Operations
        ipcMain.handle('merge-pdfs', async (event, outputPath, pdfBuffers, options = {}) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;

                logger.info(`Merging ${pdfBuffers.length} PDF files with options:`, {
                    quality: options.quality || 'balanced',
                    maintainBookmarks: options.maintainBookmarks || false,
                    addTableOfContents: options.addTableOfContents || false,
                    addPageNumbers: options.addPageNumbers || false
                });

                // Create a new PDF document
                const mergedPdf = await PDFLib.PDFDocument.create();
                let totalPages = 0;
                let tocPages = [];

                // Process each PDF buffer
                for (let i = 0; i < pdfBuffers.length; i++) {
                    const pdfBuffer = pdfBuffers[i];

                    try {
                        const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                        const pageCount = pdf.getPageCount();
                        const startPage = totalPages + 1; // +1 because page numbers are 1-based
                        totalPages += pageCount;

                        // Store TOC information
                        if (options.addTableOfContents && options.sourceFileNames && options.sourceFileNames[i]) {
                            tocPages.push({
                                fileName: options.sourceFileNames[i],
                                startPage: startPage,
                                endPage: totalPages,
                                pageCount: pageCount
                            });
                        }

                        // Copy all pages from this PDF
                        const pageIndices = Array.from({ length: pageCount }, (_, index) => index);
                        const copiedPages = await mergedPdf.copyPages(pdf, pageIndices);

                        // Add copied pages to merged PDF
                        copiedPages.forEach((page) => mergedPdf.addPage(page));

                        // Copy bookmarks if requested
                        if (options.maintainBookmarks) {
                            try {
                                // Note: pdf-lib doesn't have direct bookmark copying,
                                // but we can preserve document structure
                                const title = pdf.getTitle();
                                if (title && i === 0) {
                                    mergedPdf.setTitle(title);
                                }
                            } catch (bookmarkError) {
                                logger.warn(`Could not copy bookmarks from PDF ${i + 1}: ${bookmarkError.message}`);
                            }
                        }

                        logger.info(`Merged PDF ${i + 1}/${pdfBuffers.length} (${pageCount} pages)`);
                    } catch (pdfError) {
                        logger.error(`Error processing PDF ${i + 1}: ${pdfError.message}`);
                        throw new Error(`Failed to process PDF file ${i + 1}: ${pdfError.message}`);
                    }
                }

                // Add Table of Contents if requested
                if (options.addTableOfContents && tocPages.length > 0) {
                    try {
                        const tocPage = mergedPdf.insertPage(0); // Insert at the beginning
                        const { width, height } = tocPage.getSize();

                        // Add title
                        tocPage.drawText('Table of Contents', {
                            x: 50,
                            y: height - 80,
                            size: 20,
                            color: PDFLib.rgb(0, 0, 0)
                        });

                        // Add TOC entries
                        let yPosition = height - 120;
                        tocPages.forEach((entry, index) => {
                            const text = `${entry.fileName} ........................ Pages ${entry.startPage}-${entry.endPage}`;
                            tocPage.drawText(text, {
                                x: 50,
                                y: yPosition,
                                size: 12,
                                color: PDFLib.rgb(0, 0, 0)
                            });
                            yPosition -= 25;
                        });

                        totalPages += 1; // Account for TOC page
                        logger.info('Added Table of Contents page');
                    } catch (tocError) {
                        logger.warn('Could not create Table of Contents:', tocError.message);
                    }
                }

                // Add page numbers if requested
                if (options.addPageNumbers) {
                    try {
                        const pages = mergedPdf.getPages();
                        pages.forEach((page, index) => {
                            const { width, height } = page.getSize();
                            const pageNumber = index + 1;
                            page.drawText(`${pageNumber}`, {
                                x: width - 50,
                                y: 30,
                                size: 10,
                                color: PDFLib.rgb(0.5, 0.5, 0.5)
                            });
                        });
                        logger.info('Added page numbers to merged PDF');
                    } catch (pageNumberError) {
                        logger.warn('Could not add page numbers:', pageNumberError.message);
                    }
                }

                // Set metadata for merged PDF
                mergedPdf.setCreator('PDF Editor');
                mergedPdf.setProducer('PDF Editor - Merge Tool');
                mergedPdf.setCreationDate(new Date());
                mergedPdf.setModificationDate(new Date());

                // Save the merged PDF
                const mergedPdfBytes = await mergedPdf.save();
                await fs.writeFile(outputPath, mergedPdfBytes);

                logger.success(`PDFs merged successfully to: ${outputPath} (${totalPages} total pages)`);
                return { success: true, totalPages: totalPages };
            } catch (error) {
                logger.error('Error merging PDFs:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('validate-pdf', async (event, pdfBuffer) => {
            try {
                const PDFLib = require('pdf-lib');

                // Try to load the PDF to validate it
                const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                const pageCount = pdf.getPageCount();

                // Basic validation checks
                if (pageCount === 0) {
                    return false;
                }

                return true;
            } catch (error) {
                logger.warn('PDF validation failed:', error.message);
                return false;
            }
        });

        ipcMain.handle('get-pdf-page-count', async (event, pdfBuffer) => {
            try {
                const PDFLib = require('pdf-lib');
                const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                return pdf.getPageCount();
            } catch (error) {
                logger.error('Error getting PDF page count:', error);
                return 0;
            }
        });

        ipcMain.handle('lock-pdf', async (event, pdfBuffer, outputPath, passwordOptions) => {
            try {
                const PDFLib = require('pdf-lib');
                const CryptoJS = require('crypto-js');
                const fs = require('fs').promises;
                const path = require('path');

                logger.info(`Locking PDF with encryption-based protection`);

                // Convert ArrayBuffer to Buffer if needed
                const pdfBufferData = pdfBuffer instanceof ArrayBuffer ? Buffer.from(pdfBuffer) : pdfBuffer;

                // Encrypt the original PDF data
                const encryptedData = CryptoJS.AES.encrypt(pdfBufferData.toString('base64'), passwordOptions.userPassword).toString();

                // Create a new PDF that contains the encrypted data and instructions
                const newPdf = await PDFLib.PDFDocument.create();
                const page = newPdf.addPage([595, 842]); // A4 size
                const { rgb } = PDFLib;

                // Add title and lock icon
                page.drawText('🔒 ENCRYPTED PDF DOCUMENT', {
                    x: 50,
                    y: 750,
                    size: 24,
                    color: rgb(0.8, 0.2, 0.2)
                });

                // Add security notice
                page.drawText('This document contains encrypted content that requires a password to decrypt.', {
                    x: 50,
                    y: 700,
                    size: 14,
                    color: rgb(0.2, 0.2, 0.2)
                });

                page.drawText('The original PDF has been encrypted using AES encryption.', {
                    x: 50,
                    y: 680,
                    size: 12,
                    color: rgb(0.4, 0.4, 0.4)
                });

                // Add instructions
                page.drawText('To decrypt this document:', {
                    x: 50,
                    y: 640,
                    size: 14,
                    color: rgb(0.2, 0.2, 0.2)
                });

                page.drawText('1. Use the PDF Editor application', {
                    x: 70,
                    y: 620,
                    size: 12,
                    color: rgb(0.3, 0.3, 0.3)
                });

                page.drawText('2. Select "Unlock PDF" feature', {
                    x: 70,
                    y: 600,
                    size: 12,
                    color: rgb(0.3, 0.3, 0.3)
                });

                page.drawText('3. Enter the correct password', {
                    x: 70,
                    y: 580,
                    size: 12,
                    color: rgb(0.3, 0.3, 0.3)
                });

                // Add permissions info
                page.drawText('Document Permissions:', {
                    x: 50,
                    y: 540,
                    size: 14,
                    color: rgb(0.2, 0.2, 0.2)
                });

                const printStatus = passwordOptions.permissions?.printing ? '✓ Allowed' : '✗ Restricted';
                const copyStatus = passwordOptions.permissions?.copying ? '✓ Allowed' : '✗ Restricted';
                const modifyStatus = passwordOptions.permissions?.modifying ? '✓ Allowed' : '✗ Restricted';

                page.drawText(`Printing: ${printStatus}`, {
                    x: 70,
                    y: 520,
                    size: 12,
                    color: passwordOptions.permissions?.printing ? rgb(0.2, 0.7, 0.2) : rgb(0.8, 0.2, 0.2)
                });

                page.drawText(`Text Copying: ${copyStatus}`, {
                    x: 70,
                    y: 500,
                    size: 12,
                    color: passwordOptions.permissions?.copying ? rgb(0.2, 0.7, 0.2) : rgb(0.8, 0.2, 0.2)
                });

                page.drawText(`Modification: ${modifyStatus}`, {
                    x: 70,
                    y: 480,
                    size: 12,
                    color: passwordOptions.permissions?.modifying ? rgb(0.2, 0.7, 0.2) : rgb(0.8, 0.2, 0.2)
                });

                // Add encrypted data (hidden in the PDF)
                page.drawText('ENCRYPTED DATA:', {
                    x: 50,
                    y: 440,
                    size: 10,
                    color: rgb(0.7, 0.7, 0.7)
                });

                // Split encrypted data into chunks and add to PDF (hidden)
                const chunkSize = 80;
                const encryptedChunks = [];
                for (let i = 0; i < encryptedData.length; i += chunkSize) {
                    encryptedChunks.push(encryptedData.substring(i, i + chunkSize));
                }

                let yPos = 420;
                for (let i = 0; i < Math.min(encryptedChunks.length, 20); i++) {
                    page.drawText(encryptedChunks[i], {
                        x: 50,
                        y: yPos,
                        size: 6,
                        color: rgb(0.9, 0.9, 0.9) // Very light gray, barely visible
                    });
                    yPos -= 10;
                }

                // Add footer
                page.drawText(`Protected by PDF Editor - Created: ${new Date().toLocaleString()}`, {
                    x: 50,
                    y: 50,
                    size: 10,
                    color: rgb(0.6, 0.6, 0.6)
                });

                // Set metadata
                newPdf.setTitle('Encrypted PDF Document');
                newPdf.setCreator('PDF Editor - Encryption Tool');
                newPdf.setProducer('PDF Editor - Lock Tool');
                newPdf.setSubject('Password Protected Document');
                newPdf.setKeywords(['encrypted', 'password', 'protected']);

                // Save the encrypted PDF
                const pdfBytes = await newPdf.save();
                await fs.writeFile(outputPath, pdfBytes);

                logger.success(`PDF encrypted successfully to: ${outputPath}`);
                logger.info(`AES encryption applied - original content secured`);

                return {
                    success: true,
                    message: 'PDF has been encrypted with AES encryption. The original content is now secured and can only be accessed with the correct password using the PDF Editor unlock feature.'
                };
            } catch (error) {
                logger.error('Error locking PDF:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('delete-pdf-pages', async (event, pdfBuffer, outputPath, pagesToDelete) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;

                logger.info(`Deleting pages from PDF`);

                // Load the PDF from buffer
                const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                const totalPages = pdf.getPageCount();

                logger.info(`PDF has ${totalPages} pages, deleting: ${pagesToDelete}`);

                // Parse pages to delete
                const deleteIndices = this.parsePageNumbers(pagesToDelete, totalPages);

                // Create new PDF with remaining pages
                const newPdf = await PDFLib.PDFDocument.create();
                const pagesToKeep = [];

                for (let i = 0; i < totalPages; i++) {
                    if (!deleteIndices.includes(i)) {
                        pagesToKeep.push(i);
                    }
                }

                if (pagesToKeep.length === 0) {
                    throw new Error('Cannot delete all pages from PDF');
                }

                const copiedPages = await newPdf.copyPages(pdf, pagesToKeep);
                copiedPages.forEach((page) => newPdf.addPage(page));

                // Save the new PDF
                const newPdfBytes = await newPdf.save();
                await fs.writeFile(outputPath, newPdfBytes);

                logger.success(`Deleted ${deleteIndices.length} pages, saved to: ${outputPath}`);
                return { success: true, deletedPages: deleteIndices.length, remainingPages: pagesToKeep.length };
            } catch (error) {
                logger.error('Error deleting PDF pages:', error);
                return { success: false, error: error.message };
            }
        });

        // Split PDF
        ipcMain.handle('split-pdf', async (event, pdfBuffer, outputPath, options = {}) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;
                const path = require('path');

                logger.info(`Splitting PDF with method: ${options.method}`);

                // Load the PDF
                const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                const pageCount = pdf.getPageCount();

                logger.info(`PDF has ${pageCount} pages`);

                // Extract directory and base name from the output path
                const outputDir = path.dirname(outputPath);
                const outputBaseName = path.basename(outputPath, '.pdf');

                logger.info(`Output directory: ${outputDir}`);
                logger.info(`Base filename: ${outputBaseName}`);

                let splitRanges = [];
                let fileCount = 0;
                let firstFilePath = null;

                // Determine split ranges based on method
                switch (options.method) {
                    case 'pages':
                        splitRanges = this.parsePageRanges(options.pageRanges, pageCount);
                        break;
                    case 'every':
                        splitRanges = this.generateEveryNPagesRanges(pageCount, options.everyNPages);
                        break;
                    case 'extract':
                        splitRanges = this.parsePageRanges(options.extractPages, pageCount);
                        break;
                    default:
                        throw new Error('Invalid split method');
                }

                if (splitRanges.length === 0) {
                    throw new Error('No valid page ranges specified');
                }

                // Create output directory if it doesn't exist
                await fs.mkdir(outputDir, { recursive: true });

                // Create split PDFs
                for (let i = 0; i < splitRanges.length; i++) {
                    const range = splitRanges[i];
                    const newPdf = await PDFLib.PDFDocument.create();

                    // Copy pages in the range
                    const pageIndices = [];
                    for (let pageNum = range.start; pageNum <= range.end; pageNum++) {
                        if (pageNum >= 1 && pageNum <= pageCount) {
                            pageIndices.push(pageNum - 1); // Convert to 0-based index
                        }
                    }

                    if (pageIndices.length > 0) {
                        const copiedPages = await newPdf.copyPages(pdf, pageIndices);
                        copiedPages.forEach(page => newPdf.addPage(page));

                        // Set metadata
                        newPdf.setCreator('PDF Editor');
                        newPdf.setProducer('PDF Editor - Split Tool');
                        newPdf.setCreationDate(new Date());

                        // Generate filename using user-provided base name
                        let filename;
                        if (options.method === 'extract') {
                            filename = `${outputBaseName}_pages_${range.start}-${range.end}.pdf`;
                        } else {
                            filename = `${outputBaseName}_part_${i + 1}_pages_${range.start}-${range.end}.pdf`;
                        }

                        const filePath = path.join(outputDir, filename);

                        // Save the split PDF
                        const pdfBytes = await newPdf.save();
                        await fs.writeFile(filePath, pdfBytes);

                        // Store the first file path for opening
                        if (fileCount === 0) {
                            firstFilePath = filePath;
                        }

                        fileCount++;
                        logger.info(`Created: ${filename} (pages ${range.start}-${range.end})`);
                    }
                }

                logger.success(`PDF split successfully into ${fileCount} files in: ${outputDir}`);
                return {
                    success: true,
                    fileCount: fileCount,
                    outputDir: outputDir,
                    firstFilePath: firstFilePath
                };
            } catch (error) {
                logger.error('Error splitting PDF:', error);
                return { success: false, error: error.message };
            }
        });

        // Helper methods for PDF operations
        this.parsePageRanges = (rangeString, maxPages) => {
            if (!rangeString || !rangeString.trim()) {
                return [];
            }

            const ranges = [];
            const parts = rangeString.split(',').map(s => s.trim());

            for (const part of parts) {
                if (part.includes('-')) {
                    // Range like "1-5"
                    const [start, end] = part.split('-').map(s => parseInt(s.trim()));
                    if (!isNaN(start) && !isNaN(end) && start >= 1 && end <= maxPages && start <= end) {
                        ranges.push({ start, end });
                    }
                } else {
                    // Single page like "3"
                    const page = parseInt(part);
                    if (!isNaN(page) && page >= 1 && page <= maxPages) {
                        ranges.push({ start: page, end: page });
                    }
                }
            }

            return ranges;
        };

        this.generateEveryNPagesRanges = (totalPages, pagesPerFile) => {
            const ranges = [];
            let start = 1;

            while (start <= totalPages) {
                const end = Math.min(start + pagesPerFile - 1, totalPages);
                ranges.push({ start, end });
                start = end + 1;
            }

            return ranges;
        };

        // Create Text PDF
        ipcMain.handle('create-text-pdf', async (event, outputPath, textContent, options = {}) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;

                logger.info(`Creating text PDF with options:`, {
                    pageSize: options.pageSize || 'a4',
                    fontSize: options.fontSize || 12,
                    fontFamily: options.fontFamily || 'Arial',
                    margins: options.margins || 'medium',
                    lineSpacing: options.lineSpacing || '1.15'
                });

                // Create a new PDF document
                const pdf = await PDFLib.PDFDocument.create();

                // Set page size
                let pageSize;
                switch (options.pageSize) {
                    case 'letter':
                        pageSize = PDFLib.PageSizes.Letter;
                        break;
                    case 'legal':
                        pageSize = PDFLib.PageSizes.Legal;
                        break;
                    case 'a4':
                    default:
                        pageSize = PDFLib.PageSizes.A4;
                        break;
                }

                // Set margins
                let margins;
                switch (options.margins) {
                    case 'small':
                        margins = { top: 36, bottom: 36, left: 36, right: 36 }; // 0.5 inch
                        break;
                    case 'large':
                        margins = { top: 108, bottom: 108, left: 108, right: 108 }; // 1.5 inch
                        break;
                    case 'medium':
                    default:
                        margins = { top: 72, bottom: 72, left: 72, right: 72 }; // 1 inch
                        break;
                }

                // Get font
                let font;
                try {
                    switch (options.fontFamily) {
                        case 'Times New Roman':
                            font = await pdf.embedFont(PDFLib.StandardFonts.TimesRoman);
                            break;
                        case 'Helvetica':
                            font = await pdf.embedFont(PDFLib.StandardFonts.Helvetica);
                            break;
                        case 'Courier New':
                            font = await pdf.embedFont(PDFLib.StandardFonts.Courier);
                            break;
                        case 'Arial':
                        default:
                            font = await pdf.embedFont(PDFLib.StandardFonts.Helvetica);
                            break;
                    }
                } catch (fontError) {
                    logger.warn(`Could not embed font ${options.fontFamily}, using default`);
                    font = await pdf.embedFont(PDFLib.StandardFonts.Helvetica);
                }

                const fontSize = options.fontSize || 12;
                const lineSpacing = parseFloat(options.lineSpacing) || 1.15;
                const lineHeight = fontSize * lineSpacing;

                // Calculate text area dimensions
                const textWidth = pageSize[0] - margins.left - margins.right;
                const textHeight = pageSize[1] - margins.top - margins.bottom;

                // Split text into lines that fit the page width
                const lines = this.wrapText(textContent, font, fontSize, textWidth);

                // Calculate how many lines fit per page
                const linesPerPage = Math.floor(textHeight / lineHeight);

                // Create pages and add text
                let currentLine = 0;
                while (currentLine < lines.length) {
                    const page = pdf.addPage(pageSize);

                    let y = pageSize[1] - margins.top - fontSize;
                    let linesOnThisPage = 0;

                    while (currentLine < lines.length && linesOnThisPage < linesPerPage) {
                        page.drawText(lines[currentLine], {
                            x: margins.left,
                            y: y,
                            size: fontSize,
                            font: font,
                            color: PDFLib.rgb(0, 0, 0)
                        });

                        y -= lineHeight;
                        currentLine++;
                        linesOnThisPage++;
                    }
                }

                // Set metadata
                pdf.setTitle('Text Document');
                pdf.setCreator('PDF Editor');
                pdf.setProducer('PDF Editor - Text to PDF Tool');
                pdf.setCreationDate(new Date());
                pdf.setModificationDate(new Date());

                // Save the PDF
                const pdfBytes = await pdf.save();
                await fs.writeFile(outputPath, pdfBytes);

                logger.success(`Text PDF created successfully: ${outputPath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error creating text PDF:', error);
                return { success: false, error: error.message };
            }
        });

        // Helper method to wrap text
        this.wrapText = (text, font, fontSize, maxWidth) => {
            const lines = [];
            const paragraphs = text.split('\n');

            for (const paragraph of paragraphs) {
                if (paragraph.trim() === '') {
                    lines.push(''); // Empty line for paragraph breaks
                    continue;
                }

                const words = paragraph.split(' ');
                let currentLine = '';

                for (const word of words) {
                    const testLine = currentLine ? `${currentLine} ${word}` : word;
                    const testWidth = font.widthOfTextAtSize(testLine, fontSize);

                    if (testWidth <= maxWidth) {
                        currentLine = testLine;
                    } else {
                        if (currentLine) {
                            lines.push(currentLine);
                            currentLine = word;
                        } else {
                            // Word is too long, break it
                            lines.push(word);
                        }
                    }
                }

                if (currentLine) {
                    lines.push(currentLine);
                }
            }

            return lines;
        };
    }



    // Initialize database tables for desktop app
    initializeDesktopTables() {
        try {
            const db = database.getDatabase();

            if (!db) {
                logger.warn('Database not available, skipping desktop table initialization');
                return;
            }

            // Create quiz sessions table if it doesn't exist
            db.exec(`
                CREATE TABLE IF NOT EXISTS quiz_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    question_type TEXT NOT NULL,
                    score_correct INTEGER NOT NULL,
                    score_total INTEGER NOT NULL,
                    duration INTEGER NOT NULL,
                    answers TEXT,
                    questions TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Initialize API key in database if it doesn't exist
            this.initializeApiKeyInDatabase();

            logger.success('Desktop database tables initialized');
        } catch (error) {
            logger.error('Error initializing desktop tables:', error.message);
            // Don't throw error, just log it
        }
    }

    // Helper methods for PDF operations
    parsePageRanges(rangeString, totalPages) {
        const ranges = [];
        const parts = rangeString.split(',').map(part => part.trim());

        for (const part of parts) {
            if (part.includes('-')) {
                const [start, end] = part.split('-').map(num => parseInt(num.trim()));
                if (start >= 1 && end <= totalPages && start <= end) {
                    ranges.push({ start, end });
                }
            } else {
                const pageNum = parseInt(part);
                if (pageNum >= 1 && pageNum <= totalPages) {
                    ranges.push({ start: pageNum, end: pageNum });
                }
            }
        }

        return ranges;
    }

    parsePageNumbers(pageString, totalPages) {
        const pageIndices = [];
        const parts = pageString.split(',').map(part => part.trim());

        for (const part of parts) {
            if (part.includes('-')) {
                const [start, end] = part.split('-').map(num => parseInt(num.trim()));
                if (start >= 1 && end <= totalPages && start <= end) {
                    for (let i = start; i <= end; i++) {
                        pageIndices.push(i - 1); // Convert to 0-based index
                    }
                }
            } else {
                const pageNum = parseInt(part);
                if (pageNum >= 1 && pageNum <= totalPages) {
                    pageIndices.push(pageNum - 1); // Convert to 0-based index
                }
            }
        }

        return [...new Set(pageIndices)]; // Remove duplicates
    }

    // Initialize API key in database from environment
    async initializeApiKeyInDatabase() {
        try {
            const currentApiKey = process.env.API_KEY;

            if (!currentApiKey) {
                logger.warn('No API key found in environment variables');
                return;
            }

            // Check if we already have this key in the database
            const apiService = require('./services/apiService');
            const db = database.getDatabase();

            if (!db) {
                logger.warn('Database not available for API key initialization');
                return;
            }

            // Check if any API keys exist in database
            db.get('SELECT COUNT(*) as count FROM api_keys WHERE is_active = 1', [], async (err, row) => {
                if (err) {
                    logger.error('Error checking API keys in database:', err);
                    return;
                }

                if (row.count === 0) {
                    // No active API keys in database, add the current environment key
                    try {
                        await apiService.addApiKey(currentApiKey);
                        logger.info('Initialized database with current environment API key');
                    } catch (addError) {
                        logger.warn('Could not add environment API key to database:', addError.message);
                    }
                } else {
                    logger.info('Database already has active API keys');
                }
            });

        } catch (error) {
            logger.error('Error initializing API key in database:', error.message);
        }
    }
}

module.exports = IPCHandlers;
