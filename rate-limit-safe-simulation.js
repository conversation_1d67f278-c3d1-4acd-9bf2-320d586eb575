const https = require('https');
const fs = require('fs');

// Load environment
console.log('🔧 Loading environment...');
require('dotenv').config();
console.log('✅ Environment loaded');

console.log('🚀 Starting Rate-Limit Safe Model Simulation...');
console.log('📊 Testing models with LONG delays to avoid rate limits');
console.log('⏱️  30-second delays between tests');
console.log('🎯 Focus: No thinking tags + Best performance\n');

const API_KEY = process.env.API_KEY || process.env.OPENROUTER_API_KEY;
if (!API_KEY) {
    console.error('❌ API_KEY not found in environment');
    process.exit(1);
}
console.log('✅ API Key loaded successfully');

// Test fewer models first to avoid rate limits
const testModels = [
    'mistralai/mistral-nemo:free',
    'qwen/qwen3-32b:free', 
    'google/gemma-3-27b-it:free',
    'mistralai/devstral-small:free',
    'mistralai/mistral-7b-instruct:free',
    'qwen/qwen3-235b-a22b:free',
    'deepseek/deepseek-r1:free',
    'moonshotai/kimi-dev-72b:free'
];

const testContent = `The human heart is a vital organ that pumps blood throughout the body. It consists of four chambers: two atria (upper chambers) and two ventricles (lower chambers). The right atrium receives deoxygenated blood from the body, which then flows to the right ventricle. From the right ventricle, blood is pumped to the lungs for oxygenation.`;

// Make API request with better error handling
function makeAPIRequest(modelId, content) {
    return new Promise((resolve, reject) => {
        const prompt = `Based on the following content, generate exactly 3 true/false questions in JSON format. Generate exactly 2 true questions and 1 false question. For false questions, provide an explanation. Do not include any thinking, reasoning, or analysis - just return clean JSON.

Content: ${content}

Return format:
[
  {
    "question": "Question text here",
    "answer": true
  },
  {
    "question": "Question text here", 
    "answer": false,
    "explanation": "Explanation for false answer"
  }
]`;

        const postData = JSON.stringify({
            model: modelId,
            messages: [
                {
                    role: "user",
                    content: prompt
                }
            ],
            max_tokens: 1000,
            temperature: 0.7
        });

        const options = {
            hostname: 'openrouter.ai',
            port: 443,
            path: '/api/v1/chat/completions',
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData),
                'HTTP-Referer': 'http://localhost:3000',
                'X-Title': 'MCQ TF Generator'
            }
        };

        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    resolve(response);
                } catch (error) {
                    console.log(`    🔍 Raw response: ${data.substring(0, 300)}...`);
                    reject(new Error(`JSON parse error: ${error.message}`));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.setTimeout(60000, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        req.write(postData);
        req.end();
    });
}

// Analyze response with detailed logging
function analyzeResponse(response, modelId, isFirstTest = false) {
    const analysis = {
        success: false,
        hasThinking: false,
        jsonParsed: false,
        questionCount: 0,
        qualityScore: 0,
        errors: [],
        responseTime: 0,
        rawContent: ''
    };

    try {
        // Check for rate limit or error responses
        if (response.error) {
            analysis.errors.push(`API Error: ${response.error.message || 'Unknown error'}`);
            if (response.error.message && response.error.message.includes('rate limit')) {
                analysis.errors.push('RATE LIMITED');
            }
            return analysis;
        }

        if (!response.choices || !response.choices[0] || !response.choices[0].message) {
            analysis.errors.push('Invalid response structure');
            if (isFirstTest) {
                console.log(`    🔍 Full response: ${JSON.stringify(response, null, 2)}`);
            }
            return analysis;
        }

        const content = response.choices[0].message.content;
        analysis.rawContent = content;
        
        // Debug first test
        if (isFirstTest) {
            console.log(`    🔍 Raw content: ${content.substring(0, 400)}...`);
        }
        
        // Check for thinking tags
        const thinkingPatterns = [
            'ΓùüthinkΓû╖',
            '<think>',
            '</think>',
            'thinking:',
            'reasoning:',
            'analysis:',
            'let me think',
            'i need to',
            'first, i'
        ];
        
        analysis.hasThinking = thinkingPatterns.some(pattern => 
            content.toLowerCase().includes(pattern.toLowerCase())
        );

        // Try to parse JSON
        try {
            const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            const questions = JSON.parse(cleanContent);
            
            if (Array.isArray(questions)) {
                analysis.jsonParsed = true;
                analysis.questionCount = questions.length;
                analysis.success = true;
                
                // Calculate quality score
                let score = 0;
                if (analysis.questionCount >= 3) score += 3;
                if (!analysis.hasThinking) score += 3;
                if (analysis.jsonParsed) score += 2;
                
                const hasExplanations = questions.some(q => 
                    q.answer === false && q.explanation && q.explanation.length > 10
                );
                if (hasExplanations) score += 2;
                
                analysis.qualityScore = score;
            }
        } catch (jsonError) {
            analysis.errors.push('JSON parsing failed');
            if (isFirstTest) {
                console.log(`    🔍 JSON Error: ${jsonError.message}`);
            }
        }

    } catch (error) {
        analysis.errors.push(`Analysis error: ${error.message}`);
    }

    return analysis;
}

// Run simulation with longer delays
async function runSimulation() {
    const results = [];
    const DELAY = 30000; // 30 seconds delay to avoid rate limits
    const SIMS_PER_MODEL = 2; // Reduce to 2 simulations per model

    console.log(`⚠️  Using ${DELAY/1000}s delays between tests to avoid rate limits\n`);

    for (let i = 0; i < testModels.length; i++) {
        const modelId = testModels[i];
        console.log(`\n📊 Testing Model ${i + 1}/${testModels.length}: ${modelId}`);
        
        const modelResults = {
            modelId,
            simulations: [],
            avgResponseTime: 0,
            successRate: 0,
            hasThinkingTags: false,
            jsonParseSuccess: 0,
            avgQualityScore: 0,
            rating: 'Unknown'
        };

        for (let sim = 1; sim <= SIMS_PER_MODEL; sim++) {
            console.log(`  🔄 Simulation ${sim}/${SIMS_PER_MODEL}`);
            
            try {
                const startTime = Date.now();
                const response = await makeAPIRequest(modelId, testContent);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                const analysis = analyzeResponse(response, modelId, sim === 1 && i === 0);
                analysis.responseTime = responseTime;
                
                modelResults.simulations.push(analysis);
                
                console.log(`    ⏱️  Response Time: ${responseTime}ms`);
                console.log(`    📝 Questions: ${analysis.questionCount}`);
                console.log(`    🧠 Thinking Tags: ${analysis.hasThinking ? '❌ YES' : '✅ NO'}`);
                console.log(`    📋 JSON Parsed: ${analysis.jsonParsed ? '✅ YES' : '❌ NO'}`);
                console.log(`    ⭐ Quality: ${analysis.qualityScore}/10`);
                
                if (analysis.errors.length > 0) {
                    console.log(`    ⚠️  Errors: ${analysis.errors.join(', ')}`);
                }
                
            } catch (error) {
                console.log(`    ❌ Failed: ${error.message}`);
                modelResults.simulations.push({
                    success: false,
                    hasThinking: false,
                    jsonParsed: false,
                    questionCount: 0,
                    qualityScore: 0,
                    errors: [error.message],
                    responseTime: 0
                });
            }
            
            if (sim < SIMS_PER_MODEL) {
                console.log(`    ⏳ Waiting ${DELAY/1000}s...`);
                await new Promise(resolve => setTimeout(resolve, DELAY));
            }
        }
        
        // Calculate statistics
        const successful = modelResults.simulations.filter(s => s.success);
        modelResults.successRate = Math.round((successful.length / SIMS_PER_MODEL) * 100);
        modelResults.hasThinkingTags = modelResults.simulations.some(s => s.hasThinking);
        modelResults.jsonParseSuccess = Math.round((modelResults.simulations.filter(s => s.jsonParsed).length / SIMS_PER_MODEL) * 100);
        
        if (successful.length > 0) {
            modelResults.avgResponseTime = Math.round(successful.reduce((sum, s) => sum + s.responseTime, 0) / successful.length);
            modelResults.avgQualityScore = Math.round((successful.reduce((sum, s) => sum + s.qualityScore, 0) / successful.length) * 10) / 10;
        }
        
        // Rating
        if (modelResults.successRate >= 90 && !modelResults.hasThinkingTags && modelResults.jsonParseSuccess >= 90) {
            modelResults.rating = '🏆 EXCELLENT';
        } else if (modelResults.successRate >= 70 && !modelResults.hasThinkingTags) {
            modelResults.rating = '⭐ GOOD';
        } else if (modelResults.successRate >= 50) {
            modelResults.rating = '⚠️ AVERAGE';
        } else {
            modelResults.rating = '❌ POOR';
        }
        
        results.push(modelResults);
        
        if (i < testModels.length - 1) {
            console.log(`\n⏳ Waiting ${DELAY/1000}s before next model...`);
            await new Promise(resolve => setTimeout(resolve, DELAY));
        }
    }
    
    // Generate report
    generateReport(results);
    return results;
}

function generateReport(results) {
    console.log('\n' + '='.repeat(80));
    console.log('📊 RATE-LIMIT SAFE SIMULATION RESULTS');
    console.log('='.repeat(80));
    
    // Sort by performance
    const sorted = results.sort((a, b) => {
        if (a.hasThinkingTags !== b.hasThinkingTags) return a.hasThinkingTags ? 1 : -1;
        if (a.successRate !== b.successRate) return b.successRate - a.successRate;
        return a.avgResponseTime - b.avgResponseTime;
    });
    
    console.log('\n🏆 TOP PERFORMERS (No Thinking + High Success):');
    console.log('-'.repeat(60));
    
    const topPerformers = sorted.filter(r => !r.hasThinkingTags && r.successRate >= 50);
    topPerformers.forEach((result, index) => {
        console.log(`${index + 1}. ${result.modelId}`);
        console.log(`   ${result.rating}`);
        console.log(`   Success: ${result.successRate}% | Speed: ${result.avgResponseTime}ms | Quality: ${result.avgQualityScore}/10`);
        console.log('');
    });
    
    console.log('\n❌ MODELS WITH THINKING TAGS:');
    console.log('-'.repeat(60));
    sorted.filter(r => r.hasThinkingTags).forEach(result => {
        console.log(`❌ ${result.modelId} - ${result.rating}`);
    });
    
    console.log('\n🎯 RECOMMENDATIONS:');
    console.log('-'.repeat(60));
    if (topPerformers.length > 0) {
        console.log(`🥇 BEST: ${topPerformers[0].modelId} (${topPerformers[0].avgResponseTime}ms)`);
        if (topPerformers[1]) console.log(`🥈 SECOND: ${topPerformers[1].modelId} (${topPerformers[1].avgResponseTime}ms)`);
        if (topPerformers[2]) console.log(`🥉 THIRD: ${topPerformers[2].modelId} (${topPerformers[2].avgResponseTime}ms)`);
    } else {
        console.log('❌ No models met performance criteria - may need longer delays');
    }
    
    // Save results
    fs.writeFileSync('rate-limit-safe-results.json', JSON.stringify({
        timestamp: new Date().toISOString(),
        results: sorted
    }, null, 2));
    
    console.log('\n💾 Results saved to: rate-limit-safe-results.json');
    console.log('='.repeat(80));
}

// Run the simulation
runSimulation()
    .then(() => {
        console.log('\n✅ Rate-limit safe simulation completed!');
        process.exit(0);
    })
    .catch(error => {
        console.error('\n❌ Simulation failed:', error);
        process.exit(1);
    });
